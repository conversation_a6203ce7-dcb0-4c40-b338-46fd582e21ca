# 代码模式功能测试指南

## 功能描述

新增的代码模式 (CODE) 提供了以下功能：

- 三种模式选择：快速模式 (BASIC)、专业模式 (PROFESSIONAL)、代码模式 (CODE)
- 代码模式包含两个主要部分：
  1. **输入参数** - 参考 ScriptComponent 的输入参数逻辑
  2. **提示词** - 类似快速模式的提示词输入

## 测试步骤

### 1. 基础功能测试

#### 1.1 模式切换测试

1. 进入 Prompt 组件配置页面
2. 查看模式选择区域，应有三个选项：
   - ✅ 快速模式
   - ✅ 专业模式
   - ✅ **代码模式** (新增)
3. 点击"代码模式"，验证界面切换正常

#### 1.2 代码模式界面验证

选择代码模式后，应显示：

1. **输入参数区域**：

   - 标题："输入参数"
   - 参数配置表单（变量名、类型、描述等）
   - "添加"按钮可以增加新的参数
   - 每行都有全局变量选择器
   - 删除功能（保留至少一行）

2. **提示词区域**：
   - 标题："\* 提示词"
   - 大型文本输入框 (高度 200px)
   - 支持变量插入 (输入 $ 触发)
   - AI 优化按钮
   - 必填验证

### 2. 输入参数功能测试

#### 2.1 参数管理

1. **添加参数**：
   - 点击"添加"按钮，应增加新的参数行
   - 每个参数包含：变量名、变量类型、描述、全局变量选择
2. **删除参数**：

   - 当有多个参数时，每行应显示删除按钮
   - 点击删除，对应行被移除
   - 当只有一行时，删除按钮应禁用

3. **参数配置**：
   - 变量名：必填，输入框
   - 变量类型：下拉选择（string, number 等）
   - 描述：可选，文本输入
   - 取值：全局变量选择器，支持流程变量选择

#### 2.2 拖拽排序 (如果支持)

1. 验证参数列表是否支持拖拽重新排序
2. 拖拽后顺序应正确保存

### 3. 提示词功能测试

#### 3.1 基础输入

1. 在提示词文本框中输入代码内容
2. 验证必填校验：提交时如果为空应提示错误
3. 验证字符数限制和格式

#### 3.2 变量插入

1. 输入 `$` 字符，应触发变量选择下拉
2. 选择变量后应正确插入到文本中
3. 变量格式应符合预期（如 `${variableName}`）

#### 3.3 AI 优化

1. 输入一些代码内容
2. 点击"AI 优化"按钮
3. 验证优化功能是否正常工作
4. 优化后内容应替换原内容

### 4. 数据提交测试

#### 4.1 表单验证

1. **必填字段验证**：

   - 组件名：必填
   - 模型类型：必填
   - 提示词：必填
   - 输入参数的变量名：必填

2. **数据格式验证**：
   - 变量类型选择正确
   - 全局变量关联有效

#### 4.2 提交数据结构

验证提交的数据结构应包含：

```json
{
  "promptMode": "CODE",
  "professionalContents": [
    {
      "role": "user",
      "enableMultiModal": false,
      "script": "这里是提示词内容"
    }
  ],
  "inputParams": [
    {
      "variableName": "参数名",
      "variableValueType": "参数类型",
      "variableRequire": false,
      "description": "参数描述",
      "inputValue": "关联的全局变量"
    }
  ]
}
```

#### 4.3 数据恢复测试

1. 配置代码模式数据并保存
2. 刷新页面或重新进入
3. 验证数据是否正确恢复：
   - 模式选择应为"代码模式"
   - 输入参数应完整恢复
   - 提示词内容应正确显示

### 5. 模式切换测试

#### 5.1 数据保留测试

1. 在代码模式下配置数据
2. 切换到其他模式
3. 再切换回代码模式
4. 验证数据是否保留

#### 5.2 清理逻辑测试

1. 在代码模式配置数据
2. 切换到快速模式，验证多模态警告清除
3. 切换到专业模式，验证界面正常

### 6. 集成测试

#### 6.1 与高级设置集成

1. 在代码模式下配置高级设置
2. 验证高级设置参数正确保存和恢复

#### 6.2 与输出参数集成

1. 配置输出参数解析方式
2. 验证输出参数配置正常工作

#### 6.3 调试面板集成

1. 在代码模式下配置完成
2. 验证调试面板显示正确的表单数据
3. 测试调试功能是否正常

### 7. 边界情况测试

#### 7.1 空数据处理

1. 不配置任何输入参数
2. 验证表单提交和数据结构

#### 7.2 大量数据

1. 添加多个输入参数 (10+ 个)
2. 输入长文本提示词
3. 验证性能和界面表现

#### 7.3 特殊字符

1. 在提示词中输入特殊字符、多行文本
2. 在参数名中输入边界值
3. 验证数据处理正确性

## 预期结果

✅ **成功标准**：

- 所有三种模式都能正常工作
- 代码模式的输入参数功能完整
- 提示词功能与快速模式一致
- 数据提交格式正确 (professionalContents.script)
- 数据恢复功能正常
- 与现有功能无冲突

## 已知限制

- 代码模式不支持多模态功能
- 输入参数暂不支持拖拽排序（可后续优化）
- AI 优化功能依赖后端服务可用性

## 报告问题格式

如发现问题，请按以下格式报告：

```
**问题描述**:
**复现步骤**:
**预期结果**:
**实际结果**:
**截图**: (如有)
```
