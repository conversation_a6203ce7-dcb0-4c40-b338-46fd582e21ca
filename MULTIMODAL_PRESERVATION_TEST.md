# 多模态配置保留功能测试指南

## 功能描述

当用户在多模态模型下配置了图片/视频开关后，切换到非多模态模型时：

- 保持原有的多模态配置不变
- 显示常驻的红色警告提示
- 多模态项目显示为禁用状态（不可操作）
- 只允许删除操作
- 当模型类型切换回多模态后，原来的多模态配置重新可用
- **重要**：提交表单时，禁用的多模态配置将被过滤，不会提交给后端接口

## 测试步骤

### 1. 准备测试环境

1. 启动项目：`npm run dev`
2. 导航到 Prompt 组件配置页面
3. 切换到 "专业模式"

### 2. 配置多模态内容

1. 选择一个支持多模态的模型（如 GPT-4V）
2. 在专业模式下添加一条消息
3. 打开该消息的 "图片/视频" 开关
4. 配置模态类型和流程变量
5. 可以添加多个图片输入项

### 3. 测试模型切换

1. 切换到非多模态模型（如 GPT-3.5）
2. **预期结果**：
   - 顶部显示红色警告："模型已切换成纯文本模式，多模态不可以使用"
   - 警告下方显示："注意：提交表单时，已禁用的多模态配置将不会被保存到后端"
   - 启用了多模态的消息项显示为禁用状态
   - 消息类型下拉框变为灰色不可点击
   - 图片/视频开关显示"不可用"并禁用
   - 多模态配置区域显示红色警告框："当前模型不支持多模态，此配置已保留但提交时将被跳过"
   - 所有输入框和下拉框变为禁用状态
   - 只有删除按钮仍然可用
   - "添加新模态"按钮隐藏

### 4. 测试配置保留

1. 切换回多模态模型（如 GPT-4V）
2. **预期结果**：
   - 红色警告消失
   - 之前配置的多模态内容全部恢复
   - 所有配置项重新可用
   - 原有的模态类型和变量选择保持不变

### 5. 测试模式切换

1. 在专业模式下配置多模态后切换到快速模式
2. **预期结果**：多模态警告自动清除
3. 切换回专业模式，多模态配置仍然保留

### 6. 测试提交过滤功能

1. 在多模态模型下配置一些多模态消息和普通文本消息
2. 切换到非多模态模型，确保出现警告
3. 尝试提交表单（保存配置）
4. **预期结果**：
   - 表单可以正常提交
   - 控制台会显示"跳过禁用的多模态消息"的日志
   - 只有普通文本消息和系统消息被提交给后端
   - 禁用的多模态配置被完全过滤掉
5. 切换回多模态模型，原有配置仍然保留在前端界面中

## 关键验证点

1. **数据保留**：切换模型时不会丢失任何多模态配置数据
2. **状态管理**：警告状态在合适的时机显示和隐藏
3. **交互控制**：在禁用状态下只允许删除操作
4. **提交过滤**：禁用的多模态配置在提交时被正确过滤，不会发送给后端
5. **用户体验**：提供清晰的视觉反馈告知用户当前状态和提交行为

## 边界情况测试

1. 多个启用多模态的消息项
2. 混合内容（部分启用多模态，部分未启用）
3. 快速切换多个模型类型
4. 在禁用状态下尝试各种操作

## 问题排查

如果功能不正常，检查：

1. `multiModalWarning` 状态是否正确设置
2. `hasMultiModal` 判断是否准确
3. `isMultiModalDisabled` 条件是否正确
4. Form.List 的数据是否被意外清除
