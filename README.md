```
1 初始化项目
• 1.1 使用Vite新建项目
• 1.2 安装并运行项目
• 1.3 精简项目
2 Vite基础配置
• 2.1 配置国内镜像源
• 2.2 支持Sass/Scss/Less/Stylus
• 2.3 设置dev环境的Server端口号
• 2.4 设置dev环境自动打开浏览器
• 2.5 设置路径别名
3 项目架构搭建
• 3.1 项目目录结构设计
• 3.2 关于样式命名规范
• 3.3 设置全局公用样式
4 引入Ant Design 5.x
• 4.1 安装Ant Design
• 4.2 设置Antd为中文语言
5 页面开发
• 5.1 构建Login页面
• 5.2 构建Home页面
• 5.3 构建Account页面
• 5.4 通过一级路由实现页面跳转
• 5.5 在React组件中实现页面路由跳转
• 5.6 在非React组件中实现页面路由跳转
6 组件开发
• 6.1 创建自定义SVG图标Icon组
• 6.2 创建Header组件
• 6.3 引入Header组件
• 6.4 在Header组件中添加页面导航示
• 6.5 组件传参
7 二级路由配置
• 7.1 创建二级路由的框架页面
• 7.2 配置二级路由
8 React Developer Tools浏览器插件
9 Redux及Redux Toolkit
• 9.1 安装Redux及Redux Toolkit
• 9.2 创建全局配置文件
• 9.3 创建用于主题换肤的store分库
• 9.4 创建store总库
• 9.5 引入store到项目
• 9.6 store的使用：实现亮色/暗色主题切换
• 9.7 非Ant Design组件的主题换肤
• 9.8 store的使用：实现主题色切换
• 9.9 安装Redux调试浏览器插件
10 基于axios封装公用API库
• 10.1 安装axios
• 10.2 封装公用API库
• 10.3 Mock.js安装与使用
• 10.4 发起API请求：实现登录功能
11 一些细节问题
• 11.1 解决Modal.method跟随主题换肤的问题
• 11.2 路由守卫
• 11.3 设置开发环境的反向代理请求
• 11.4 兼容js文件
• 11.5 允许dev环境的IP访问
• 11.6 批量升级全部项目npm依赖包
12 build项目
• 12.1 设置静态资源引用路径
• 12.2 设置build目录名称及静态资源存放目录
• 12.3 开启build项目生成map文件（不推荐）
• 12.4 执行build项目
```
