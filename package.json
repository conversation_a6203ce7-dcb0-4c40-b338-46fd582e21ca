{"name": "vite-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "cross-env REACT_APP_BUILD_TIME=$(date \"+%s\") vite build", "lint": "eslint src --ext js,jsx --fix --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,less}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,less}\""}, "lint-staged": {"src/**/*.{jsx,ts,tsx,json,css,scss,md}": ["npm run lint"]}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.5.1", "@ant-design/plots": "^1.2.5", "@ant-design/pro-components": "2.7.1", "@ant-design/x": "^1.1.0", "@antv/xflow": "^1.0.55", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/view": "^6.37.2", "@dagrejs/dagre": "^1.1.4", "@ebay/nice-modal-react": "^1.2.13", "@emotion/react": "^11.13.5", "@jridgewell/gen-mapping": "^0.3.5", "@loadable/component": "^5.16.4", "@microsoft/fetch-event-source": "^2.0.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/highlight": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@reduxjs/toolkit": "^1.9.5", "@replit/codemirror-indentation-markers": "^6.5.3", "@tanstack/react-query": "^4.29.25", "@tanstack/react-query-devtools": "^4.29.25", "@uiw/codemirror-theme-andromeda": "^4.23.5", "@uiw/codemirror-themes": "^4.23.13", "@uiw/react-codemirror": "^4.21.20", "@uiw/react-markdown-preview": "^5.1.3", "@uiw/react-md-editor": "^4.0.5", "allotment": "^1.20.4", "antd": "5.23.1", "axios": "^1.4.0", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "form-render": "^2.3.5", "github-markdown-css": "5.4.0", "highlight.js": "^11.8.0", "insert-css": "^2.0.0", "js-file-download": "^0.4.12", "lodash": "^4.17.21", "lz-string": "^1.5.0", "markdown-it": "^13.0.2", "markdown-it-for-inline": "^0.1.1", "marked": "^7.0.3", "mermaid": "^11.3.0", "mockjs": "^1.1.0", "moment": "^2.29.4", "pdfjs-dist": "3.4.120", "prop-types": "^15.8.1", "query-string": "^8.1.0", "quill": "^1.3.7", "quill-cursors": "^4.0.2", "quill-emoji": "^0.2.0", "quill-image-resize-module-react": "^3.0.0", "rc-animate": "^3.1.1", "rc-banner-anim": "^2.4.5", "rc-queue-anim": "^2.0.0", "rc-scroll-anim": "^2.7.6", "rc-texty": "^0.2.0", "rc-tween-one": "^3.0.6", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.5.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-highlight-words": "^0.20.0", "react-image-crop": "^10.1.5", "react-intersection-observer": "^9.16.0", "react-json-view": "^1.21.3", "react-loadable": "^5.5.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-redux": "^8.0.7", "react-router-dom": "^6.11.2", "react-transition-group": "^4.4.5", "react-zoom-pan-pinch": "^3.6.1", "reactflow": "^11.11.4", "rehype-rewrite": "^4.0.2", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "table-render": "^2.1.3", "textarea-caret": "^3.1.0", "uuid": "^9.0.0", "zustand": "^4.3.9"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/react": "^18.2.8", "@types/react-dom": "^18.2.4", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "less": "^4.1.3", "lint-staged": "^13.2.3", "postcss": "^8.4.26", "prettier": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.62.1", "stylus": "^0.59.0", "tailwindcss": "^3.3.3", "vite": "^4.3.9", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.3.0"}, "resolutions": {"commander": "11.0.0", "marked": "7.0.3", "antd": "5.23.1"}}