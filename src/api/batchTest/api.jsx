// api.jsx
import { Get, Post, Put } from "@/api/server"
import { botPrefix } from "@/constants"
import { downloadFileWithHeaders } from "../tools"

// BATCHTEST-创建测试集
export const createTestSet = (params) => {
  const { botNo, skillNo, skillVersionNo, ...res } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/create/${skillVersionNo}`, {
    ...res
  }).then((res) => res)
}

// 调试测试集 /bots/{botNo}/{skillNo}/test-set/debug
export const debugTestSet = (params) => {
  const { botNo, skillNo, ...restData } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/debug`, restData).then((res) => res)
}

// BATCHTEST-删除测试集
export const deleteTestSet = (params) => {
  const { botNo, skillNo, id } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/delete/${id}`).then((res) => res)
}

// BATCHTEST-获取单个测试集测试结果
export const fetchTestSetDetail = (params) => {
  const { botNo, skillNo, id } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/detail/${id}`, {
    pageNum: 1,
    pageSize: 5000
  }).then((res) => res.data || {})
}

// BATCHTEST-下载测试集模板
export const downloadTestSetTemplate = (params, body) => {
  const { botNo, skillNo, skillVersionNo } = params
  downloadFileWithHeaders(
    `${botPrefix}/bots/${botNo}/${skillNo}/test-set/download/${skillVersionNo}`,
    "测试集模板.xlsx",
    "Post",
    body
  )
}

// BATCHTEST-获取测试集列表
export const fetchTestSetPage = (params) => {
  const { botNo, skillNo, ...queryParams } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/page`, queryParams).then(
    (res) => res.data
  )
}

// BATCHTEST-上传测试集数据
export const uploadTestSetData = (params) => {
  const { botNo, skillNo, skillVersionNo } = params
  return `${botPrefix}/bots/${botNo}/${skillNo}/test-set/${skillVersionNo}/upload`
}

// 导出测试集excel
export const exportTestSetExcel = (params) => {
  const { botNo, skillNo, skillVersionNo, id } = params
  downloadFileWithHeaders(
    `${botPrefix}/bots/${botNo}/${skillNo}/test-set/export-excel/${skillVersionNo}/${id}`,
    `${skillVersionNo}结果集.xlsx`,
    "Get"
  )
}

//获取测试集输出schema
export const fetchTestSetOutputSchema = (params) => {
  const { botNo, skillNo, skillVersionNo } = params
  return Post(
    `${botPrefix}/bots/${botNo}/${skillNo}/test-set/output-schema/${skillVersionNo}`
  ).then((res) => res.data)
}

// 再次运行测试集
export const runTestSetAgain = (params) => {
  const { botNo, skillNo, id, skillVersionNo } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/run/${id}/${skillVersionNo}`).then(
    (res) => res
  )
}

// skillVersion信息列表
export const fetchSkillVersionList = (params) => {
  const { botNo, skillNo } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/skill-version-list`).then(
    (res) => res.data
  )
}

// 测试集取消运行
export const cancelTestSet = (params) => {
  const { botNo, skillNo, id } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/cancel/${id}`).then((res) => res)
}

// 续跑测试集
// /bots/{botNo}/{skillNo}/test-set/continue/{id}
export const continueTestSet = (params) => {
  const { botNo, skillNo, id } = params
  return Post(`${botPrefix}/bots/${botNo}/${skillNo}/test-set/continue/${id}`).then((res) => res)
}

export const fetchTagList = (params) => {
  return Get(`${botPrefix}/admin/tag/list`, params).then((res) => res.data)
}

export const fetchAdminRoles = () => {
  return Get(`${botPrefix}/admin/authRole/listAdminRoles`).then((res) => res.data)
}

export const fetchBotAuthRole = (params) => {
  const { botNo } = params
  return Get(`${botPrefix}/admin/bot/${botNo}/authRole`).then((res) => res.data)
}
