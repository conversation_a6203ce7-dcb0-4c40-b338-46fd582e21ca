// index.jsx
import { useMutation, useQuery } from "@tanstack/react-query"
import {
  createTestSet,
  debugTestSet,
  deleteTestSet,
  fetchTestSetDetail,
  fetchTestSetOutputSchema,
  fetchTestSetPage,
  fetchSkillVersionList,
  runTestSetAgain,
  cancelTestSet,
  continueTestSet,
  fetchAdminRoles,
  fetchBotAuthRole,
  fetchTagList
} from "./api"
import { QUERY_KEYS } from "@/constants/queryKeys"

// 创建测试集
export const useCreateTestSet = () => {
  return useMutation(createTestSet)
}

export const useDebugTestSet = () => {
  return useMutation(debugTestSet)
}

// 删除测试集
export const useDeleteTestSet = () => {
  return useMutation(deleteTestSet)
}

// 获取单个测试集测试结果
export const useFetchTestSetDetail = (params) => {
  return useQuery([QUERY_KEYS.TEST_SET_DETAIL, params.id], () => fetchTestSetDetail(params), {
    enabled: !!params.id
  })
}

// 获取测试集
export const useFetchTestSetPage = (params) => {
  return useQuery([QUERY_KEYS.TEST_SET_PAGE, params.pageSize, params.pageNum], () =>
    fetchTestSetPage(params)
  )
}

// 获取测试集输出schema
export const useFetchTestSetSchema = (params) => {
  return useQuery(
    [QUERY_KEYS.TEST_SET_SCHEMA, params.skillVersionNo],
    () => fetchTestSetOutputSchema(params),
    {
      enabled: !!params.skillVersionNo
    }
  )
}

export const useFetchSkillVersionList = (params) => {
  return useQuery([QUERY_KEYS.TEST_SKILL_VERSION_LIST], () => fetchSkillVersionList(params), {
    enabled: !!params.skillNo
  })
}

export const useRunTestSetAgain = () => {
  return useMutation(runTestSetAgain)
}

// 取消测试集
export const useCancelTestSet = () => {
  return useMutation(cancelTestSet)
}

// 续跑测试集
export const useContinueTestSet = () => {
  return useMutation(continueTestSet)
}

// 角色-查询用户拥有的管理员角色列表
export const useFetchAdminRoles = () => {
  return useQuery([QUERY_KEYS.ADMIN_ROLES], () => fetchAdminRoles())
}

// 角色-根据机器人编号查询角色列表
export const useFetchBotAuthRole = () => {
  // 使用mutation
  return useMutation(fetchBotAuthRole)
}

export const useFetchTagList = (params) => {
  return useQuery([QUERY_KEYS.TAG_LIST], () => fetchTagList(params))
}
