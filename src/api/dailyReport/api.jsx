import { Get, Post } from "@/api/server"
import { botPrefix } from "@/constants"

// 获取调用日志概览
export const fetchCallLogsOverview = (params) => {
  return Get(`${botPrefix}/admin/dailyReports/overview`, params).then((res) => res.data)
}

// 获取技能调用情况
export const fetchSkillOverview = (params) => {
  return Get(`${botPrefix}/admin/dailyReports/${params.proportionCode}`, params).then(
    (res) => res.data
  )
}

// 获取调用top榜
export const fetchTopOverview = (params) => {
  return Get(`${botPrefix}/admin/dailyReports/${params.dimensionCode}`, params).then(
    (res) => res.data
  )
}

// 获取调用趋势
export const fetchCallTrendOverview = (params) => {
  return Get(`${botPrefix}/admin/dailyReports/skillCallsTrend`, params).then((res) => res.data)
}

// 获取聚合周期范围
export const fetchMetricCyclesList = (params) => {
  return Get(`${botPrefix}/admin/dailyReports/metricCycles`, params).then((res) => res.data)
}

/**
 * 获取机器人列表
 * @param {*} params
 * @returns
 */
export const fetchBotListByTagNo = (params) => {
  return Get(`${botPrefix}/admin/bot/listRestricted`, params).then((res) => res.data)
}

/**
 * 获取机器人使用概览
 * @param {*} params
 * @returns
 */
export const fetchBotUsageOverview = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/overview`, params).then((res) => res.data)
}

// 获取技能调用次数趋势
export const fetchSkillCallsTrend = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/skill-calls-trend`, params).then(
    (res) => res.data
  )
}

// 获取模型Token调用量趋势
export const fetchModelTotalUsageTrend = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/model-total-usage-trend`, params).then(
    (res) => res.data
  )
}

// 获取各模型调用Token量趋势
export const fetchPerModelUsageTrend = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/per-model-usage-trend`, params).then(
    (res) => res.data
  )
}

// 获取模型调用Token量明细
export const fetchModelUsageDetails = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/page-model-usage`, params).then(
    (res) => res.data
  )
}

// 获取模型调用总Token占比
export const fetchModelUsagePieChart = (params) => {
  return Post(`${botPrefix}/admin/report/v2/bot-usage/pie-chart-of-per-model-usage`, params).then(
    (res) => res.data
  )
}
