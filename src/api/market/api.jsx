/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-03-16 15:40:30
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-05-22 17:53:43
 * @FilePath: /za-aigc-platform-admin-static/src/api/market/api.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
import { Get, Post } from "@/api/server"
import { botPrefix } from "@/constants"
import { infoInterceptors } from "../tools"

/**
 * @description: 市集首页，根据类型查询各组别内容
 * @params {*} botNo
 * @params {*} query
 * @params {*} bizType
 * @return {*}
 */
export const fetchMarketHome = (params) => {
  const { botNo } = params
  const url = `${botPrefix}/admin/bot/${botNo}/page-subscribable-resource`
  return Get(url, params).then((res) => infoInterceptors(res)?.data)
}

/**
 * @description: 取消订阅
 * @params {*} botNo
 * @params {*} query
 * @params {*} bizType
 * @return {*}
 */
export const fetchCancelSubscribe = (params) => {
  const { botNo } = params
  const url = `${botPrefix}/admin/bot/${botNo}/cancel-subscribe`
  return Post(url, { ...params }).then((res) => res)
}

/**
 * @description: 订阅
 * @params {*} botNo
 * @params {*} query
 * @params {*} bizType
 * @return {*}
 */
export const fetchSubscribe = (params) => {
  const { botNo } = params
  const url = `${botPrefix}/admin/bot/${botNo}/subscribe`
  return Post(url, { ...params }).then((res) => res)
}

/**
 * @description: 查询可订阅资源列表
 * @params {*} query
 * @params {*} bizType
 * @return {*}
 */
export const fetchSubscribableResources = (params) => {
  const url = `${botPrefix}/admin/bot/page-subscribable-resource`
  return Get(url, params).then((res) => infoInterceptors(res)?.data)
}

/**
 * @description: 获取机器人列表
 * @return {*}
 */
export const fetchBotList = () => {
  const url = `/api/proxy/bots`
  return Get(url).then((res) => infoInterceptors(res)?.data)
}
