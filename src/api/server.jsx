/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-03-12 11:20:14
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-03-21 17:10:38
 * @FilePath: /za-aigc-platform-admin-static/src/api/server.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
import axios from "axios"
import { message } from "antd"
import { SSOReqHeader, SSOLogIn } from "@/api/sso"
import queryString from "query-string"

// 开发环境地址
// let API_DOMAIN = "/api/"
// if (process.env.NODE_ENV === "production") {
//   // 正式环境地址
//   API_DOMAIN = "http://xxxxx/api/",
// }

const searchParams = queryString.parse(window.location.search) || {}
const hashParams = queryString.parse(window.location.hash.split("?")[1] || "") || {}
const { token, workbenchNo, botNo } = searchParams.token ? searchParams : hashParams

// Handle request and response interceptors
axios.interceptors.request.use((config) => {
  // 写入 header
  // @ts-ignore
  config.headers =
    config?.url.includes("nsso.") ||
    config?.url.includes("za-uc.") ||
    config?.url.includes("za-uat-uc.")
      ? {}
      : {
          BotNo: botNo || undefined,
          WorkbenchNo: workbenchNo || undefined,
          IsIframe: token ? true : false
        }
  // 合并sso headers
  Object.assign(config.headers, SSOReqHeader(config))
  return config
})

axios.interceptors.response.use(
  (response) => {
    if (response?.config?.responseType === "blob") {
      // 对于blob响应，直接返回整个response
      return response
    }
    // if (response.status === 200) {
    //   window.location.hash = "#/404"
    // }
    if (response.status !== 200) {
      message.warning(response.data?.message || response.data?.msg)
    }
    if (response.data?.code === 401) {
      SSOLogIn()
    }

    // Return response data directly
    return response.data
  },
  (error) => {
    if (axios.isCancel(error)) {
      console.log("Request cancelled", error.message)
    } else if (error.response) {
      switch (error.response.status) {
        case 401:
          // if (process.env.NODE_ENV !== "development") {
          //   window.location.href = "/?fail=2"
          // }
          SSOLogIn()
          break
        case 403:
          // 无权限跳转
          window.location.hash = "#/404"
          message.warning("当前页面暂无权限！")
          // message.warning("403 Forbidden")
          break
        case 404:
          break
        default:
          message.error("服务器错误")
          break
      }
    }
    return Promise.reject(error)
  }
)

export const Get = (url, params = {}, config = {}) => {
  return axios({
    url,
    method: "get",
    params,
    ...config
  })
}

export const Post = (url, data, params = {}, cancelToken) => {
  return axios.post(url, data, { params, cancelToken })
}

export const Put = (url, data, params = {}, cancelToken) => {
  return axios.put(url, data, { params, cancelToken })
}

export const Delete = (url, params = {}, cancelToken) => {
  return axios.delete(url, { params, cancelToken })
}

export const Patch = (url, data, params = {}, cancelToken) => {
  return axios.patch(url, data, { params, cancelToken })
}

export const Upload = (url, data, params = {}, cancelToken) => {
  return axios.post(url, data, { ...params, cancelToken })
}
