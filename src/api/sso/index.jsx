import { fetchSessionByTicket, fetchUserInfoBySession } from "./api"
import queryString from "query-string"
import { message } from "antd"

/**
 * @description: SSO接入规约
 *
 * @return {*} 浏览器与系统间授信使用<PERSON>ie,系统间的授信方式使用Header。将sessionID和serviceName传至下游系统。使用统一sdk对sessionID进行鉴权。传递的cookie或header如下：
 *  @Cookie
 *  ATLANTIS_SESSION_ID : {session-id}//ticket置换的唯一性id
 *  ATLANTIS_SERVICE_NAME : {serviceName} //申请到的登录serviceName
 * @Header {*}:
 * X-Usercenter-Session : {session-id}//ticket置换的唯一性id
 * X-Service-Name : {serviceName}//申请到的登录serviceName
 * @客户端保存Session的方式为本地Cookie,CookieName约定如下
 * Cookie-Name : ATLANTIS_SESSION_ID
 */

/**
 * @description: 外部系统接入灵犀后管iframe 规则
 * @parmas {*}  token: ssoSessionId
 * @parmas {*}  servicename: ssoSessionId配对SSO ServiceName
 * @example https://aigc-admin.zhonganonline.com/?token=encode(sessionId_xxxx)&serviceName=xxx#/url?xxx=xxx
 */

/**
 * @description: ssoServicename
 * @return {*} 默认za-open-bot
 */

const searchParams = queryString.parse(window.location.search) || {}
const hashParams = queryString.parse(window.location.hash.split("?")[1] || "") || {}
const { token, serviceName, servicename } = searchParams.token ? searchParams : hashParams

console.log("==searchParams==", searchParams)
console.log("==hashParams==", hashParams)

export const getTokenAndServiceName = () => {
  if (serviceName && token) {
    return { token, serviceName: serviceName ?? servicename ?? "" }
  }
  return {
    token: localStorage.getItem("ATLANTIS_SESSION_ID") || "",
    serviceName: "za-open-bot"
  }
}

// 根据当前host换取对应用户中心的url
// 测试环境地址：http://usercenter-server.test.za-tech.net
// 公共环境内网地址：http://usercenter-server.pub.za-tech.net
// 科技办公网地址：https://nsso.zhonganinfo.com
// 保险办公网地址：https://nsso.zhongan.com
export const userCenterHost = () => {
  return getSsoHost()
}

/**
 * 是否国际地址
 * @returns
 */
export function isIntl() {
  if (/\.in\.za$/.test(window.location.hostname)) {
    return true
  }
  return false
}

function isInsure() {
  const hostname = window.location.hostname
  const localhost = ["127.0.0.1", "localhost"]
  return (
    hostname.endsWith(".za.biz") ||
    hostname.endsWith(".zhonganonline.com") ||
    localhost.includes(hostname)
  )
}

function isZhongAn() {
  const hostname = window.location.hostname
  return hostname.endsWith(".zhongan.com")
}

// /**
//  * 获取sso主机
//  * @returns
//  */
// export function getSsoHost() {
//   const isInternational = isIntl()
//   return isInternational
//     ? `https://za-uc.in.za`
//     : `https://nsso.zhonganinfo.com`
// }

/**
 * 获取sso主机
 * @returns
 */
export function getSsoHost() {
  const isInternational = isIntl()
  const currentEnv = getEnv()
  return isInternational
    ? getIntlSsoHost(currentEnv)
    : isInsure()
      ? "https://nsso.zhonganinfo.com"
      : `https://nsso.zhongan.io`
}

/**
 * 通过环境获取国际sso登录域名
 * @param env string
 * @returns host string
 */
function getIntlSsoHost(env = "dev") {
  const intlSsoHost = {
    dev: "https://za-dev-uc.in.za",
    sit: "https://za-sit-uc.in.za",
    uat: "https://za-uat-uc.in.za",
    prd: "https://za-uc.in.za"
  }
  return intlSsoHost[env]
}

/**
 * 获取环境
 * @returns {string} env
 */
export function getEnv() {
  const str = location.hostname
  // 国际域名规则
  // 匹配 'aigc-admin-xxx.in.za' 格式的字符串
  const regex = /^aigc-admin-(\w+)\.in\.za$/
  const match = str.match(regex)
  if (match) {
    return match[1]
  }
  const prdENv = ["aigc-admin.in.za", "aigc-admin.zhonganonline.com", "aigc-admin.zhongan.com"]
  if (prdENv.includes(str)) {
    return "prd"
  }
  const testEnv = ["aigc-admin-test.zhonganonline.com", "aigc-admin-test.zhongan.com"]
  if (testEnv.includes(str)) {
    return "test"
  }
  const preEnv = ["aigc-admin-pre.zhonganonline.com", "aigc-admin-pre.zhongan.com"]
  if (preEnv.includes(str)) {
    return "pre"
  }
  // 保险域名规则
  const insureRegex = /^za-aigc-platform-admin-static\.(\w+)\.za\.biz$/
  const insureMatch = str.match(insureRegex)
  if (insureMatch) {
    return insureMatch[1]
  } else {
    return "dev"
  }
}

/**
 * 获取应用地址
 * @param {string} env 环境字符串
 * @returns {string} url 应用端地址
 */
export function getAppUrl(env = getEnv()) {
  const isInternational = isIntl()
  const isZhongAnCom = isZhongAn()
  const intlUrls = {
    dev: "https://aigc-dev.in.za",
    sit: "https://aigc-sit.in.za",
    uat: "https://aigc-uat.in.za",
    prd: "https://aigc.in.za"
  }
  const insureUrls = {
    dev: "https://aigc-test.zhonganonline.com",
    test: "https://aigc-test.zhonganonline.com",
    pre: "https://aigc-pre.zhonganonline.com",
    prd: "https://aigc.zhonganonline.com"
  }

  const zhongAnUrls = {
    dev: "https://aigc-portal-test.zhongan.com",
    test: "https://aigc-portal-test.zhongan.com",
    pre: "https://aigc-portal-pre.zhongan.com",
    prd: "https://aigc-portal.zhongan.com"
  }
  if (isInternational) {
    return intlUrls[env]
  }
  if (isZhongAnCom) {
    return zhongAnUrls[env]
  }
  return insureUrls[env]
}

// 创建Target地址
const createTarget = () => {
  const params = new URLSearchParams(window.location.search)
  params.delete("ticket")
  const hashParamsString = window.location.hash?.split("?")[1]
  const searchParamsString = params.toString()
  let urlParams = ""
  if (hashParamsString && searchParamsString) {
    urlParams = `?${hashParamsString}&${searchParamsString}`
  } else if (hashParamsString || searchParamsString) {
    hashParamsString && (urlParams = `?${hashParamsString}`)
    searchParamsString && (urlParams = `?${searchParamsString}`)
  }
  const targetUrl = `${window.location.origin}${window.location.pathname}${window.location.hash?.split("?")[0] || ""}${urlParams}`
  return encodeURIComponent(targetUrl)
}

// sso登录
export const SSOLogIn = () => {
  // window.localStorage.clear()
  window.localStorage.removeItem("ATLANTIS_SESSION_ID")
  const url = `${userCenterHost()}/login?service=${
    getTokenAndServiceName().serviceName
  }&target=${createTarget()}`
  window.location.href = url
}

// sso登出
export const SSOLogOut = () => {
  window.localStorage.clear()
  // console.log(`${userCenterHost()}/logout?target=${createTarget()}`)
  window.location.href = `${userCenterHost()}/logout?target=${createTarget()}`
}

// 设置请求头部
export const SSOReqHeader = (config) => {
  // 所有的请求头都需要添加 X-Service-Name，设置为当前应用服务名称
  const headers = {
    "X-Service-Name": getTokenAndServiceName().serviceName //申请得到的sso service
  }
  // 验证接口validate2以外都接口都需添加 X-Usercenter-Session
  if (config && config?.url !== "/validate2") {
    headers["X-Usercenter-Session"] = getTokenAndServiceName().token
  }
  if (config?.url?.includes("openapi")) {
    headers["access-channel"] = getTokenAndServiceName().token
    headers["access-key"] = getTokenAndServiceName().token
  }

  return headers
}

// 获取ticket
export const SSOGetTicket = () => {
  const { ticket } = queryString.parse(window.location.search)
  if (!ticket) {
    SSOLogIn()
    return false
  }
  return ticket
}

// ticket置换session接口
// 如用户中心已经登录，客户端调用login接口将会重定向至携带ticket参数的客户端回调地址。客户端需要使用ticket置换session信息。
export const FetchSessionByTicket = (callback) => {
  if (SSOGetTicket()) {
    fetchSessionByTicket({
      service: getTokenAndServiceName().serviceName,
      ticket: SSOGetTicket()
    })
      .then((data) => {
        if (data) {
          const { result, success } = data
          if (!success) {
            message.error(data.message)
            SSOLogIn()
          } else {
            window.localStorage.setItem("ATLANTIS_SESSION_ID", result)
            FetchUserInfoBySession(result, callback)
          }
        }
      })
      .catch((e) => {
        console.log(e)
      })
  }
}
// sessionID置换用户信息接口
// 如客户端从request Cookie中获取到了当前应用的SessionID,可通过该接口换取用户信息。
export const FetchUserInfoBySession = (sid, callback) => {
  let session_id

  // 首先尝试从哈希中获取session_id
  const hash = window.location.hash
  if (hash.includes("?")) {
    const hashParams = new URLSearchParams(hash.split("?")[1])
    console.log(hashParams)
    const rawSessionId = hashParams.get("session_id")
    console.log(rawSessionId)
    session_id = rawSessionId ? encodeURIComponent(rawSessionId) : null
  }

  // 如果哈希中没有session_id，则尝试从标准的查询参数中获取
  if (!session_id) {
    const params = new URLSearchParams(window.location.search)
    session_id = params.get("session_id")
  }

  console.log("Session ID:", session_id)

  if (session_id) {
    // 如果URL中有session_id，则使用它
    localStorage.setItem("ATLANTIS_SESSION_ID", session_id)
  }

  // 然后尝试获取加密的Session
  const encryptedSession = sid ?? getTokenAndServiceName().token
  if (!encryptedSession) {
    // 如果没有加密的Session，则尝试获取Ticket
    return FetchSessionByTicket(callback)
  }

  // 以下是原有的逻辑
  fetchUserInfoBySession({
    service: getTokenAndServiceName().serviceName,
    encryptedSession
  }).then((data) => {
    if (data) {
      const { result, success } = data
      if (!success) {
        message.error(data.message)
        FetchSessionByTicket(callback)
      } else {
        if (callback) callback(result)
      }
    }
  })
}
