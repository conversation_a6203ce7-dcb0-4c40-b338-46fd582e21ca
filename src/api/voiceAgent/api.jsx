// api.js
import { Get, Post } from "@/api/server"
import { voiceAgentPrefix } from "@/constants"
import { PAGE_CODE } from "@/constants/pageCode"

const fix = "/api/v1"

// 获取语音 agent 列表
export const getVoiceAgentList = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/page`, params)
}

// 创建或更新语音模板
export const createOrUpdateVoiceAgent = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/createOrUpdate`, params)
}

// 获取语音模板详情
export const getVoiceAgentDetail = (params) => {
  return Get(`${voiceAgentPrefix}${fix}/nlu/task-config/detail`, params)
}

// 获取语音模板详情（支持agentNo参数）
export const getVoiceAgentDetails = (params) => {
  return Get(`${voiceAgentPrefix}${fix}/nlu/task-config/detail`, params)
}

// 更新语音模板状态（启用/停用）
export const updateVoiceAgentStatus = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/statusUpdate`, params)
}

// 复制语音模板
export const copyVoiceAgent = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/copy`, params)
}

// 删除语音模板
export const deleteVoiceAgent = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/del`, params)
}

// 获取画布历史节点
export const getFlowConfigDetails = (params) => {
  return Get(`${voiceAgentPrefix}${fix}/nlu/flow-config/detail`, params)
}

// 保存画布配置
export const saveFlowConfig = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/flow-config/save`, params)
}

// 创建或更新节点配置
export const createOrUpdateNodeConfig = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/node-config/createOrUpdate`, params)
}

// 已选择话术列表
export const getScriptList = (params) => {
  return Get(`${voiceAgentPrefix}${fix}/nlu/script-config/selected/list`, params)
}

// 获取事件列表
export const getEventList = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/event-config/page`, params)
}

// 创建或更新事件
export const createOrUpdateEvent = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/event-config/createOrUpdate`, params)
}

// 删除事件
export const deleteEvent = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/event-config/del`, params)
}

// 获取意图筛选列表
export const getIntentionList = (params) => {
  return Get(`${voiceAgentPrefix}${fix}/nlu/intention-config/list`, params)
}

// 保存新增意图
export const saveIntentionConfig = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/intention-config/save`, params)
}

// 获取话术列表（分页）
export const getScriptListByPage = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/script-config/page`, params)
}

// 保存选中的话术
export const saveSelectedScripts = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/script-config/selected/save`, params)
}

// 获取音色列表
export const getTimbreList = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/timbre-config/page`, params)
}

// 创建或更新音色
export const createOrUpdateTimbre = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/timbre-config/createOrUpdate`, params)
}

// 删除音色
export const deleteTimbre = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/timbre-config/del`, params)
}

// 语音合成
export const synthesisVoice = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/timbre-config/synthesis`, params)
}

// 保存话术类型
export const saveScriptType = (params, queryParams) => {
  // 构建查询参数字符串
  const { botNo, taskId, taskScriptId, scriptId, intentionType, faqCode } = queryParams
  let url = `${voiceAgentPrefix}${fix}/nlu/script-config/selected/script/save?botNo=${botNo}&taskId=${taskId}&taskScriptId=${taskScriptId}&scriptId=${scriptId}&intentionType=${intentionType}`

  // 如果有 faqCode 则添加到查询字符串
  if (faqCode) {
    url += `&faqCode=${faqCode}`
  }

  // 直接传递params，允许为null
  return Post(url, params)
}

// 删除已选择话术
export const deleteSelectedScript = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/script-config/selected/del`, params)
}

// 创建或更新话术
export const createOrUpdateScript = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/script-config/createOrUpdate`, params)
}

// 删除话术
export const deleteScript = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/script-config/del`, params)
}

// 更新agent和语音模板的映射关系
export const updateAgentMapping = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/agent/mapping/update`, params)
}

// 更新语音模板类型
export const updateFlowType = (params) => {
  return Post(`${voiceAgentPrefix}${fix}/nlu/task-config/flowTypeUpdate`, params)
}
