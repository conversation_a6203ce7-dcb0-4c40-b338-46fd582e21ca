.ai-optimize-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .anticon {
    color: #1890ff;
    font-size: 16px;
  }
}
.ai-optimize-button {
  display: flex;
  cursor: pointer;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 100px;
  background: linear-gradient(84deg, #e9e8ff 6.73%, #eec7ff 131.73%);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  color: var(--primary-color, #7f56d9);
  &:hover {
    opacity: 0.8;
  }

  &.cursor-not-allowed {
    &:hover {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.ai-optimize-content {
  padding: 5px 0;

  .ant-input {
    border-radius: 4px;
    resize: none;
    font-size: 14px;

    &::placeholder {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
.ai-optimize-modal {
  .ant-modal-content {
    padding: 0px;
  }
  .ant-modal-header {
    padding: 12px 16px;
    justify-content: space-between;
    align-items: center;
    background: var(---, #f5f7fa);
    margin-bottom: 0px;
  }
  .ant-modal-body {
    padding: 16px;
  }
}
