.animated-dropdown-trigger {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.animated-dropdown-trigger:hover {
  color: #1890ff;
}

.animated-dropdown-menu {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* transform: translateY(-50%); 向上移动半个菜单的高度 */
}

.animated-dropdown-menu .ant-dropdown-menu {
  min-width: 118px;
}

.animated-dropdown-menu .ant-dropdown-menu-item {
  padding: 8px 12px;
  transition: background-color 0.3s ease;
  text-align: center;
  white-space: nowrap;
}

.animated-dropdown-menu .ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
}

/* 动画效果 */
.animated-dropdown-menu-enter,
.animated-dropdown-menu-appear {
  opacity: 0;
  transform: translateY(calc(-50% - 10px));
}

.animated-dropdown-menu-enter-active,
.animated-dropdown-menu-appear-active {
  opacity: 1;
  transform: translateY(-50%);
  transition: opacity 300ms, transform 300ms;
}

.animated-dropdown-menu-exit {
  opacity: 1;
  transform: translateY(-50%);
}

.animated-dropdown-menu-exit-active {
  opacity: 0;
  transform: translateY(calc(-50% - 10px));
  transition: opacity 300ms, transform 300ms;
}
