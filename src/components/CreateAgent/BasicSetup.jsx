import { Form, Input, Select, Upload, message } from "antd"
import { PlusOutlined } from "@ant-design/icons"
import { fetchUploadFile } from "@/api/common/api"
import { CameraOutlined } from "@ant-design/icons"
import { AGENT_MODE_OPTIONS } from "./index"
import "./index.scss"

const BasicSetup = ({ initialValues, imageUrl, setImageUrl }) => {
  const handleChange = async (info) => {
    if (info.file.status === "uploading") {
      return
    }

    if (info.file.status === "done") {
      try {
        const formData = new FormData()
        formData.append("file", info.file.originFileObj)
        const data = await fetchUploadFile(formData)
        const { temporarySignatureUrl } = data || {}
        if (temporarySignatureUrl) {
          setImageUrl(temporarySignatureUrl)
        }
      } catch (error) {
        console.error("Error uploading file:", error)
        message.error("上传失败")
      }
    }
  }

  const beforeUpload = (file) => {
    const isValidType = ["image/png", "image/jpg", "image/jpeg", "image/gif"].includes(file.type)
    if (!isValidType) {
      message.error("只支持上传 PNG/JPG/JPEG/GIF 格式的图片!")
    }
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      message.error("图片大小不能超过 2MB!")
    }
    return isValidType && isLt2M
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 4, fontSize: "12px" }}>上传</div>
    </div>
  )
  return (
    <>
      <Form.Item
        label="Agent 名称"
        name="agentName"
        rules={[{ required: true, message: "请输入Agent名称" }]}
      >
        <Input placeholder="请输入Agent名称" maxLength={50} />
      </Form.Item>

      {initialValues && (
        <Form.Item
          label="Agent 编号"
          name="agentNo"
          rules={[{ required: true, message: "请输入Agent 编号" }]}
        >
          <Input disabled={true} placeholder="请输入Agent 编号" maxLength={50} />
        </Form.Item>
      )}
      <Form.Item
        label="Agent 类型"
        name="agentMode"
        rules={[{ required: true, message: "请选择Agent类型" }]}
      >
        <Select
          disabled={!!initialValues}
          placeholder="请选择Agent类型"
          options={AGENT_MODE_OPTIONS}
        />
      </Form.Item>

      <Form.Item label="描述" name="description">
        <Input.TextArea
          placeholder="请输入描述"
          maxLength={200}
          autoSize={{ minRows: 3, maxRows: 5 }}
        />
      </Form.Item>

      <Form.Item
        label="图标"
        name="icon"
        className="small-upload-card"
        rules={[{ required: true, message: "请上传图标" }]}
      >
        <Upload
          name="file"
          listType="picture-card"
          showUploadList={false}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          customRequest={({ file, onSuccess }) => {
            setTimeout(() => {
              onSuccess("ok")
            }, 0)
          }}
        >
          {imageUrl ? (
            <img
              src={imageUrl}
              alt="avatar"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover"
              }}
            />
          ) : (
            uploadButton
          )}
          <div
            className="absolute -bottom-[5px] left-[40px] bg-[#7F56D9] text-white rounded-full w-[24px] h-[24px] flex items-center justify-center"
            style={{ border: "1px solid #fff" }}
          >
            <CameraOutlined />
          </div>
        </Upload>
      </Form.Item>
    </>
  )
}

export default BasicSetup
