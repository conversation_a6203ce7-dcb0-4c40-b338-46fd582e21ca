import { useState, useEffect, useRef } from "react"
import { Modal, Form, Tabs, Tooltip } from "antd"
import { useAddAgent, useUpdateAgentNew } from "@/api/agent"
import { generateNO } from "@/utils"
import defaultAgentIcon from "../../assets/img/agentAvater-new.png?url"
import BasicSetup from "./BasicSetup"
import ShareSettingForm from "@/components/SkillModal/ShareSettingForm"
import { useQueryClient } from "@tanstack/react-query"
import "./index.scss"

const defaultImageUrl = defaultAgentIcon
// "http://za-aigc-platform-test.oss-cn-hzjbp-b-internal.aliyuncs.com/ubveafukuibky.png?Expires=http://za-aigc-platform-prd.oss-cn-hzjbp-b-internal.aliyuncs.com/ubvegygocccke.png?Expires=1748055095&OSSAccessKeyId=LTAI5tJDRSw9xTCEV3678AHj&Signature=ZTSv%2BkTiwbs2luTnELKNGLI2GeU%3D&OSSAccessKeyId=LTAI5tMugme8cECmAEPFEd1r&Signature=JYbz9s%2F9wzECvx2SSvpXRgh47W4%3D"

export const AGENT_MODE_OPTIONS = [
  { label: "标准Agent", value: 1 },
  { label: "语音Agent", value: 2 },
  { label: "保险文字客服Agent", value: 4 },
  { label: "材料分类Agent", value: 3 },
  { label: "材料智能采集Agent", value: 5 }
]

const CreateAgent = ({ visible, onClose, currentBotNo, initialValues }) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [imageUrl, setImageUrl] = useState(defaultImageUrl)
  const [tabsActiveKey, setTabsActiveKey] = useState("1")
  const { mutate: addAgent } = useAddAgent()
  const { mutate: updateAgent } = useUpdateAgentNew()
  const queryClient = useQueryClient()
  const shareFormRef = useRef(null)

  // 初始化表单数据
  useEffect(() => {
    if (!visible) {
      form.resetFields()
      return
    }
    if (initialValues) {
      form.setFieldsValue({
        agentName: initialValues.agentName,
        agentNo: initialValues?.agentNo,
        agentMode: initialValues.agentMode,
        description: initialValues.description
      })
      shareFormRef?.current?.init?.()
      setImageUrl(initialValues.icon || defaultImageUrl)
    } else {
      setImageUrl(defaultImageUrl)
    }
  }, [visible, initialValues, form])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)
      if (initialValues) {
        // 更新模式
        updateAgent(
          {
            ...values,
            botNo: currentBotNo,
            agentNo: initialValues.agentNo,
            icon: imageUrl
          },
          {
            onSettled: () => {
              setLoading(false)
            },
            onSuccess: (res) => {
              if (res.success) {
                onClose()
              }
            }
          }
        )
        shareFormRef?.current?.onFinish?.(values)
      } else {
        // 创建模式
        addAgent(
          {
            ...values,
            botNo: currentBotNo,
            agentNo: generateNO("a"),
            icon: imageUrl
          },
          {
            onSettled: () => {
              setLoading(false)
            },
            onSuccess: (res) => {
              if (res.success) {
                form.resetFields()
                setImageUrl(defaultAgentIcon)
                onClose()
              }
            }
          }
        )
      }
    } catch (error) {
      console.log("error:", error)
      setLoading(false)
      const errorFieldsName = error.errorFields.reduce((pre, cur) => {
        return [...pre, ...cur.name]
      }, [])
      if (
        errorFieldsName.includes("agentName") ||
        errorFieldsName.includes("agentMode") ||
        errorFieldsName.includes("agentNo")
      ) {
        setTabsActiveKey("1")
      } else {
        setTabsActiveKey("2")
      }
    }
  }

  return (
    <Modal
      title={initialValues ? "编辑 Agent" : "创建 Agent"}
      open={visible}
      onCancel={onClose}
      onOk={handleSubmit}
      confirmLoading={loading}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        labelCol={{
          span: tabsActiveKey === "1" ? 6 : 5
        }}
        initialValues={{
          agentName: "",
          description: "",
          agentMode: 1,
          icon: defaultAgentIcon
        }}
      >
        <Tabs type="card" activeKey={tabsActiveKey} onChange={(val) => setTabsActiveKey(val)}>
          <Tabs.TabPane key="1" tab="基础设置" forceRender>
            <BasicSetup
              initialValues={initialValues}
              imageUrl={imageUrl}
              setImageUrl={setImageUrl}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            key="2"
            tab={
              initialValues?.agentNo && initialValues?.status !== "released" ? (
                <Tooltip title="该Agent未发布，暂不可共享">共享设置</Tooltip>
              ) : (
                "共享设置"
              )
            }
            forceRender
            disabled={!initialValues?.agentNo || initialValues?.status !== "released"}
          >
            <ShareSettingForm
              ref={shareFormRef}
              form={form}
              bizType="AGENT"
              botNo={currentBotNo}
              skillNo={initialValues?.agentNo}
            />
          </Tabs.TabPane>
        </Tabs>
      </Form>
    </Modal>
  )
}

export default CreateAgent
