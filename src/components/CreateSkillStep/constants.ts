export const customTemplateList = [
  {
    id: 1,
    name: "快速问答",
    iconUrl:
      "https://alicdn.zaticdn.com/zaip/zaip-toolweb-file-service/upload/rxdVEgA6ka2VFVm7RgvHHj-chatIcon.png",
    description:
      "可配置Bot基本聊天功能的返回规则，配置聊天Skill后，可在Bot基本配置-聊天模型中选择该Skill",
    skillType: "1",
    __custom: true
  },
  {
    id: 2,
    name: "表单类型",
    iconUrl:
      "https://cdn.zaticdn.com/if/zaip-toolweb-seagull-httpsvr/if/2023-08-28/cllus5he400p62gpe235kfhio_%E5%B0%8F%E7%8A%80%E5%87%BA%E9%A2%98.png",
    description:
      "可自定义单个或多个输入项和输出项,在聊天框上方展示Skill按钮入口, 以表单形式输入,以机器人回复内容为输出",
    skillType: "2",
    __custom: true
  },
  {
    id: 3,
    name: "API类型",
    iconUrl:
      "https://alicdn.zaticdn.com/zaip/zaip-toolweb-file-service/upload/29HzoihzLxm2trdVbSVv7U-api.png",
    description: "将需要执行的技能配置为可调用的OpenAPI，自定义输入和输出项，供外部进行调用",
    skillType: "3",
    __custom: true
  },
  {
    id: 4,
    name: "智能创建",
    iconUrl:
      "https://alicdn.zaticdn.com/zaip/zaip-toolweb-file-service/upload/29HzoihzLxm2trdVbSVv7U-api.png",
    description: "使用自然语言快速创建API技能，支持通过代码编辑",
    skillType: "3",
    __custom: true
  },
  {
    id: 5,
    name: "dify技能",
    iconUrl:
      "https://alicdn.zaticdn.com/zaip/zaip-toolweb-file-service/upload/29HzoihzLxm2trdVbSVv7U-api.png",
    description: "创建该API类型后可在平台内使用或被其他灵犀技能调用",
    skillType: "3",
    __custom: true
  }
]
