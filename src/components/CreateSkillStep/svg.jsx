export const Svg1 = (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path
      d="M18.7025 1.7998H5.29735C4.33195 1.7998 3.54895 2.5828 3.54895 3.5482V4.1308C3.54895 5.0962 4.33195 5.8792 5.29735 5.8792H18.7025C19.6679 5.8792 20.4509 6.6622 20.4509 7.6276V6.4618C20.4509 5.4964 19.6679 4.7134 18.7025 4.7134H5.29735C4.97575 4.7134 4.71475 4.4524 4.71475 4.1308V3.5482C4.71475 3.2266 4.97575 2.9656 5.29735 2.9656H18.7025C19.6685 2.9656 20.4509 3.7486 20.4509 4.714V3.5482C20.4509 2.5822 19.6679 1.7998 18.7025 1.7998Z"
      fill="#FFA300"
    />
    <path
      d="M18.7025 5.87975H5.29735C4.33195 5.87975 3.54895 5.09675 3.54895 4.13135V20.4513C3.54895 21.4167 4.33195 22.1997 5.29735 22.1997H18.7025C19.6679 22.1997 20.4509 21.4167 20.4509 20.4513V7.62814C20.4509 6.66214 19.6679 5.87975 18.7025 5.87975ZM6.69895 9.34774C6.69895 9.01654 6.96775 8.74774 7.29895 8.74774H16.7009C17.0321 8.74774 17.3009 9.01654 17.3009 9.34774V11.5833C17.3009 11.9145 17.0321 12.1833 16.7009 12.1833H7.29895C6.96775 12.1833 6.69895 11.9145 6.69895 11.5833V9.34774Z"
      fill="#FFC33A"
    />
    <path
      d="M18.7027 2.96593H5.29757C4.97597 2.96593 4.71497 3.22693 4.71497 3.54853V4.13113C4.71497 4.45333 4.97597 4.71373 5.29757 4.71373H18.7027C19.6687 4.71373 20.4511 5.49673 20.4511 6.46213V4.71373C20.4511 3.74833 19.6681 2.96593 18.7027 2.96593Z"
      fill="#E5E8EB"
    />
    <path
      d="M16.7009 8.74805H7.29891C6.96754 8.74805 6.69891 9.01668 6.69891 9.34805V11.5836C6.69891 11.915 6.96754 12.1836 7.29891 12.1836H16.7009C17.0323 12.1836 17.3009 11.915 17.3009 11.5836V9.34805C17.3009 9.01668 17.0323 8.74805 16.7009 8.74805Z"
      fill="white"
    />
  </svg>
)

export const Svg2 = (
  <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
    <path
      d="M11 0.692871C5.20099 0.692871 0.5 4.90127 0.5 10.0925C0.5 12.1649 1.2554 14.0759 2.5268 15.6287C2.9924 16.1975 3.2054 16.9313 3.065 17.6525L2.492 20.5906C2.396 21.0838 2.912 21.4696 3.3578 21.2374L6.84559 19.4218C7.33819 19.1656 7.90519 19.0822 8.44699 19.2052C9.26419 19.3906 10.1186 19.492 11 19.492C16.799 19.492 21.5 15.2837 21.5 10.0925C21.5 4.90127 16.799 0.692871 11 0.692871Z"
      fill="#64A7FF"
    />
    <path
      d="M6.65239 9.21777C7.30159 9.21777 7.82719 9.74397 7.82719 10.3926C7.82719 10.704 7.70359 11.0028 7.48279 11.2236C7.26259 11.4438 6.96379 11.568 6.65179 11.568C6.00259 11.568 5.47699 11.0418 5.47699 10.3932C5.47699 9.74457 6.00319 9.21837 6.65179 9.21837L6.65239 9.21777Z"
      fill="white"
    />
    <path
      d="M15.3476 11.5674C15.9964 11.5674 16.5224 11.0414 16.5224 10.3926C16.5224 9.74375 15.9964 9.21777 15.3476 9.21777C14.6988 9.21777 14.1728 9.74375 14.1728 10.3926C14.1728 11.0414 14.6988 11.5674 15.3476 11.5674Z"
      fill="white"
    />
    <path
      d="M10.9999 11.5674C11.6488 11.5674 12.1747 11.0414 12.1747 10.3926C12.1747 9.74375 11.6488 9.21777 10.9999 9.21777C10.3511 9.21777 9.82513 9.74375 9.82513 10.3926C9.82513 11.0414 10.3511 11.5674 10.9999 11.5674Z"
      fill="white"
    />
  </svg>
)

export const Svg3 = (
  <svg xmlns="http://www.w3.org/2000/svg" width="23" height="20" viewBox="0 0 23 20" fill="none">
    <path
      d="M22.3392 12.723C22.3392 10.6835 21.1787 8.82418 19.3449 7.93132C16.4453 6.51946 12.2435 4.47823 10.5315 3.66937C9.53147 3.19687 8.346 3.4172 7.57853 4.21368L3.05561 8.90756C2.59269 9.38798 2.33405 10.0292 2.33405 10.6963V14.5559C2.33405 16.6916 4.06531 18.4228 6.20094 18.4228L17.0171 18.4044C19.9579 18.3994 22.3392 16.0141 22.3392 13.0732L22.3392 12.723Z"
      fill="#FFB582"
    />
    <path d="M11.7139 11.7922V9.63135L7.85895 13.4863L11.7139 11.7922Z" fill="#EC6700" />
    <path
      d="M18.937 1.64057L17.9438 0.647119C17.717 0.420361 17.3493 0.420361 17.1225 0.647119L1.11485 16.655C1.04184 16.728 0.989317 16.819 0.962587 16.9187L0.471026 18.753C0.41243 18.9717 0.612566 19.1719 0.831265 19.1132L2.66554 18.6218C2.76526 18.595 2.85623 18.5425 2.92925 18.4695L3.78266 17.6161C3.63299 17.4215 3.51434 17.1998 3.4437 16.9524C3.16896 15.9897 3.65251 14.9728 4.57265 14.5782L8.25819 13.1405L18.937 2.46176C19.1638 2.23501 19.1638 1.86733 18.937 1.64057Z"
      fill="#4B596A"
    />
    <path
      d="M3.44381 16.952C3.51445 17.1995 3.6331 17.4212 3.78277 17.6157C4.17883 18.1306 4.81142 18.4367 5.491 18.3907L13.8852 18.3409L12.9335 11.3164L8.25831 13.1402L4.57277 14.5779C3.65262 14.9724 3.16907 15.9893 3.44381 16.952Z"
      fill="#FFB582"
    />
  </svg>
)

// 配图的SVG
export const Illustration = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none">
      <path
        d="M3.00008 1.66669C2.2637 1.66669 1.66675 2.26364 1.66675 3.00002V13C1.66675 13.7364 2.2637 14.3334 3.00008 14.3334H13.0001C13.7365 14.3334 14.3334 13.7364 14.3334 13V3.00002C14.3334 2.26364 13.7365 1.66669 13.0001 1.66669H3.00008ZM3.00008 3.00002H13.0001V13H3.00008V3.00002ZM6.33342 4.33335H4.33341V6.33335H6.33342V4.33335ZM11.6667 11.6667V6.39054L8.06268 9.99462L6.83342 8.41414L4.30366 11.6667H11.6667Z"
        fill="#7F56D9"
      />
    </svg>
  )
}
// 改写的SVG
export const RewriteIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none">
      <path
        d="M11.1047 1.26227L14.4045 4.5621L5.01687 13.9497H1.71704V10.6499L11.1047 1.26227ZM3.05037 11.2022V12.6164H4.46459L10.869 6.21201L9.45478 4.7978L3.05037 11.2022ZM10.3976 3.85499L11.8118 5.26921L12.5189 4.5621L11.1047 3.14789L10.3976 3.85499Z"
        fill="#7F56D9"
      />
    </svg>
  )
}

// 续写的SVG
export const ContinueIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none">
      <path
        d="M1.33325 2.33331H14.6666V13.6666H1.33325V2.33331ZM2.66659 3.66665V5.66665H4.66658L4.66658 3.66665H2.66659ZM5.99992 3.66665L5.99992 5.66665H13.3333V3.66665H5.99992ZM13.3333 6.99998H5.99992V8.99998H13.3333V6.99998ZM13.3333 10.3333H5.99992L5.99992 12.3333H13.3333V10.3333ZM4.66658 12.3333L4.66658 10.3333H2.66659V12.3333H4.66658ZM2.66659 8.99998H4.66658V6.99998H2.66659V8.99998Z"
        fill="#7F56D9"
      />
    </svg>
  )
}

// 翻译的SVG
export const TranslateIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none">
      <path
        d="M10.3332 2.99997L6.61304 2.99997L6.32022 1.53589L5.01278 1.79738L5.2533 2.99997L1.6665 2.99997V4.3333L3.16499 4.3333C3.25371 4.61342 3.38524 4.99105 3.56453 5.4129C3.87316 6.13909 4.34065 7.03816 5.00555 7.79184C4.40435 8.24705 3.73472 8.66046 3.14711 8.99167C2.7413 9.2204 2.38313 9.40521 2.12703 9.53253C1.99911 9.59613 1.89701 9.64521 1.82757 9.67808C1.79286 9.69451 1.76634 9.70688 1.74886 9.71497L1.72956 9.72387L1.72437 9.72624C1.72437 9.72624 1.72448 9.72619 1.99992 10.3333C2.27535 10.9404 2.27639 10.9399 2.27639 10.9399L2.27835 10.939L2.28504 10.936L2.30914 10.9249C2.32987 10.9153 2.35979 10.9013 2.39804 10.8832C2.4745 10.847 2.58435 10.7942 2.72061 10.7265C2.99287 10.5911 3.37197 10.3955 3.80181 10.1532C4.46732 9.77807 5.27573 9.27935 5.99988 8.7084C6.72403 9.27935 7.53243 9.77807 8.19795 10.1532C8.62778 10.3955 9.00688 10.5911 9.27915 10.7265C9.31329 10.7434 9.34578 10.7595 9.37649 10.7745L9.11099 11.4096L8.047 14.0872L9.28609 14.5796L9.91373 13.0001H12.7525L13.3804 14.5797L14.6194 14.0872L13.5595 11.4205L11.9903 7.66675H10.6759L9.89181 9.54201C9.88551 9.53889 9.87915 9.53573 9.87272 9.53253C9.61662 9.40521 9.25846 9.2204 8.85264 8.99166C8.26504 8.66046 7.5954 8.24705 6.99421 7.79184C7.6591 7.03816 8.1266 6.13909 8.43522 5.41291C8.61452 4.99105 8.74604 4.61342 8.83477 4.3333L10.3332 4.3333V2.99997ZM7.20812 4.89138C6.9243 5.55919 6.52406 6.30756 5.99988 6.90359C5.4757 6.30756 5.07546 5.55919 4.79164 4.89138C4.70688 4.69195 4.63405 4.50343 4.57261 4.3333L7.42715 4.3333C7.36571 4.50343 7.29288 4.69195 7.20812 4.89138ZM12.2173 11.6667H10.4488L11.3331 9.55152L12.2173 11.6667Z"
        fill="#7F56D9"
      />
    </svg>
  )
}

export const InputIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g>
        <path
          d="M8.71638 2.86566C8.76915 2.89609 8.81329 2.93946 8.84463 2.9917C8.87597 3.04393 8.89347 3.10329 8.89548 3.16417V4.11939C8.88886 4.19122 8.90408 4.26336 8.93914 4.32639C8.9742 4.38943 9.02747 4.4404 9.09199 4.47266C9.1565 4.50492 9.22924 4.51695 9.3007 4.50718C9.37217 4.49741 9.43901 4.46629 9.4925 4.4179L10.2089 3.88059H10.5671L11.4626 4.1791C11.5295 4.21102 11.6046 4.22144 11.6776 4.20891C11.7506 4.19638 11.8179 4.16153 11.8703 4.10915C11.9227 4.05677 11.9575 3.98944 11.9701 3.91643C11.9826 3.84342 11.9722 3.76833 11.9403 3.70148L11.5821 2.74626C11.5592 2.68665 11.5526 2.62201 11.5631 2.55903C11.5736 2.49605 11.6008 2.43702 11.6418 2.38805L12.1791 1.67163C12.2275 1.61815 12.2586 1.5513 12.2683 1.47984C12.2781 1.40838 12.2661 1.33563 12.2338 1.27112C12.2016 1.20661 12.1506 1.15334 12.0876 1.11828C12.0245 1.08322 11.9524 1.068 11.8806 1.07462H10.985C10.9242 1.07261 10.8648 1.05511 10.8126 1.02377C10.7603 0.992427 10.717 0.948285 10.6865 0.895514L10.0895 0.179096C10.0517 0.117667 9.99626 0.0690792 9.93039 0.0396907C9.86452 0.0103021 9.79132 0.00148183 9.72035 0.0143841C9.64939 0.0272864 9.58398 0.0613103 9.53267 0.112003C9.48136 0.162696 9.44655 0.227697 9.4328 0.298499L9.19399 1.13432C9.17689 1.19069 9.14615 1.24198 9.1045 1.28363C9.06284 1.32529 9.01156 1.35603 8.95519 1.37313L8.05966 1.67163C7.96466 1.6558 7.86726 1.67835 7.78888 1.73433C7.71051 1.79032 7.65759 1.87514 7.64175 1.97014C7.62592 2.06514 7.64847 2.16255 7.70445 2.24092C7.76043 2.31929 7.84526 2.37222 7.94026 2.38805L8.71638 2.86566ZM7.64175 6.68656L7.22384 7.04477C7.20357 7.07505 7.19049 7.10958 7.18562 7.1457C7.18074 7.18181 7.1842 7.21857 7.19573 7.25314C7.20725 7.28772 7.22654 7.3192 7.25211 7.34517C7.27768 7.37114 7.30885 7.39091 7.34325 7.40298L7.88056 7.52238L8.05966 7.64178L8.29847 8.11939C8.31912 8.15672 8.34939 8.18783 8.38614 8.2095C8.42289 8.23116 8.46477 8.24259 8.50742 8.24259C8.55008 8.24259 8.59196 8.23116 8.62871 8.2095C8.66546 8.18783 8.69573 8.15672 8.71638 8.11939L8.95519 7.64178L9.13429 7.52238H9.6716C9.706 7.51032 9.73717 7.49054 9.76274 7.46457C9.78831 7.4386 9.8076 7.40712 9.81912 7.37255C9.83065 7.33797 9.83411 7.30122 9.82923 7.2651C9.82436 7.22898 9.81128 7.19446 9.79101 7.16417L9.4328 6.80596C9.4112 6.78173 9.39476 6.75335 9.3845 6.72256C9.37423 6.69176 9.37035 6.6592 9.3731 6.62686L9.4925 6.08954C9.49368 6.05246 9.48621 6.01562 9.47067 5.98193C9.45514 5.94824 9.43196 5.91863 9.40299 5.89545C9.37402 5.87228 9.34005 5.85617 9.30378 5.84841C9.2675 5.84065 9.22991 5.84144 9.19399 5.85074L8.71638 6.08954H8.53727L7.99996 5.73133C7.96404 5.72204 7.92646 5.72124 7.89018 5.729C7.8539 5.73677 7.81993 5.75287 7.79096 5.77605C7.76199 5.79923 7.73882 5.82883 7.72328 5.86252C7.70774 5.89621 7.70027 5.93306 7.70145 5.97014V6.50745C7.7042 6.5398 7.70032 6.57236 7.69005 6.60316C7.67979 6.63395 7.66335 6.66233 7.64175 6.68656ZM4.77608 4.4179C4.80678 4.44853 4.82855 4.48695 4.83907 4.52902C4.84959 4.57109 4.84845 4.61523 4.83578 4.65671L4.71638 5.25372C4.69145 5.30202 4.68374 5.35738 4.69453 5.41065C4.70531 5.46392 4.73395 5.51192 4.7757 5.54671C4.81746 5.58151 4.86983 5.60102 4.92418 5.60202C4.97852 5.60303 5.03158 5.58546 5.07459 5.55223L5.6716 5.31342H5.91041L6.44772 5.67163C6.47939 5.72705 6.53178 5.76762 6.59335 5.78441C6.65493 5.80121 6.72066 5.79285 6.77608 5.76119C6.8315 5.72952 6.87207 5.67713 6.88886 5.61555C6.90566 5.55397 6.8973 5.48825 6.86563 5.43283V4.77611C6.85949 4.72894 6.86757 4.68101 6.88884 4.63846C6.91012 4.59591 6.94361 4.56069 6.98504 4.5373L7.46265 4.11939C7.49401 4.11155 7.52352 4.09761 7.54949 4.07837C7.57546 4.05912 7.59739 4.03495 7.61402 4.00723C7.63065 3.97952 7.64166 3.94879 7.64642 3.91682C7.65118 3.88485 7.64959 3.85225 7.64175 3.82089C7.63391 3.78953 7.61997 3.76002 7.60073 3.73404C7.58148 3.70807 7.55731 3.68614 7.52959 3.66951C7.50187 3.65288 7.47115 3.64187 7.43918 3.63711C7.4072 3.63236 7.37461 3.63394 7.34325 3.64178L6.62683 3.52238L6.44772 3.34328L6.20892 2.74626C6.21683 2.68293 6.19927 2.61904 6.16008 2.56866C6.12089 2.51827 6.0633 2.48552 5.99996 2.4776C5.93663 2.46969 5.87274 2.48725 5.82236 2.52644C5.77197 2.56563 5.73922 2.62322 5.7313 2.68656L5.3731 3.22387L5.13429 3.34328H4.53727C4.48694 3.33855 4.43641 3.34991 4.39295 3.37573C4.34949 3.40155 4.31535 3.4405 4.29543 3.48696C4.27552 3.53343 4.27086 3.58501 4.28214 3.63429C4.29341 3.68357 4.32003 3.728 4.35817 3.76119L4.77608 4.4179Z"
          fill="#7F56D9"
        />
        <path
          d="M14.5671 7.28356H14.4477C13.6723 6.92354 12.8472 6.68204 12 6.56714H11.7612L11.1044 7.04475L11.7015 7.16416L12.2388 7.34326C12.6567 7.52237 12.6567 7.76117 12.2388 7.94028L11.403 8.17908C9.43071 8.52385 7.41726 8.56412 5.4328 8.29848L3.99997 7.94028C3.82086 7.88057 3.64176 7.70147 3.64176 7.58207C3.64176 7.46266 3.82086 7.28356 3.99997 7.22386L5.3731 6.86565L6.14922 6.68654C6.44773 6.62684 6.32833 6.44774 6.26863 6.26863L5.91042 6.14923L3.70146 6.56714C2.80183 6.68265 1.94246 7.01003 1.194 7.52237C0.776088 7.88057 0.716386 8.23878 1.1343 8.53729C1.36587 8.73687 1.62789 8.89812 1.91042 9.0149L3.34325 9.43281C3.52236 9.49251 3.64176 9.55222 3.64176 9.67162C3.52236 10.9851 3.40295 12.3582 3.22385 13.6716C3.17947 13.8809 3.19516 14.0984 3.26911 14.2991C3.34305 14.4998 3.47224 14.6755 3.64176 14.8059L4.47758 15.2836C5.88928 15.8822 7.43105 16.1083 8.95519 15.9403C10.0681 15.8624 11.1493 15.536 12.1194 14.9851C12.3762 14.8574 12.5815 14.6455 12.701 14.3847C12.8206 14.124 12.847 13.8301 12.7761 13.5522C12.597 12.7164 12.5373 11.8806 12.4179 11.0448C12.2985 10.2089 12.4179 10.1492 12.4179 9.67162C12.4179 9.19401 12.4776 9.43281 12.7164 9.37311C13.3691 9.20335 14.0077 8.9838 14.6268 8.7164C15.3433 8.35819 15.3433 7.76117 14.5671 7.28356ZM11.403 10.4477C11.403 10.5074 11.2836 10.6268 11.2238 10.6268L8.41788 10.9851C7.21585 11.0479 6.01083 10.9475 4.83579 10.6865C4.59698 10.6268 4.53728 10.5074 4.53728 10.2686V9.49251C6.80486 9.93079 9.13538 9.93079 11.403 9.49251V10.5074V10.4477Z"
          fill="#7F56D9"
        />
      </g>
      <defs>
        <clipPath id="clip0_2438_104041">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
