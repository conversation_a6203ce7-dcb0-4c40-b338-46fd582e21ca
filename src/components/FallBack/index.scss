.fallback-global.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .dot {
    width: 16px;
    height: 16px;
    margin: 0 2px;
    background-color: #333;
    border-radius: 50%;
    animation: dotFlashing 1.4s infinite both;
  }

  .dot:nth-child(2) {
    animation-delay: .2s;
  }

  .dot:nth-child(3) {
    animation-delay: .4s;
  }
}


@keyframes dotFlashing {
  0% {
    transform: scale(0.5);
    background-color: #7F56D9;
  }

  50% {
    transform: scale(1);
    background-color: #ccc;
  }

  100% {
    transform: scale(0.5);
    background-color: #7F56D9;
  }
}