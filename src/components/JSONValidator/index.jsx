import React, { useState } from "react"
import React<PERSON><PERSON> from "react-json-view"
import { Input, Alert } from "antd"

const { TextArea } = Input

const JSONValidator = ({ value = "", onChange }) => {
  const [parsedJSON, setParsedJSON] = useState(null)
  const [error, setError] = useState(null)

  const handleInputChange = (e) => {
    const inputValue = e.target.value

    try {
      const parsedValue = JSON.parse(inputValue)
      setParsedJSON(parsedValue)
      setError(null)
    } catch (err) {
      setParsedJSON(null)
      setError(err.toString())
    }

    if (onChange) {
      onChange(inputValue) // Pass value to Form
    }
  }

  return (
    <div>
      <TextArea value={value} onChange={handleInputChange} placeholder="请输入JSON" rows={10} />
      {parsedJSON && (
        <div style={{ marginTop: 20 }}>
          <ReactJson src={parsedJSON} />
        </div>
      )}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          style={{ marginTop: 20 }}
        />
      )}
    </div>
  )
}

export default JSONValidator
