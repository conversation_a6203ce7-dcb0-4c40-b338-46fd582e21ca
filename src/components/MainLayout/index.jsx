/*
 * @Author: <PERSON><PERSON>
 * @Date: 2023-10-16 14:52:58
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-04-25 20:05:21
 * @FilePath: /za-aigc-platform-admin-static/src/components/MainLayout/index.jsx
 * Copyright (c) 2023 by ZA-智能中台, All Rights Reserved.
 */
// 1. 创建一个 MainLayout 组件
// @ts-ignore
import { ConfigProvider, Layout, theme } from "antd"
import ErrorBoundary from "@/pages/errorBoundary"
const { darkAlgorithm, defaultAlgorithm } = theme
import { GlobalLoadingIndicator } from "@/components/globalLoadingIndicator"
// @ts-ignore
import { useEffect, useState, useCallback, Profiler } from "react"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import { usePreviousLocation } from "@/router/PreviousLocationProvider"
import React from "react"

const MainLayout = React.memo(function MainLayout({
  // @ts-ignore
  children,
  // @ts-ignore
  closeGlobalLoadingIndicator = false
}) {
  // @ts-ignore
  const globalTheme = useSelector((state) => state.theme)
  const [antdTheme, setAntdTheme] = useState({
    algorithm: globalTheme.dark ? darkAlgorithm : defaultAlgorithm
  })
  useEffect(() => {
    let newTheme = {
      algorithm: globalTheme.dark ? darkAlgorithm : defaultAlgorithm
    }
    if (globalTheme.colorPrimary) {
      newTheme.token = { colorPrimary: globalTheme.colorPrimary }
    }
    setAntdTheme(newTheme)
  }, [globalTheme])

  const location = useLocation()
  const { setPrevLocation } = usePreviousLocation()

  useEffect(() => {
    setPrevLocation(location.pathname)
  }, [location.pathname, setPrevLocation])

  return (
    <ConfigProvider theme={antdTheme}>
      {!closeGlobalLoadingIndicator ? <GlobalLoadingIndicator /> : null}
      <ErrorBoundary>{children}</ErrorBoundary>
    </ConfigProvider>
  )
})

export default MainLayout
