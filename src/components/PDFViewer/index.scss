.pdf-viewer-container {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;

  .pdf-viewer-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
  }
}

.rpv-default-layout__toolbar {
  background: transparent;
  height: 34px;
  border: none;
  display: flex;
  justify-content: flex-end;
}

.rpv-default-layout__container {
  border: none;
}

/* 自定义工具栏样式 */
.pdf-custom-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 0 8px;
  height: 34px;
  box-shadow: 0px 4px 32px 0px #3333331f;

  .pdf-navigation {
    display: flex;
    align-items: center;

    .pdf-nav-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border: none;
      border-radius: 6px;
      background-color: transparent;
      color: #475467;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: #f0f0f0;
        color: #333;
      }

      &:disabled {
        color: #ccc;
        cursor: not-allowed;
        opacity: 0.5;
      }

      svg {
        pointer-events: none;
      }
    }

    .pdf-page-info {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #475467;
      justify-content: center;

      .rpv-page-navigation__current-page-input {
        width: initial;
      }

      input {
        width: 25px;
        height: 25px;
        text-align: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 4px;
        font-size: 12px;

        &:focus {
          outline: none;
        }
      }

      .pdf-page-separator {
        margin: 0 8px;
      }
    }
  }

  .pdf-zoom-controls {
    display: flex;
    align-items: center;

    .pdf-zoom-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border: none;
      border-radius: 6px;
      background-color: transparent;
      color: #475467;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: #f0f0f0;
        color: #333;
      }

      &:disabled {
        color: #ccc;
        cursor: not-allowed;
        opacity: 0.5;
      }

      svg {
        pointer-events: none;
      }
    }

    .pdf-zoom-level {
      font-size: 14px;
      color: #666;
      min-width: 50px;
      text-align: center;
      font-weight: 500;
      padding: 4px 0;
    }
  }
}

/* 隐藏默认工具栏 */
:global(.rpv-default-layout__toolbar) {
  display: none !important;
}

/* PDF内容区域样式 */
:global(.rpv-core__viewer) {
  background-color: #f8f9fa;
}

:global(.rpv-core__page-layer) {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-custom-toolbar {
    padding: 8px 12px;
    flex-wrap: wrap;
    gap: 8px;

    .pdf-navigation {
      order: 1;
      flex: 1;
      justify-content: center;
    }

    .pdf-zoom-controls {
      order: 2;
      flex: 1;
      justify-content: center;
    }
  }
}

/* 加载和错误状态 */
:global(.rpv-core__doc-loading) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
  font-size: 14px;
}

:global(.rpv-core__doc-error) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #e74c3c;
  font-size: 14px;
}
