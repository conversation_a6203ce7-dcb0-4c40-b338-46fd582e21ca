.plugin-edit-modal {
  .header-img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    img {
      border-radius: 8px;
      width: 80px;
      height: 80px;
    }
  }
  .headers-list-wrap {
    border: 1px solid rgba(29,28,35,.12);
    border-radius: 8px;
    padding: 0 16px;
    .header-row {
      line-height: 40px;
      border-bottom: 1px solid rgba(29,28,35,.12);
    }
    .headers-list-content {
      padding-top: 24px;
      max-height: 306px;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  
  .add-btn {
    position: absolute;
    top: -28px;
    right: 0;
    color: #5e5ff8;
  }
}

.plugin-edit-modal-header-tips {
  .header-tips {
    padding: 12px 16px;
    font-size: 14px;
    color: #333;
    .tips-head {
      line-height: 24px;
    }
    .tips-content {
      line-height: 22px;
      font-size: 12px;
      color: #999;
    }
  }
}