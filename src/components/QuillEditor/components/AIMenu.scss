.ai-menu-comp {


  .arrow {
    font-size: 10px;
    color: rgba(55, 53, 47, 0.45);
    transform: translateY(2px);

  }

  .ai-menu {
    position: absolute;
    display: none;
    width: 250px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    transform: translateY(-10px);
    opacity: 0;
    font-family: 'Arial', sans-serif;
    font-size: 14px;
    z-index: 9999;
    margin-top: 4px;
  }

  .ai-menu.show {
    display: block;
    transform: translateY(0);
    opacity: 1;
  }

  .ai-menu-header {
    padding: 10px 15px;
    font-weight: 500;
  }

  .ai-menu-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }

  .ai-menu-list li {
    padding: 10px 15px;
    cursor: pointer;
    // border-bottom: 1px solid #e1e1e1;
    position: relative;
    color: #555;
    transition: background-color 0.2s;
    display: flex;
    justify-content: space-between;
  }

  .ai-menu-list li:hover {
    background-color: #f7f7f7;
  }

  .ai-menu-list li.selected {
    background: var(--main-2, #F2F2FE);
    ;
    color: #333;
  }

  .ai-submenu {
    position: absolute;
    top: 0;
    left: 100%;
    width: 200px;
    background-color: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
  }

  .ai-submenu li {
    padding: 10px 15px;
    // border-bottom: 1px solid #e1e1e1;
    transition: background-color 0.2s;
  }

  .ai-submenu li:hover {
    background: var(--main-2, #F2F2FE);
  }

  .ai-menu-actions {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    border-top: 1px solid #e1e1e1;
  }

  .ai-menu-actions button {
    // background-color: #f7f7f7;
    // border: 1px solid #e1e1e1;
    // border-radius: 4px;
    // padding: 5px 10px;
    // cursor: pointer;
    // transition: background-color 0.2s;
  }

  .ai-menu-actions button:hover {
    // background: var(--main-2, #F2F2FE);
    ;
  }

  .ai-menu-wrapper {
    border-radius: 6px;
    background: #FFF;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.20);

  }

  .ai-content {
    top: -30px;
    width: 500px;
  }

  .input-bar {
    outline: none; // 去掉默认的outline
    width: 500px;
    border: none; // 去掉默认边框
    border-radius: 0; // 去掉圆角

    &:hover,
    &:focus,
    &:active {
      border-color: transparent; // 去掉hover, focus, active时的边框颜色
      box-shadow: none; // 去掉focus时的阴影
      border: 0;
      outline: none; // 去掉默认的outline
    }

    &::-webkit-input-placeholder {
      // 更改placeholder的样式（如果需要的话）
      color: #555;
      opacity: 0.7;
    }
  }

  .ai-content-gen {
    background-color: #fff;
    padding: 0.6rem; // 增加些许内边距以匹配Input的内边距
  }

  .input-wrapper {
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.20);
    border-radius: 6px; // 添加这一行

    .ai-content-gen,
    .input-bar {
      border-radius: 6px;
    }

  }

}