.quill-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  .quill-editor-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ql-toolbar {
      border-radius: 8px 8px 0 0;
      border-color: #d0d5dd;
    }
    .ql-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      border-radius: 0 0 8px 8px;
      border-color: #d0d5dd;
    }
  }
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: 'H3' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: 'H1' !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: 'H2' !important;
}

.ql-snow .ql-picker.ql-header {
  width: 80px !important;
}

.quill-editor-wrapper {
  img {
    height: auto !important;
    margin: 0;
    padding: 0;
  }
}