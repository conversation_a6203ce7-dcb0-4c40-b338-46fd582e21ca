/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-09 13:37:36
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-05-06 16:02:25
 * @FilePath: /za-aigc-platform-admin-static/src/components/SSOProvider/index.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
import { FetchUserInfoBySession, getTokenAndServiceName } from "@/api/sso"
import React from "react"
import { createContext, useContext, useEffect, useState } from "react"

const SSOContext = createContext({ isInitialized: false, userInfo: {} })

export const useSSO = () => {
  return useContext(SSOContext)
}

// @ts-ignore
export const SSOProvider = React.memo(({ children }) => {
  const [isInitialized, setIsInitialized] = useState(!!getTokenAndServiceName().token)
  const [userInfo, setUserInfo] = useState({})

  useEffect(() => {
    FetchUserInfoBySession(null, (userData) => {
      setIsInitialized(true)
      setUserInfo(userData)
    })
  }, [])

  useEffect(() => {
    if (userInfo) {
      window.parent.postMessage(
        {
          message: "za-iframe-userinfo",
          userInfo
        },
        "*"
      )
    }
  }, [userInfo])

  return isInitialized ? (
    <SSOContext.Provider value={{ isInitialized, userInfo }}>{children}</SSOContext.Provider>
  ) : null
})
