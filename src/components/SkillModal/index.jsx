import React, { useRef, useEffect, useState, useMemo } from "react"
import { Modal, Form, Input, Button, message, Select, Tabs } from "antd"
import { useFetchLlmModelType, useFetchSkillType } from "@/api/common"
import { useUpdateSkill, useUpdateSubscribeSkill } from "@/api/skill"
import { useQueryClient } from "@tanstack/react-query"
import { QUERY_KEYS } from "@/constants/queryKeys"
import { skillAvatarList } from "@/assets/imgUrl"
import { useFetchAppPlatformList } from "@/api/bot"
import AvatarSelect from "@/pages/addBot/components/AvatarSelect"
import "./index.scss"
import { AVATAR_ICON_TYPE, avatarMode } from "@/constants"
import SecurityPolicySwitch from "../SecurityPolicySwitch"
import ShareSettingForm from "./ShareSettingForm"
import { fetchSourceTag } from "@/api/sourceTag/api"
import { useFetchSourceTagList } from "@/api/sourceTag"
import { getDifyToken } from "@/components/CreateSkillStep"

const { TabPane } = Tabs

export const canReadOptions = [
  { label: "可查看详情，不可编辑", value: true },
  { label: "不可查看详情，不可编辑", value: false }
]

const SkillModal = ({
  visible,
  onClose,
  botNo,
  initialValues: initValues,
  skillNo,
  onSuccess = () => {}
}) => {
  const shareFormRef = useRef(null)
  const [form] = Form.useForm()
  const queryClient = useQueryClient()
  const [selectedAvatar, setSelectedAvatar] = useState(skillAvatarList[0])
  const [tabsActiveKey, setTabsActiveKey] = useState("1")

  const { data: appPlatformList = [] } = useFetchAppPlatformList()

  // 是否订阅技能
  const isSubscribedSkill = useMemo(() => {
    return initValues.type === "subscribed_skill"
  }, [initValues])

  // 技能是否调试中
  const isDebug = useMemo(() => {
    return initValues.status === 2
  }, [initValues])

  const initialValues = useMemo(() => {
    const { subscribeSettings = {} } = initValues
    // 如果是订阅技能，则取 subscribeSettings 覆盖原默认值
    return isSubscribedSkill ? { ...initValues, ...subscribeSettings } : { ...initValues }
  }, [initValues, isSubscribedSkill])

  const handleAvatarSelect = ({ iconURL, objectKey, iconType }) => {
    setSelectedAvatar(iconURL)
    form.setFieldsValue({
      iconUrl: iconURL
    })
    form.setFieldsValue({
      icon: { iconURL, objectKey, iconType }
    })
  }

  const onFinish = async (values) => {
    console.log("Form values: ", values)
    if (isSubscribedSkill) {
      return handleUpdateSubscribeSkill(values)
    }
    if (initValues.generateMethod === 2) {
      const difyRes = await getDifyToken(botNo)
      if (!difyRes) return
      values.difyAccessToken = difyRes.difyData?.accessToken
    }
    /* 去除未分组 */
    if (!values.groupTagId) {
      delete values.groupTagId
    }
    updateSkill(
      {
        ...values,
        icon: form.getFieldValue("icon") ?? {
          iconURL: selectedAvatar,
          iconType: [...skillAvatarList].includes(selectedAvatar)
            ? AVATAR_ICON_TYPE.SYSTEM
            : AVATAR_ICON_TYPE.CUSTOM
        },
        skillNo
      },
      {
        onSuccess: (e) => {
          console.log(e)
          queryClient.invalidateQueries([QUERY_KEYS.SKILL_INFO, skillNo])
          if (e.success) {
            message.success("更新成功")
            onSuccess()
          } else {
            // @ts-ignore
            message.error(e.message)
          }
        }
      }
    )
    onClose()
  }

  const handleUpdateSubscribeSkill = (values) => {
    updateSubscribeSkill(
      {
        botNo,
        skillNo,
        ...values,
        type: initialValues.type
      },
      {
        onSuccess: (e) => {
          if (e.success) {
            message.success("更新成功")
            onSuccess()
          } else {
            // @ts-ignore
            message.error(e.message)
          }
        }
      }
    )
    onClose()
  }

  const handleConfirm = () => {
    form
      .validateFields()
      .then((values) => {
        onFinish(values)
        !isDebug && shareFormRef?.current?.onFinish(values)
      })
      .catch((error) => {
        const errorFieldsName = error.errorFields.reduce((pre, cur) => {
          return [...pre, ...cur.name]
        }, [])
        if (errorFieldsName.includes("skillName")) {
          setTabsActiveKey("1")
        } else {
          setTabsActiveKey("2")
        }
      })
  }

  // const { data: skillType = [] } = useFetchSkillType()
  const { mutate: updateSkill } = useUpdateSkill()
  const { mutate: updateSubscribeSkill } = useUpdateSubscribeSkill()

  const { data: groupData = {} } = useFetchSourceTagList(
    {
      botNo,
      tagType: "skillGroupTag"
    },
    {
      enabled: !!botNo
    }
  )

  const skillGroupList = useMemo(() => {
    // @ts-ignore
    return [{ id: 0, tagDesc: "未分组" }, ...(groupData.data || [])]
  }, [groupData])

  useEffect(() => {
    if (initialValues?.iconUrl) {
      setSelectedAvatar(initialValues.iconUrl)
    }
  }, [initialValues])

  useEffect(() => {
    setTabsActiveKey("1")
    console.log("🤖==> ~ initialValues:", initialValues)

    initialValues.groupTagId = +(initialValues.groupTagId || 0)
    form.setFieldsValue(initialValues)
    // 初始化共享设置表单数据
    visible && !isDebug && shareFormRef?.current?.init()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible])

  return (
    <Modal
      title={isSubscribedSkill ? "订阅技能设置" : "技能基础设置"}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleConfirm}>
          确定
        </Button>
      ]}
    >
      <Form
        form={form}
        // onFinish={onFinish}
        initialValues={initialValues}
        className="mt-5 skill-modal-form"
        labelCol={{
          span: 5
        }}
      >
        {isSubscribedSkill ? (
          <>
            <Form.Item
              name="name"
              label="显示名称"
              rules={[{ required: true, message: "请输入显示名称" }]}
            >
              <Input placeholder="请输入显示名称" />
            </Form.Item>
            <Form.Item name="description" label="描述">
              <Input.TextArea placeholder="请输入描述" />
            </Form.Item>
            {initialValues.type !== "3" && (
              <Form.Item
                label="可见应用端"
                name="applicationPlatformTypes"
                tooltip={"若本技能所属机器人未配置对应应用端，则本技能不可见"}
                initialValue={[...appPlatformList.map((item) => item.code)]}
                rules={[{ required: false, message: "请选择应用端" }]}
              >
                <Select
                  allowClear
                  style={{ width: 370 }}
                  placeholder="请选择"
                  mode="multiple"
                  options={appPlatformList}
                  fieldNames={{ label: "name", value: "code" }}
                />
              </Form.Item>
            )}
          </>
        ) : (
          <Tabs type="card" activeKey={tabsActiveKey} onChange={(val) => setTabsActiveKey(val)}>
            <TabPane tab="基础设置" key="1" forceRender>
              <Form.Item name="skillNo" label="技能编号">
                <Input placeholder="请输入技能编号" disabled={true} />
              </Form.Item>
              <Form.Item
                name="skillName"
                label="技能名称"
                rules={[{ required: true, message: "技能名称是必填的" }]}
              >
                <Input placeholder="请输入技能名称" />
              </Form.Item>
              <Form.Item name="description" label="描述">
                <Input.TextArea placeholder="请输入描述" />
              </Form.Item>
              <Form.Item
                name="groupTagId"
                label="技能分组"
                rules={[{ required: true, message: "请选择技能分组" }]}
              >
                <Select
                  allowClear
                  style={{ width: 370 }}
                  placeholder="请选择"
                  options={skillGroupList}
                  fieldNames={{ label: "tagDesc", value: "id" }}
                />
              </Form.Item>
              {/* 类型只展示当前类型 */}
              <Form.Item name="type" label="类型">
                {initialValues.type === "1"
                  ? "快速问答"
                  : initialValues.type === "2"
                    ? "表单类型"
                    : "API类型"}
                {/* <Radio.Group buttonStyle="solid" disabled>
                    {skillType.map((type) => (
                      <Radio.Button key={type.code} value={type.code}>
                        {type.name}
                      </Radio.Button>
                    ))}
                  </Radio.Group> */}
              </Form.Item>
              {/* <Form.Item
                  name="iconUrl"
                  label="头像"
                  initialValue={selectedAvatar}
                  className="avatar-form-item"
                >
                  <AvatarSelect
                    mode={avatarMode.skill}
                    selectedAvatar={selectedAvatar}
                    handleAvatarSelect={handleAvatarSelect}
                  />
                </Form.Item> */}
              {initialValues.type !== "3" && (
                <Form.Item
                  label="可见应用端"
                  name="applicationPlatformTypes"
                  tooltip={"若本技能所属机器人未配置对应应用端，则本技能不可见"}
                  initialValue={[...appPlatformList.map((item) => item.code)]}
                  rules={[{ required: false, message: "请选择应用端" }]}
                >
                  <Select
                    allowClear
                    style={{ width: 370 }}
                    placeholder="请选择"
                    mode="multiple"
                    options={appPlatformList}
                    fieldNames={{ label: "name", value: "code" }}
                  />
                </Form.Item>
              )}
              <Form.Item
                tooltip={
                  initialValues.type === "1"
                    ? "快速聊天技能必须开启安全策略，安全网关将过滤敏感词等风险信息"
                    : "启用后,安全网关将过滤敏感词等风险信息"
                }
                label="安全策略"
                name="enableSenseInfoDetect"
                valuePropName="checked"
                initialValue={true}
              >
                <SecurityPolicySwitch form={form} disabled={true} />
              </Form.Item>
            </TabPane>
            {!isDebug && !initValues.appId && (
              <TabPane tab="共享设置" key="2" forceRender>
                <ShareSettingForm
                  ref={shareFormRef}
                  form={form}
                  bizType="SKILL"
                  botNo={botNo}
                  skillNo={skillNo}
                />
              </TabPane>
            )}
          </Tabs>
        )}
      </Form>
    </Modal>
  )
}

export default SkillModal
