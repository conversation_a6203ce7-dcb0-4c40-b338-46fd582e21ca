import React from "react"
import { Modal, Form, Input } from "antd"
import AvatarSelect from "@/pages/addBot/components/AvatarSelect"
import { avatarMode } from "@/constants"

const SkillTemplateEditModal = ({
  visible,
  handleOk,
  handleCancel,
  form,
  selectedAvatar,
  handleAvatarSelect
}) => {
  return (
    <Modal
      title={"编辑技能模板"}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
    >
      <div style={{ color: "grey", marginBottom: 15 }}>自定义技能模板仅当前空间可见</div>
      <Form
        form={form}
        labelCol={{
          span: 6
        }}
        wrapperCol={{
          span: 16
        }}
      >
        <Form.Item
          name="name"
          label="技能名称"
          rules={[{ required: true, message: "请输入技能名称!" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="description"
          label="技能描述"
          rules={[{ required: true, message: "请输入技能描述!" }]}
        >
          <Input.TextArea />
        </Form.Item>
        <Form.Item name="description" label="类型">
          {form.getFieldValue("skillTypeName")}
        </Form.Item>
        {/* <Form.Item
          label="技能头像"
          name="iconUrl"
          rules={[{ required: true, message: "请选择头像" }]}
          initialValue={selectedAvatar}
        >
          <AvatarSelect
            disabled={false}
            mode={avatarMode.skill}
            selectedAvatar={selectedAvatar}
            handleAvatarSelect={handleAvatarSelect}
          />
        </Form.Item> */}
      </Form>
    </Modal>
  )
}

export default SkillTemplateEditModal
