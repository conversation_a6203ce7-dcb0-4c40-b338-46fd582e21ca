.tabel-object-pro {
  padding: 15px 0;
  :global {
    .ant-form-item {
      margin: 0;
    }
    .ant-table-placeholder,
    .ant-table {
      background: none !important;
    }
  }
  td {
    position: relative;
  }
  .expanded-icon {
    color: inherit;
    text-decoration: none;
    outline: none;
    cursor: pointer;
    margin: 5px 5px 0 0;
    transition: all 0.3s;
    position: relative;
    float: left;
    box-sizing: border-box;
    width: 20px;
    height: 20px;
    padding: 0;
    line-height: 17px;
    border-radius: 6px;
    user-select: none;
    color: #777;
    transform: rotate(0deg);
    font-size: 14px;
    svg {
      transition: all 0.3s;
      transform: rotate(0);
    }
  }
  .expanded-cre {
    svg {
      transition: all 0.3s;
      transform: rotate(-90deg);
    }
  }
  .add-btn {
    margin-top: 20px;
  }
  .required-icon {
    color: red;
  }
}
