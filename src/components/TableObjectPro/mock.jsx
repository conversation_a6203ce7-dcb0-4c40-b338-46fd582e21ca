/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-22 10:47:53
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-04-24 15:57:19
 * @FilePath: /za-aigc-platform-admin-static/src/pages/market/components/TableObjectPro/mock.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
export const mock = [
  {
    variableName: "",
    variableDesc: "",
    variableRequire: true,
    variableValueType: "String",
    key: "1",
    parentKey: null,
    children: [
      {
        variableName: "",
        variableDesc: "",
        variableRequire: true,
        variableValueType: "String",
        key: "1-1",
        parentKey: "1",
        children: [
          {
            variableName: "",
            variableDesc: "",
            variableRequire: true,
            variableValueType: "String",
            key: "1-1-1",
            parentKey: "1-1"
          }
        ]
      },
      {
        variableName: "",
        variableDesc: "",
        variableRequire: true,
        variableValueType: "String",
        key: "1-2",
        parent<PERSON>ey: "1",
        children: []
      }
    ]
  }
]
