/*
 * @Author: dyton
 * @Date: 2023-10-16 14:52:58
 * @Descripttion: 文件描述
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2023-10-18 14:21:22
 * @FilePath: /za-aigc-platform-admin-static/src/components/extraIcons/index.jsx
 * Copyright (c) 2023 by ZA-智能中台, All Rights Reserved.
 */
import Icon from "@ant-design/icons"

const SunSvg = () => (
  <svg
    t="1669043669296"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="1180"
    width="1em"
    height="1em"
    fill="currentColor"
  >
    <path
      d="M554.666667 42.666667v85.333333a42.666667 42.666667 0 0 1-85.333334 0V42.666667a42.666667 42.666667 0 0 1 85.333334 0z m-42.666667 810.666666a42.666667 42.666667 0 0 0-42.666667 42.666667v85.333333a42.666667 42.666667 0 0 0 85.333334 0v-85.333333a42.666667 42.666667 0 0 0-42.666667-42.666667zM149.962667 149.962667a42.666667 42.666667 0 0 0 0 60.341333l60.341333 60.341333a42.666667 42.666667 0 0 0 60.341333-60.341333l-60.341333-60.341333a42.666667 42.666667 0 0 0-60.341333 0z m603.392 603.392a42.666667 42.666667 0 0 0 0 60.330666v0.010667l60.341333 60.341333a42.666667 42.666667 0 0 0 60.341333-60.341333l-60.341333-60.341333a42.656 42.656 0 0 0-60.341333 0zM0 512a42.666667 42.666667 0 0 0 42.666667 42.666667h85.333333a42.666667 42.666667 0 0 0 0-85.333334H42.666667a42.666667 42.666667 0 0 0-42.666667 42.666667z m853.333333 0a42.666667 42.666667 0 0 0 42.666667 42.666667h85.333333a42.666667 42.666667 0 0 0 0-85.333334h-85.333333a42.666667 42.666667 0 0 0-42.666667 42.666667zM149.962667 874.037333a42.666667 42.666667 0 0 0 60.341333 0l60.330667-60.341333a42.666667 42.666667 0 0 0-60.330667-60.341333l-60.341333 60.341333a42.666667 42.666667 0 0 0 0 60.341333z m603.392-603.392a42.666667 42.666667 0 0 0 60.330666 0h0.010667l60.341333-60.341333a42.666667 42.666667 0 0 0-60.341333-60.341333l-60.341333 60.341333a42.656 42.656 0 0 0 0 60.341333zM768 512c0 141.386667-114.613333 256-256 256S256 653.386667 256 512s114.613333-256 256-256 256 114.613333 256 256z m-85.333333 0c0-94.261333-76.405333-170.666667-170.666667-170.666667s-170.666667 76.405333-170.666667 170.666667 76.405333 170.666667 170.666667 170.666667 170.666667-76.405333 170.666667-170.666667z"
      p-id="1181"
    ></path>
  </svg>
)

const MoonSvg = () => (
  <svg
    t="1669043635815"
    viewBox="0 0 1024 1024"
    version="1.1"
    p-id="1041"
    width="1em"
    height="1em"
    fill="currentColor"
  >
    <path
      d="M427.989333 181.12A395.84 395.84 0 0 0 426.666667 213.333333c0 211.754667 172.245333 384 384 384 10.816 0 21.557333-0.437333 32.213333-1.322666C805.344 743.733333 671.232 853.333333 512 853.333333c-188.213333 0-341.333333-153.12-341.333333-341.333333 0-159.232 109.6-293.344 257.322666-330.88M512 85.333333C276.362667 85.333333 85.333333 276.362667 85.333333 512c0 235.648 191.029333 426.666667 426.666667 426.666667 235.648 0 426.666667-191.018667 426.666667-426.666667 0-9.525333-0.426667-18.933333-1.045334-28.309333A297.418667 297.418667 0 0 1 810.666667 512c-164.949333 0-298.666667-133.717333-298.666667-298.666667 0-45.408 10.186667-88.426667 28.309333-126.954666A424.672 424.672 0 0 0 512 85.333333z"
      p-id="1042"
    ></path>
  </svg>
)

const ThemeSvg = () => (
  <svg
    t="1669127520687"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="1107"
    width="1em"
    height="1em"
    fill="currentColor"
  >
    <path
      d="M857.6 268.8c-9.6-12.8-32-16-44.8-6.4-12.8 9.6-16 32-6.4 44.8 38.4 54.4 57.6 121.6 57.6 201.6 0 48-9.6 92.8-28.8 134.4-12.8-3.2-22.4-3.2-35.2-3.2-86.4 0-156.8 64-156.8 140.8 0 12.8 3.2 28.8 6.4 41.6-38.4 16-76.8 25.6-118.4 25.6-19.2 0-32 16-32 32s16 32 32 32c60.8-3.2 118.4-16 172.8-44.8 9.6-3.2 12.8-12.8 16-19.2 3.2-9.6 0-19.2-3.2-25.6-9.6-12.8-12.8-25.6-12.8-38.4 0-41.6 41.6-76.8 92.8-76.8 12.8 0 25.6 3.2 38.4 6.4 16 6.4 32 0 41.6-16 32-57.6 48-124.8 48-188.8 3.2-92.8-19.2-176-67.2-240zM185.6 700.8c16-9.6 22.4-28.8 12.8-41.6C172.8 608 160 560 160 508.8 160 320 313.6 166.4 502.4 166.4c22.4 0 51.2 3.2 67.2 6.4s35.2-9.6 38.4-25.6-9.6-35.2-25.6-38.4c-22.4-3.2-54.4-6.4-80-6.4C278.4 102.4 96 284.8 96 508.8c0 64 12.8 118.4 44.8 179.2 6.4 9.6 19.2 16 28.8 16 6.4 0 9.6 0 16-3.2z"
      p-id="1108"
    ></path>
    <path d="M272 432m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z" p-id="1109"></path>
    <path d="M352 288m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z" p-id="1110"></path>
    <path
      d="M524.8 704l297.6-556.8c9.6-25.6 3.2-57.6-22.4-73.6-25.6-16-57.6-12.8-76.8 12.8L332.8 582.4s0 3.2-3.2 3.2c-9.6 16-12.8 35.2-9.6 51.2 0 3.2 3.2 6.4 3.2 9.6-41.6 3.2-102.4 25.6-115.2 89.6-9.6 44.8 3.2 67.2 9.6 83.2 0 3.2 3.2 6.4 3.2 3.2l-3.2 3.2c-16 22.4-32 25.6-32 25.6-12.8 3.2-22.4 12.8-25.6 25.6-3.2 12.8 3.2 25.6 12.8 32 32 22.4 89.6 51.2 144 51.2 41.6 0 83.2-16 118.4-54.4 54.4-64 44.8-160 44.8-166.4 19.2-6.4 35.2-19.2 44.8-35.2zM384 620.8L736 169.6 470.4 672c-3.2 3.2-6.4 6.4-6.4 3.2L384 627.2v-6.4z m3.2 243.2c-38.4 44.8-92.8 35.2-131.2 19.2 6.4-6.4 9.6-12.8 16-19.2 22.4-32 9.6-57.6 3.2-70.4-6.4-9.6-9.6-22.4-6.4-44.8 6.4-28.8 44.8-35.2 60.8-38.4l89.6 48c0 25.6-3.2 73.6-32 105.6z"
      p-id="1111"
    ></path>
  </svg>
)

export const SunOutlined = (props) => <Icon component={SunSvg} {...props} />
export const MoonOutlined = (props) => <Icon component={MoonSvg} {...props} />
export const ThemeOutlined = (props) => <Icon component={ThemeSvg} {...props} />
