import { getEnv } from "@/api/sso"

const difyUrls = {
  dev: "http://4285543-za-aigc-dify-web.test.za.biz",
  test: "https://aigc-test.zhonganonline.com",
  pre: "https://aigc-pre.zhonganonline.com",
  prd: "https://aigc.zhonganonline.com"
}

export function getDifyUrl() {
  const currentEnv = getEnv()
  const url = "/dify"
  return (difyUrls[currentEnv] || difyUrls["test"]) + url
}

const materialUrls = {
  dev: "https://material-engine-test.zhonganonline.com",
  test: "https://material-engine-test.zhonganonline.com",
  pre: "https://material-engine-pre.zhonganonline.com",
  prd: "https://material-engine.zhonganonline.com"
}

export function getMaterialUrl() {
  const currentEnv = getEnv()
  return materialUrls[currentEnv] || materialUrls["test"]
}
