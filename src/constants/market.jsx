/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-16 20:31:45
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-04-23 18:56:59
 * @FilePath: /za-aigc-platform-admin-static/src/constants/market.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */

import Iconfont from "@/components/Icon"
import { SKILLICONTYPE } from "."
export const emptyIcon = "https://static.zhongan.com/website/cs/poseidon/empty.png"
export const marketCode = {
  ALL: "ALL",
  APP: "APP",
  SKILL: "SKILL",
  PLUG_IN: "PLUG_IN",
  ROBOT: "ROBOT",
  AGENT: "AGENT",
  TIMBRE: "TIMBRE"
}

export const marketOptions = [
  {
    value: marketCode.ALL,
    label: "全部"
  },
  // {
  //   value: marketCode.APP,
  //   label: "应用"
  // },
  {
    value: marketCode.AGENT,
    label: "Agent"
  },
  {
    value: marketCode.SKILL,
    label: "技能"
  },
  {
    value: marketCode.PLUG_IN,
    label: "工具"
  },
  {
    value: marketCode.ROBOT,
    label: "应用"
  },
  {
    value: marketCode.TIMBRE,
    label: "声音"
  }
]

export const marketObject = {
  [marketCode.ALL]: "全部",
  [marketCode.APP]: "应用",
  [marketCode.SKILL]: "技能",
  [marketCode.PLUG_IN]: "插件",
  [marketCode.ROBOT]: "机器人",
  [marketCode.AGENT]: "Agent",
  [marketCode.TIMBRE]: "声音"
}

/**
 * @description: 相关卡片主题色
 * @param {*} type
 * @return {*}
 */
export const getThemeConfig = (type) => {
  switch (type) {
    case marketCode.APP:
      return {
        color: "#7F56D9",
        hover: "#5cdbd3",
        border: "#b5f5ec",
        title: "应用",
        icon: <Iconfont type={"icon-yingyong"} />
      }
    case marketCode.PLUG_IN:
      return {
        color: "#1677ff",
        hover: "#4096ff",
        border: "#91caff",
        title: "工具",
        icon: <Iconfont type={"icon-chajian"} />
      }
    case marketCode.SKILL:
      return {
        color: "#7F56D9",
        hover: "#8b91fc",
        border: "#efdbff",
        title: "技能",
        icon: <Iconfont type={"icon-APIjineng"} />
      }
    case marketCode.AGENT:
      return {
        color: "#7F56D9",
        hover: "#8b91fc",
        border: "#efdbff",
        title: "Agent",
        icon: <Iconfont type={"icon-APIjineng"} />
      }
    case marketCode.ROBOT:
      return {
        color: "#7F56D9",
        hover: "#7F56D9",
        border: "#b5f5ec",
        title: "应用",
        icon: <Iconfont type={"icon-APIjineng"} />
      }
    case marketCode.TIMBRE:
      return {
        color: "#52c41a",
        hover: "#73d13d",
        border: "#b7eb8f",
        title: "声音",
        icon: <Iconfont type={"icon-shengyin"} />
      }
    default:
      return {
        color: "#7F56D9",
        hover: "#8b91fc",
        border: "#efdbff",
        title: "",
        icon: <Iconfont type={"icon-APIjineng"} />
      }
  }
}

export const getIcon = (type, skillType) => {
  switch (type) {
    case marketCode.APP:
      return <Iconfont type={"icon-yingyong"} />
    case marketCode.PLUG_IN:
      return <Iconfont type={"icon-chajian"} />
    case marketCode.ROBOT:
      return <Iconfont type={"icon-jiqiren"} />
    case marketCode.SKILL:
      return <Iconfont type={SKILLICONTYPE?.[skillType ?? 3]} />
    case marketCode.TIMBRE:
      return <Iconfont type={"icon-shengyin"} />
    default:
      return <Iconfont type={"icon-APIjineng"} />
  }
}

export const subtitleOptions = [
  { value: "AGENT", label: "众有灵犀 · 百业千面，随需而智" },
  { value: "SKILL", label: "众有灵犀 · 任务自治，效率自驱" },
  { value: "PLUG_IN", label: "众有灵犀 · 需求即达，使命必达" },
  { value: "ROBOT", label: "众有灵犀 · 所有AI智能体，一个指令调度" },
  { value: "TIMBRE", label: "众有灵犀 · 声音市集，音色丰富" }
]
