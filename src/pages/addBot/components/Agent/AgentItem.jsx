import {
  But<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Divider,
  Tag,
  Modal,
  Popconfirm,
  message,
  Radio,
  Tooltip
} from "antd"
import { useNavigate } from "react-router-dom"
import CopyToClipboard from "react-copy-to-clipboard"
import { StarOutlined } from "@ant-design/icons"
import { AGENT_MODE_OPTIONS } from "@/components/CreateAgent"
import { useCancelSubscribeApi } from "@/api/market"
import { useCopyAgent } from "@/api/agent"
import { useQueryClient } from "@tanstack/react-query"
import defaultOldUrl from "@/assets/img/agentAvater.png"
import defaultImg from "@/assets/img/agentAvater-new.png?url"
import { getMaterialUrl } from "@/config.env"
import { cancelBubble } from "@/utils"
import { QUERY_KEYS } from "@/constants/queryKeys"
import { useState } from "react"
import { useMemo } from "react"
import { deepCopyAgent } from "@/api/agent/api"
import { isAdmin } from "@/api/agent/api"
import { useEffect } from "react"

const { Text } = Typography

const defautOldUrl =
  "http://za-aigc-platform-test.oss-cn-hzjbp-b-internal.aliyuncs.com/txrzvjbtpbiqi.png?Expires=1744448056&OSSAccessKeyId=LTAI5tMugme8cECmAEPFEd1r&Signature=Rpq1ixYX2ulI00Fwjnsc6BTzo7g%3D"

const defaultOldUrl2 =
  "http://za-aigc-platform-test.oss-cn-hzjbp-b-internal.aliyuncs.com/txryvhwxsvhme.jpg?Expires=1744446759&OSSAccessKeyId=LTAI5tMugme8cECmAEPFEd1r&Signature=kGkh96QoJtK3LNXDU%2FBEBTDJnHc%3D"

const defaultOldUrl3 =
  "http://za-aigc-platform-test.oss-cn-hzjbp-b-internal.aliyuncs.com/tyjtezebcyhyu.jpeg?Expires=1745045297&OSSAccessKeyId=LTAI5tMugme8cECmAEPFEd1r&Signature=uWvIqKr6XXzhek%2B5Db7x7NuWRpI%3D"

const COPY_TYPE = {
  NORMAL: "normal",
  DEEP: "deep"
}

const AgentItem = ({
  agent,
  currentBotNo,
  workbenchNo,
  iframeStyle,
  parentOrigin,
  token,
  saveFilterState,
  setCurrentItem,
  setOpenDeleteModal,
  setEditingAgent,
  setCreateAgentVisible
}) => {
  const [modal, contextHolder] = Modal.useModal()
  const [copyLoading, setCopyLoading] = useState(false)
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  // 改为刷新 Agent 列表
  const queryAgentList = () => queryClient.invalidateQueries([QUERY_KEYS.AGENT_LIST])
  const { mutate: mutateCancelSubscribe } = useCancelSubscribeApi(queryAgentList)
  const { mutate: mutateCopyAgent } = useCopyAgent()
  // 受控复制弹窗相关 state
  const [copyModalVisible, setCopyModalVisible] = useState(false)
  const [copyType, setCopyType] = useState(COPY_TYPE.NORMAL)
  const [copyAgentData, setCopyAgentData] = useState(null)
  const [isAdminUser, setIsAdminUser] = useState(false)
  const [isAdminLoading, setIsAdminLoading] = useState(false)

  const handleEditAgent = (agent) => {
    // 跳转前保存当前筛选状态
    saveFilterState()
    // 材料分类
    if (agent?.agentMode === 3) {
      window.location.href = `${getMaterialUrl()}/agent?botNo=${currentBotNo}&agentNo=${agent.agentNo}&token=${token || ""}`
      return
    }
    // 材料智能采集
    if (agent?.agentMode === 5) {
      window.location.href = `${getMaterialUrl()}/extractAgent?botNo=${currentBotNo}&agentNo=${agent.agentNo}&token=${token || ""}`
      return
    }
    let subscribeSkillQuery = ``
    if (agent.type === "subscribed_agent") {
      subscribeSkillQuery += `&mode=showDetail&agentCanRead=${agent.canRead}`
    }

    navigate(
      `/agent/detail?agentNo=${agent.agentNo}${subscribeSkillQuery}&isIframe=${iframeStyle}&botNo=${currentBotNo}&workbenchNo=${workbenchNo}&parentOrigin=${parentOrigin}&agentMode=${agent?.agentMode || undefined}&originalAgentNo=${agent.originalAgentNo || undefined}`
    )
  }

  const handleDelete = (e, record) => {
    cancelBubble(e)
    setCurrentItem(record)
    setOpenDeleteModal(true)
  }

  const handleEditClick = (e, agent) => {
    cancelBubble(e)
    setEditingAgent(agent)
    setCreateAgentVisible(true)
  }

  const onCancelSubscribe = async (e, agent) => {
    cancelBubble(e)
    return mutateCancelSubscribe({
      botNo: currentBotNo,
      bizType: "AGENT",
      bizNo: agent.bizNo
    })
  }

  // Agent复制功能
  const handleCopyAgent = async (e, agent) => {
    cancelBubble(e)
    setCopyType(COPY_TYPE.NORMAL)
    setCopyAgentData(agent)
    // 只有在 agentMode === 1 且 type === 'single_agent_skill_mode' 时才请求 isAdmin
    if (agent.agentMode == 1 && agent.type === "single_agent_skill_mode") {
      setIsAdminLoading(true)
      try {
        const res = await isAdmin({ botNo: currentBotNo })
        setIsAdminUser(res?.data === true)
      } catch {
        setIsAdminUser(false)
      } finally {
        setIsAdminLoading(false)
        setCopyModalVisible(true)
      }
    } else {
      setCopyModalVisible(true)
    }
  }

  const handleCopyOk = async () => {
    if (!copyAgentData) return
    const botNo = currentBotNo
    const agentNo = copyAgentData.agentNo
    const showCopyType =
      copyAgentData.agentMode === 1 && copyAgentData.type === "single_agent_skill_mode"
    try {
      setCopyLoading(true)
      if (showCopyType && copyType === COPY_TYPE.DEEP) {
        await deepCopyAgent({ botNo, agentNo })
      } else {
        mutateCopyAgent({ botNo, agentNo })
      }
      queryAgentList()
      message.success("复制成功")
      setCopyModalVisible(false)
    } catch (error) {
      message.error(error.message || "复制失败")
    } finally {
      setCopyLoading(false)
    }
  }

  const isValidHttpUrl = (string) => {
    try {
      return string?.startsWith("http://") || string?.startsWith("https://")
    } catch {
      return false
    }
  }

  const iconUrlData = useMemo(() => {
    const url =
      agent.icon === defautOldUrl || agent.icon === defaultOldUrl2 || agent.icon === defaultOldUrl3
        ? defaultImg
        : isValidHttpUrl(agent.icon)
          ? agent.icon
          : agent.icon?.includes("/agentAvater") && !agent.icon?.includes("/agentAvater-new")
            ? defaultOldUrl
            : defaultImg
    return {
      icon: url,
      styles: url === defaultOldUrl ? { width: "30px" } : {}
    }
  }, [agent.icon])

  const deactivate = false

  return (
    <Col xs={24} sm={12} md={12} lg={8} xl={8} xxl={6} key={agent.agentName}>
      <div
        className={`card-item agent-card-item ${"cursor-pointer"} ${deactivate && "deactivate"} relative`} //agent?.agentMode !== 2 &&
        style={{ marginBottom: "20px" }}
        onClick={() => handleEditAgent(agent)} //() => agent?.agentMode !== 2 &&
      >
        {/* Agent类型标识 */}
        {/* <AgentTypeTag agentMode={agent.agentMode} /> */}
        <div className="flex justify-between items-start w-[100%]">
          <div className="flex-1 overflow-hidden">
            <div className="title w-[100%] mr-2 skill-item-text flex !items-start">
              <div className="flex items-center justify-center bg-[#F4F7FF] w-[48px] h-[48px] rounded-[8px]">
                <img
                  style={iconUrlData.styles}
                  src={iconUrlData.icon}
                  alt="agent icon"
                  className="w-[48px]"
                />
              </div>
              <div className="flex flex-1 flex-col items-start overflow-hidden">
                <div className="flex justify-between w-[100%]">
                  <Tooltip title={agent.agentName}>
                    <Text className="text-base">{agent.agentName}</Text>
                  </Tooltip>
                  <div>
                    {agent.type === "subscribed_agent" && (
                      // 兼容订阅技能的显示
                      <span className={`subscribe-status ${agent.status === true && "using"}`}>
                        {agent.status === true ? "启用中" : "已停用"}
                      </span>
                    )}
                  </div>
                </div>
                <CopyToClipboard text={agent.agentNo} onCopy={() => message.success("复制成功")}>
                  <Tooltip title="点击复制Agent编号">
                    <span
                      className="text-[#626263] cursor-pointer leading-[18px] text-[12px] mt-[8px]"
                      onClick={(e) => cancelBubble(e)}
                    >
                      {agent.agentNo}
                    </span>
                  </Tooltip>
                </CopyToClipboard>
              </div>
            </div>
            <div className="skill-item-text belongTo-botName mt-[12px]">
              <div className="time-tag">
                <span className="title">类型</span>
                <span className="time">
                  {agent.type === "subscribed_agent"
                    ? "订阅Agent"
                    : AGENT_MODE_OPTIONS.find((v) => v.value === agent.agentMode)?.label?.replace(
                        "Agent",
                        ""
                      ) || "--"}
                </span>
              </div>
              <div className="time-tag">
                <span className="title">更新时间</span>
                <span className="time">
                  {agent?.gmtModified ? agent?.gmtModified?.split(" ")?.[0] : "--"}
                </span>
              </div>
              {agent?.belongToBotName && (
                <div className="time-tag">
                  <span className="title">来源</span>
                  <span className="time">{agent?.belongToBotName}</span>
                </div>
              )}
              {agent?.subscribeCount > 0 && (
                <div className="time-tag">
                  <span className="title">订阅</span>
                  <span className="time">{agent?.subscribeCount}</span>
                </div>
              )}
            </div>
            <Tooltip title={agent.description}>
              <Text className="desc">{agent.description || "这个人很懒，暂未填写描述～"}</Text>
            </Tooltip>
          </div>
        </div>
        <Divider className="!my-[12px] !mt-[18px]" />
        <div className="bottom-center">
          <div className="remove-btn">
            {agent.type === "subscribed_agent" ? (
              // 订阅技能
              <div className="remove-btn-insert">
                <div
                  className="px-[12%] w-[100%] remove-btn-insert-sub"
                  style={{ justifyContent: "center" }}
                >
                  {agent.isShare && (
                    <span className="subscribe-count">
                      <StarOutlined />
                      <span className="count-text">{agent.subscribeCount}</span>
                    </span>
                  )}
                  <Popconfirm
                    title=""
                    description="【取消订阅】后，将无法调用对应技能"
                    onConfirm={(e) => onCancelSubscribe(e, agent)}
                    onPopupClick={(e) => cancelBubble(e)}
                    okText="是"
                    cancelText="否"
                  >
                    <Button
                      className="p-0 !text-[#181B25] !hover:text-[#7F56D9] text-[12px]"
                      type="link"
                      onClick={(e) => cancelBubble(e)}
                    >
                      <i className="iconfont icon-dingyue align-middle -mt-[1px] mr-[3px] text-[#F6B51E]"></i>
                      取消订阅
                    </Button>
                  </Popconfirm>
                </div>
              </div>
            ) : (
              <div className="remove-btn-insert">
                <Button
                  type="link"
                  className="p-0 !text-[#181B25] !hover:text-[#7F56D9] text-[12px]"
                  // disabled={agent?.agentMode === 2}
                >
                  <i className="iconfont icon-bianji1 text-[#98A2B3] align-middle -mt-[1px] mr-[3px]"></i>
                  编辑
                </Button>
                <Button
                  type="link"
                  onClick={(e) => handleEditClick(e, agent)}
                  className="p-0 !text-[#181B25] !hover:text-[#7F56D9] text-[12px]"
                >
                  <i className="iconfont icon-shezhi text-[#98A2B3] align-middle -mt-[1px] mr-[3px]"></i>
                  设置
                </Button>
                {[1, 2].includes(agent?.agentMode) && (
                  <Button
                    type="link"
                    loading={copyLoading}
                    onClick={(e) => handleCopyAgent(e, agent)}
                    className="p-0 !text-[#181B25] !hover:text-[#7F56D9] text-[12px]"
                  >
                    <i className="iconfont icon-fuzhi text-[#98A2B3] text-[22px] align-middle -mt-[0px] mr-[1px]"></i>
                    复制
                  </Button>
                )}
                <Button
                  className="p-0  !text-[#181B25]  text-[12px]"
                  type="link"
                  onClick={(e) => handleDelete(e, agent)}
                >
                  <i className="iconfont icon-shanchu1 text-[#98A2B3] align-middle -mt-[1px] mr-[3px]"></i>
                  删除
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {contextHolder}

      {/* 受控复制弹窗 */}
      <Modal
        open={copyModalVisible}
        title="复制确认"
        onOk={handleCopyOk}
        onCancel={() => setCopyModalVisible(false)}
        okText="确认"
        cancelText="取消"
        confirmLoading={copyLoading}
      >
        <div>确定要复制这个Agent吗？</div>
        {copyAgentData &&
          copyAgentData.agentMode == 1 &&
          copyAgentData.type === "single_agent_skill_mode" &&
          isAdminUser && (
            <div className="mt-3 bg-gray-100 px-4 p-2 pb-3 rounded-md">
              <Radio.Group
                value={copyType}
                onChange={(e) => setCopyType(e.target.value)}
                className="flex flex-col gap-4 mt-2"
              >
                <Radio value={COPY_TYPE.NORMAL}>
                  仅复制Agent
                  <Tooltip title="技能/常量变更，Agent 同步更新">
                    <span className="ml-1 text-gray-500 cursor-pointer">
                      <i className="iconfont icon-yiwen text-gray-500 text-[16px]align-middle mt-1 mr-[3px]"></i>
                    </span>
                  </Tooltip>
                </Radio>
                <Radio value={COPY_TYPE.DEEP}>
                  全链路复制Agent
                  <Tooltip title="仅保存复制当下信息，不随技能/常量作同步变更">
                    <span className="ml-1 text-gray-500 cursor-pointer">
                      <i className="iconfont icon-yiwen text-gray-500 text-[16px] align-middle mt-1 mr-[3px]"></i>
                    </span>
                  </Tooltip>
                </Radio>
              </Radio.Group>
            </div>
          )}
      </Modal>
    </Col>
  )
}

export default AgentItem
