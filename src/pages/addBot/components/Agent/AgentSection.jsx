import { useState, useCallback, useEffect } from "react"
import { Row, Empty } from "antd"
import DeleteModal from "@/components/DeleteModal/DeleteModal"
import { useDeleteAgentByNo } from "@/api/agent"
import AgentItem from "./AgentItem"
import { AGENT_FILTER_STATE_KEY } from "./index"

const AgentSection = ({
  agents,
  tabValue,
  isComposing,
  filterInputValue,
  selectedAgentType,
  agentTypeOptions,
  currentBotNo,
  workbenchNo,
  iframeStyle,
  parentOrigin,
  token,
  searchExpand,
  setEditingAgent,
  setCreateAgentVisible
}) => {
  const [currentItem, setCurrentItem] = useState({ agentName: "", agentNo: "" })
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const { mutate: deleteAgent } = useDeleteAgentByNo()

  const confirmCallback = useCallback(async () => {
    deleteAgent({ botNo: currentBotNo, agentNo: currentItem.agentNo })
  }, [currentItem, currentBotNo, deleteAgent])

  // 保存筛选状态到 sessionStorage
  const saveFilterState = useCallback(() => {
    const filterState = {
      filterInputValue,
      selectedAgentType,
      searchExpand,
      tabValue,
      botNo: currentBotNo,
      timestamp: Date.now()
    }
    sessionStorage.setItem(AGENT_FILTER_STATE_KEY, JSON.stringify(filterState))
  }, [filterInputValue, selectedAgentType, searchExpand, tabValue, currentBotNo])

  // 监听筛选条件变化，自动保存状态（延迟保存，避免初始化时立即覆盖）
  useEffect(() => {
    const timer = setTimeout(() => {
      saveFilterState()
    }, 1000) // 1秒延迟，确保状态恢复完成后再开始自动保存

    return () => clearTimeout(timer)
  }, [saveFilterState])

  if (!agents?.length) return null

  let agentsList = agents.filter((agent) => {
    if (tabValue === "2") return true
    // 文本筛选
    if (
      !isComposing &&
      filterInputValue &&
      !agent.agentName?.includes(filterInputValue) &&
      !agent.description?.includes(filterInputValue) &&
      !agent.agentNo?.includes(filterInputValue)
    ) {
      return false
    }

    // 类型筛选
    if (selectedAgentType !== "all" && agent.agentMode !== selectedAgentType) {
      return false
    }

    return true
  })

  return (
    <>
      <div>
        <Row gutter={20}>
          {agentsList.map((agent) => (
            <AgentItem
              key={agent.agentNo}
              agent={agent}
              currentBotNo={currentBotNo}
              workbenchNo={workbenchNo}
              iframeStyle={iframeStyle}
              parentOrigin={parentOrigin}
              token={token}
              saveFilterState={saveFilterState}
              setCurrentItem={setCurrentItem}
              setOpenDeleteModal={setOpenDeleteModal}
              setEditingAgent={setEditingAgent}
              setCreateAgentVisible={setCreateAgentVisible}
            />
          ))}
        </Row>
        {agentsList.length === 0 && (filterInputValue || selectedAgentType !== "all") && (
          <div className="mt-[50px]">
            <Empty
              description={
                filterInputValue && selectedAgentType !== "all"
                  ? `未找到匹配关键词 "${filterInputValue}" 且类型为 "${agentTypeOptions.find((opt) => opt.value === selectedAgentType)?.label}" 的Agent`
                  : filterInputValue
                    ? `未搜索到匹配关键词 "${filterInputValue}" 的Agent`
                    : `未找到类型为 "${agentTypeOptions.find((opt) => opt.value === selectedAgentType)?.label}" 的Agent`
              }
            />
          </div>
        )}
      </div>
      <DeleteModal
        title={<span>删除</span>}
        desc={
          <div>
            <p className="text-[#475467]">删除Agent是一个重要操作,是否确定删除</p>
            <br />
            <p className="-mt-[20px] mb-[10px]">
              请输入Agent名称 <b style={{ color: "red" }}>{currentItem.agentName} </b>以确认
            </p>
          </div>
        }
        placeholder="请输入Agent名称"
        confirmText={currentItem.agentName}
        openDeleteModal={openDeleteModal}
        setOpenDeleteModal={setOpenDeleteModal}
        confirmCallback={confirmCallback}
      />
    </>
  )
}

export default AgentSection
