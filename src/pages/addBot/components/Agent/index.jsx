import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react"
import { <PERSON>bs, Button, Row, Col, Spin, Space, Input, Select } from "antd"
import { PlusOutlined, SearchOutlined } from "@ant-design/icons"
import { useFetchAgentList, useUpdateAgent } from "@/api/agent"
import { useFetchSubscribeSkillListByPage } from "@/api/skill"
import { useQueryClient } from "@tanstack/react-query"
import { debounce, throttle } from "lodash"
import queryString from "query-string"
import CustomEmpty from "@/antd-styles/components/CustomEmpty"
import { QUERY_KEYS } from "@/constants/queryKeys"
import CreateAgent, { AGENT_MODE_OPTIONS } from "@/components/CreateAgent"
import AgentSection from "./AgentSection"
import SubscribeAgentModal from "./SubscribeAgentModal"
import "../SkillList.scss"

// 状态保持的 key
export const AGENT_FILTER_STATE_KEY = "agent_filter_state"

const Agent = ({ currentBotNo, workbenchNo, parentOrigin, iframeStyle = false, token }) => {
  const [filterInputValue, setFilterInputValue] = useState("")
  const [isComposing, setIsComposing] = useState(false)
  const [createAgentVisible, setCreateAgentVisible] = useState(false)
  const [editingAgent, setEditingAgent] = useState(null)
  const [searchExpand, setSearchExpand] = useState(true)
  const [selectedAgentType, setSelectedAgentType] = useState("all") // 类型筛选状态
  // 我的技能和我的订阅切换
  const [tabValue, setTabValue] = useState("1")

  // 从 sessionStorage 恢复筛选状态
  const restoreFilterState = useCallback(() => {
    try {
      const savedState = sessionStorage.getItem(AGENT_FILTER_STATE_KEY)
      if (savedState) {
        const filterState = JSON.parse(savedState)
        // 检查状态是否过期（30分钟）
        const isExpired = Date.now() - filterState.timestamp > 30 * 60 * 1000
        if (!isExpired && filterState.botNo === currentBotNo) {
          setFilterInputValue(filterState.filterInputValue || "")
          setSelectedAgentType(filterState.selectedAgentType || "all")
          setSearchExpand(filterState.searchExpand || false)
          setTabValue(filterState.tabValue || "1")
        } else {
          // 清除过期状态
          sessionStorage.removeItem(AGENT_FILTER_STATE_KEY)
          setFilterInputValue("")
          setSelectedAgentType("all")
          setTabValue("1")
        }
      }
    } catch (error) {
      console.error("Failed to restore filter state:", error)
      sessionStorage.removeItem(AGENT_FILTER_STATE_KEY)
    }
  }, [currentBotNo])

  // 组件挂载时恢复状态
  useEffect(() => {
    restoreFilterState()
  }, [restoreFilterState])

  const onCompositionStart = useCallback(() => setIsComposing(true), [])
  const onCompositionEnd = useCallback(() => {
    setIsComposing(false)
  }, [])

  const queryClient = useQueryClient()

  const { mutate: updateAgent } = useUpdateAgent()

  const { data: agentList = [], isLoading } = useFetchAgentList({
    botNo: currentBotNo,
    agentName: filterInputValue || undefined
  })

  const { data: subscribedAgentData = {}, isLoading: subscribedAgentLoading } =
    useFetchSubscribeSkillListByPage({
      botNo: currentBotNo,
      bizType: "AGENT",
      pageSize: 100,
      pageNum: 1
    })

  const subscribedAgentTemp = useMemo(
    () => ({
      ...subscribedAgentData,
      list: (subscribedAgentData.list || []).map((i) => ({
        ...i,
        agentName: i.name, // 前端添加字段
        agentNo: i.bizNo, // 前端添加字段
        type: "subscribed_agent" // 前端添加字段
      }))
    }),
    [subscribedAgentData]
  )

  const agentData = useMemo(
    () => (tabValue === "1" ? agentList || [] : subscribedAgentTemp.list),
    [tabValue, agentList, subscribedAgentTemp]
  )

  // 根据agentData动态生成类型选项
  const agentTypeOptions = useMemo(() => {
    const typeMap = new Map()

    // 遍历agentData，收集所有存在的类型
    agentData.forEach((agent) => {
      if (agent.agentMode) {
        const typeKey = agent.agentMode
        const typeName =
          AGENT_MODE_OPTIONS.find((v) => v.value === agent.agentMode)?.label?.replace(
            "Agent",
            ""
          ) || "其他"

        if (!typeMap.has(typeKey)) {
          typeMap.set(typeKey, typeName)
        }
      }
    })

    // 转换为选项数组，添加"全部"选项
    const options = [{ label: "全部类型", value: "all" }]

    // 按照固定顺序添加类型选项
    AGENT_MODE_OPTIONS.forEach((option) => {
      if (typeMap.has(option.value)) {
        options.push({ label: option.label?.replace("Agent", ""), value: option.value })
      }
    })

    // 添加其他未知类型
    typeMap.forEach((typeName, typeKey) => {
      if (!AGENT_MODE_OPTIONS.map((v) => v.value).includes(typeKey)) {
        options.push({ label: typeName, value: typeKey })
      }
    })

    return options
  }, [agentData])

  const handleCreateAgentClose = () => {
    setCreateAgentVisible(false)
    setEditingAgent(null)
  }

  // const onSearchInputChange = useCallback(
  //   debounce((e) => {
  //     const value = e.target.value
  //     console.log("value", value)
  //     if (!isComposing) {
  //       setFilterInputValue(e.target.value)
  //     }
  //   }, 500),
  //   [isComposing]
  // )

  // 防抖的筛选函数
  const debouncedSetFilter = useCallback(
    debounce((value) => {
      if (!isComposing) {
        setFilterInputValue(value)
      }
    }, 300),
    [isComposing]
  )

  // 处理输入框值变化
  const handleInputChange = (e) => {
    const value = e.target.value
    // 实时更新输入框显示值
    setFilterInputValue(value)
    // 延迟更新筛选条件（如果需要的话，这里可以用于API调用等）
    // debouncedSetFilter(value)
  }

  // 清除筛选状态
  const clearFilterState = useCallback(() => {
    sessionStorage.removeItem(AGENT_FILTER_STATE_KEY)
  }, [])

  const toggleSearch = () => {
    setSearchExpand(!searchExpand)
  }

  // 监听滚动
  const [pageScrollTop, setPageScrollTop] = useState(0)
  const scrollChange = () => {
    // 监听滚动条距离顶部距离
    setPageScrollTop(document.documentElement.scrollTop || 0)
  }

  useEffect(() => {
    // 滚动条滚动时触发
    window.addEventListener(
      "scroll",
      throttle(() => {
        scrollChange()
      }, 300),
      true
    )
    scrollChange()
    return () => {
      window.removeEventListener("scroll", scrollChange, false)
    }
  }, [])

  // 组件卸载时的清理（可选，如果希望在特定情况下清除状态）
  useEffect(() => {
    return () => {
      // 如果需要在组件卸载时清除状态，可以取消注释下面的代码
      // clearFilterState()
    }
  }, [])

  const searchParams = queryString.parse(window.location.search) || {}
  const hashParams = queryString.parse(window.location.hash.split("?")?.[1] || "") || {}
  const { token: isIframe } = searchParams.token ? searchParams : hashParams

  return (
    <div className="skill-list-wrapper-v2">
      <div className={`skill-list-wrapper ${iframeStyle && "iframeStyle"}`}>
        <Tabs
          activeKey={tabValue}
          className={`${!isIframe ? "tab-skill-no-iframe" : "tab-skill"} ${pageScrollTop > 10 && isIframe ? "tab-skill-shadow" : ""}`}
          style={tabValue === "2" ? { paddingBottom: 0 } : {}}
          onChange={(active) => {
            setTabValue(active)
          }}
          items={[
            {
              key: "1",
              label: "我的Agent",
              children: (
                <div className="bg-white">
                  <Row className="header" justify="space-between">
                    <Col>
                      <Space>
                        <div>
                          <Button
                            type="primary"
                            className="h-[36px]"
                            icon={<PlusOutlined />}
                            onClick={() => {
                              setCreateAgentVisible(true)
                            }}
                          >
                            创建 Agent
                          </Button>
                        </div>
                      </Space>
                    </Col>
                    <Col className="flex items-center">
                      <Select
                        value={selectedAgentType}
                        onChange={setSelectedAgentType}
                        options={agentTypeOptions}
                        style={{ width: 200, marginRight: 12 }}
                        size="middle"
                        placeholder="选择类型"
                      />
                      <Input
                        placeholder="搜索Agent名称，按 Enter 确认"
                        value={filterInputValue}
                        onChange={handleInputChange}
                        onCompositionStart={onCompositionStart}
                        onCompositionEnd={onCompositionEnd}
                        onPressEnter={handleInputChange}
                        suffix={
                          <SearchOutlined
                            style={{ color: "#bfbfbf", cursor: "pointer" }}
                            onClick={handleInputChange}
                          />
                        }
                      />
                    </Col>
                  </Row>
                </div>
              )
            },
            {
              key: "2",
              label: "我的订阅"
            }
          ]}
        />

        <Spin spinning={isLoading || subscribedAgentLoading}>
          <div
            className={`${isIframe ? (tabValue === "1" ? "mt-[106px]" : "mt-[50px]") : "mt-[20px]"}`}
          >
            {agentData?.length ? (
              <AgentSection
                agents={agentData}
                tabValue={tabValue}
                isComposing={isComposing}
                filterInputValue={filterInputValue}
                selectedAgentType={selectedAgentType}
                agentTypeOptions={agentTypeOptions}
                currentBotNo={currentBotNo}
                workbenchNo={workbenchNo}
                iframeStyle={iframeStyle}
                parentOrigin={parentOrigin}
                token={token}
                searchExpand={searchExpand}
                setEditingAgent={setEditingAgent}
                setCreateAgentVisible={setCreateAgentVisible}
              />
            ) : (
              <div className="mt-[38vh]">
                <CustomEmpty
                  description={tabValue === "1" ? `您还未添加任何Agent` : `暂未订阅任何Agent`}
                />
              </div>
            )}
          </div>
        </Spin>
        <CreateAgent
          visible={createAgentVisible && tabValue === "1"}
          currentBotNo={currentBotNo}
          initialValues={editingAgent}
          onClose={handleCreateAgentClose}
        />
        <SubscribeAgentModal
          visible={createAgentVisible && tabValue !== "1"}
          botNo={currentBotNo}
          initialValues={editingAgent}
          onClose={handleCreateAgentClose}
          onSuccess={() => {
            queryClient.invalidateQueries([
              editingAgent?.type === "subscribed_agent"
                ? QUERY_KEYS.SUBSCRIBE_SKILL_LIST_BY_PAGE
                : QUERY_KEYS.AGENT_LIST
            ])
          }}
        />
      </div>
    </div>
  )
}

export default Agent
