.avatar-item {
  width: 44px;
  height: 44px;
  border-radius: 100%;
  cursor: pointer;
  padding: 5px;
  border: 1px solid #dadce2;
  position: relative;
  overflow: hidden;

  &.selected {
    // box-shadow: 0 0 25px #7F56D9;
    position: relative;
    overflow: unset;
    border-radius: 100%;
  }
  &.selected::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border: 2px solid #4b4eea;
    width: 100%;
    height: 100%;
    border-radius: 100%;
  }
  .avatar {
    width: 100%;
    height: 100%;
    border-radius: 100%;
  }
}
