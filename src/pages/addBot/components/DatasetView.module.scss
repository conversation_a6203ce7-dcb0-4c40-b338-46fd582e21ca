.content {
  display: flex;
  width: 100%;
  gap: 20px;
  margin-top: 10px;
}

.leftPane-wrapper {
  flex: 4;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.leftPane {
  height: calc(100vh - 220px);
  overflow-y: auto;
}

.rightPane {
  flex: 1;

  p {
    margin-bottom: 10px;
  }
}

.record {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 4px;

  .id {
    background-color: #EAEDF3;

  }

  div {
    padding: 9px 10px;
  }

  b {
    font-weight: 700;
    min-width: 100px;
    display: inline-block;
  }

  span {
    color: gray;
    font-weight: 500;
  }
}

.ant-divider-horizontal {
  margin: 10px 0;
}

h2 {
  color: #000;
  font-size: 24px;
  margin-bottom: 10px;
}

.deleteWrapper {
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: fixed;
  bottom: 80px;
  left: 50%;
  margin-left: -10%;
  transform: translateX(-50%);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  color: #999;
  background: #fff;
  font-size: 14px;
  animation-duration: 0.4s;
  animation-fill-mode: both;
  visibility: hidden;

  &.hide {
    animation-name: slideOut;
  }

  &.show {
    animation-name: slideIn;
  }

  :global {
    .ant-btn {
      margin: 0 30px;
    }

    .anticon-close {
      cursor: pointer;
      color: #000;
    }
  }

  @keyframes slideOut {
    from {
      transform: translate(-50%, 0);
      opacity: 1;
      visibility: visible;
    }
    to {
      transform: translate(-50%, 100%);
      opacity: 0;
      visibility: hidden;
    }
  }

  @keyframes slideIn {
    from {
      transform: translate(-50%, 100%);
      opacity: 0;
      visibility: hidden;
    }
    to {
      transform: translate(-50%, 0);
      opacity: 1;
      visibility: visible;
    }
  }
}

.structure-data-modal-form {
  label {
    height: auto !important;

  }

}
