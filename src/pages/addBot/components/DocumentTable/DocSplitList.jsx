import { useState, useEffect } from "react"
import { Button, <PERSON>, Switch, Popconfirm, Tooltip, Modal, Form, Input } from "antd"
import {
  useFetchPageSharing,
  useDeleteDocumentSharding,
  useEditDocumentSharding
} from "@/api/knowledgeDocument"
import "./index.scss"

const DocSplitList = ({ visible, knowledgeBaseNo, currentData }) => {
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 })
  const [shardingNo, setShardingNo] = useState("")
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editForm] = Form.useForm()
  const { data, isLoading } = useFetchPageSharing({
    knowledgeBaseNo,
    catalogNo: currentData?.catalogNo,
    documentNo: currentData?.documentNo,
    pageNum: pagination?.current ?? 1,
    pageSize: pagination?.pageSize ?? 10
  })
  const { mutate: deleteDocumentSharding, isLoading: isDeleteLoading } = useDeleteDocumentSharding()
  const { mutate: editDocumentSharding, isLoading: isEditLoading } = useEditDocumentSharding()

  useEffect(() => {
    if (!visible) {
      setPagination((preState) =>
        preState?.current === 1 && preState?.pageSize === 10
          ? preState
          : { current: 1, pageSize: 10 }
      )
      setShardingNo("")
    }
  }, [visible])

  return (
    <div className="mr-[4px] bg-white h-[100%] rounded-tl-[8px] rounded-tr-[8px]">
      <div className="rag-flow-header">文档拆分预览</div>
      <List
        loading={isLoading}
        className="rag-flow-list"
        pagination={{
          size: "small",
          current: pagination?.current ?? 1,
          pageSize: pagination?.pageSize ?? 10,
          total: data?.total ?? 0,
          pageSizeOptions: [10, 20, 60, 100],
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: (page, pageSize) => {
            setPagination({ current: page, pageSize })
          }
        }}
        dataSource={data?.records}
        renderItem={(item) => (
          <List.Item
            actions={[
              <Tooltip title={`${item.status === 8 ? "停用切片" : "启用切片"}`}>
                <Switch
                  loading={isEditLoading}
                  onChange={(checked) =>
                    editDocumentSharding({
                      knowledgeBaseNo,
                      catalogNo: item.catalogNo,
                      documentNo: item.documentNo,
                      shardingNo: item.shardingNo,
                      shardingContent: item.content,
                      status: checked ? 8 : 9
                    })
                  }
                  size="small"
                  checked={item.status === 8}
                  disabled={currentData.status === 8}
                />
              </Tooltip>,
              <Tooltip title={"编辑切片"}>
                <Button
                  onClick={() => {
                    setShardingNo(item.shardingNo)
                    editForm.setFieldsValue({ content: item.content })
                    setIsModalVisible(true)
                  }}
                  disabled={currentData.status === 8}
                  type="link"
                  icon={<span className="iconfont icon-Edit" />}
                />
              </Tooltip>,
              <Popconfirm
                title="是否确定删除该切片？"
                okButtonProps={{ loading: isDeleteLoading }}
                onConfirm={() => {
                  deleteDocumentSharding({
                    knowledgeBaseNo,
                    catalogNo: item.catalogNo,
                    documentNo: item.documentNo,
                    shardingNo: item.shardingNo
                  })
                }}
              >
                <Tooltip title={"删除切片"}>
                  <Button
                    disabled={currentData.status === 8}
                    type="link"
                    icon={<span className="iconfont icon-shanchu1" />}
                  />
                </Tooltip>
              </Popconfirm>
            ]}
          >
            <List.Item.Meta description={item.content} />
          </List.Item>
        )}
      />
      <Modal
        title={"编辑"}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        okText={"确认"}
        width={720}
        closable={false}
        confirmLoading={isEditLoading}
        onOk={async () => {
          const values = await editForm.validateFields()
          const curData = data?.records?.find((item) => item.shardingNo === shardingNo)
          editDocumentSharding(
            {
              knowledgeBaseNo,
              catalogNo: curData?.catalogNo,
              documentNo: curData?.documentNo,
              shardingNo,
              shardingContent: values.content,
              status: curData?.status
            },
            {
              onSuccess: () => {
                setIsModalVisible(false)
                editForm.resetFields()
              }
            }
          )
        }}
      >
        <Form form={editForm}>
          <Form.Item name="content" rules={[{ required: true, message: "请输入切片内容" }]}>
            <Input.TextArea placeholder="请输入切片内容" rows={10} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DocSplitList
