import { useEffect, useRef, useMemo } from "react"
import {
  Input,
  Select,
  Form,
  Modal,
  Radio,
  Upload,
  message,
  TreeSelect,
  notification,
  Slider,
  InputNumber
} from "antd"
import { InfoCircleOutlined } from "@ant-design/icons"
import { useAddDocument, useEditDocument, useFetchKnowledgeDictionaryList } from "@/api/knowledge"
import { uploadDocumentKnowledgeUrl } from "@/api/knowledge/api"
import { useQueryClient } from "@tanstack/react-query"
import { QUERY_KEYS } from "@/constants/queryKeys"
import UploadFile from "@/components/UploadFile"
import docIcon from "@/assets/img/doc.png"
import docxIcon from "@/assets/img/docx.png"
import pdfIcon from "@/assets/img/pdf.png"
import txtIcon from "@/assets/img/txt.png"

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B"
  const k = 1024
  const sizes = ["B", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

const getFileIcon = (file) => {
  const extension = file.name?.split(".")?.pop()?.toLowerCase()
  switch (extension) {
    case "doc":
      return docIcon
    case "docx":
      return docxIcon
    case "pdf":
      return pdfIcon
    case "txt":
      return txtIcon
    default:
      return null
  }
}

// 将文件扩展名转换为accept属性需要的格式
function getAcceptTypes(extraInfoStr) {
  try {
    const extensions = JSON.parse(extraInfoStr)
    return extensions.map((ext) => `.${ext.toLowerCase()}`).join(",")
  } catch (e) {
    // 如果解析失败，返回默认值
    return ".doc,.docx,.pdf,.txt,.md"
  }
}

// 获取允许的MIME类型列表
function getAllowedMimeTypes(extraInfoStr) {
  try {
    const extensions = JSON.parse(extraInfoStr)
    const mimeTypeMap = {
      DOCX: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      DOC: "application/msword",
      XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      XLS: "application/vnd.ms-excel",
      PPT: "application/vnd.ms-powerpoint",
      PDF: "application/pdf",
      TXT: "text/plain",
      JPEG: "image/jpeg",
      JPG: "image/jpeg",
      PNG: "image/png",
      TIF: "image/tiff",
      GIF: "image/gif",
      CSV: "text/csv",
      JSON: "application/json",
      EML: "message/rfc822",
      HTML: "text/html",
      MD: "text/markdown"
    }

    return extensions.map((ext) => mimeTypeMap[ext.toUpperCase()]).filter(Boolean)
  } catch (e) {
    // 如果解析失败，返回默认值
    return [
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/pdf",
      "text/plain",
      "text/markdown"
    ]
  }
}

const EditDocModal = ({
  selectedKnowledgeBase,
  selectNode,
  docCategories,
  docAction,
  isModalVisible,
  setIsModalVisible,
  currentData
}) => {
  const currentRecordRef = useRef(null)
  const documentNoRef = useRef(null)

  const [form] = Form.useForm()
  const queryClient = useQueryClient()
  const { mutate: addDocument } = useAddDocument()
  const { mutate: editDocument } = useEditDocument()
  const { data: knowledgeDictionaryList } = useFetchKnowledgeDictionaryList({
    dictionaryType: "RAG_CHUNK_METHOD"
  })
  const splitMethodCode = Form.useWatch("splitMethodCode", form)
  const splitMethod = Form.useWatch("splitMethod", form)

  const extraInfo = useMemo(() => {
    return knowledgeDictionaryList?.find((item) => item.code === splitMethod)?.extraInfo || "[]"
  }, [knowledgeDictionaryList, splitMethod])

  const extraInfoText = useMemo(
    () =>
      JSON.parse(extraInfo || "[]")
        .map((ext) => ext.toLowerCase())
        .join("，"),
    [extraInfo]
  )

  const isInstruction = useMemo(
    () => splitMethodCode === "SPLIT_BY_RAG_FOR_INSURANCE_PRODUCT",
    [splitMethodCode]
  )

  useEffect(() => {
    if (isModalVisible) {
      documentNoRef.current = currentData?.documentNo || null
      currentRecordRef.current = currentData || null
      currentData &&
        form.setFieldsValue({
          catalogNo: currentData.catalogNo,
          title: currentData.title,
          splitMethodCode: currentData.splitMethodCode,
          summary: currentData.summary,
          files: [{ path: currentData.url, title: currentData.title }],
          splitMethod: currentData.splitArg?.splitMethod,
          chunkSize: currentData.splitArg?.chunkSize
        })
    }
  }, [currentData, isModalVisible, form])

  const handleOk = () => {
    console.log(form.getFieldsValue())
    form
      .validateFields()
      .then(({ splitMethod, chunkSize, ...values }) => {
        // 使用正则表达式替换，确保将 "\\n" 字符串强制转换为真正的换行符 "\n"
        if (typeof values.splitSymbol === "string") {
          values.splitSymbol = values.splitSymbol.replace(/\\n/g, "\n")
        }

        const params = {
          ...values,
          knowledgeBaseNo: selectedKnowledgeBase,
          files:
            docAction === "edit"
              ? values.files
              : values?.files.map((path) => ({
                  title:
                    !isInstruction && values.title
                      ? values.title
                      : path.name.split(".").slice(0, -1).join("."),
                  path: path.response.data
                })),
          // path: docAction === "edit" ? values.path : values?.path[0].response.data,
          documentNo: documentNoRef.current,
          splitArg: { splitMethod, chunkSize }
        }
        // 如果是编辑
        const isEdit = docAction === "edit"
        if (isEdit) {
          // 目标路径就是当前输入框的路径
          params.targetCatalogNo = values.catalogNo
          // 知识库路径是原来的路径
          params.catalogNo = currentRecordRef.current.catalogNo
        }

        const fetch = isEdit ? editDocument : addDocument
        fetch(params, {
          onSuccess: (e) => {
            if (e.success) {
              message.success(e.message)
              queryClient.invalidateQueries([QUERY_KEYS.DOCUMENT_LIST_BY_PAGE])
            } else {
              notification.warning({
                message: "出错了",
                description: e.message
              })
            }
          }
        })

        setIsModalVisible(false)
        form.resetFields()
      })
      .catch((info) => {
        console.log("Validate Failed:", info)
      })
  }

  const handleUploadChange = (info) => {
    if (info.file.status === "done") {
      const response = info.file.response
      if (response) {
        if (response.success) {
          // Success case
          if (!form.getFieldValue("title")) {
            form.setFieldsValue({
              title: info.file.name.split(".").slice(0, -1).join(".")
            })
          }
        } else {
          // Business error from server
          message.error(response.message || "服务器返回未知错误。")
        }
      }
    } else if (info.file.status === "error") {
      // Network error
      message.error(info.file.response?.message || "请检查网络或联系管理员")
    }
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  return (
    <Modal
      title={docAction === "add" ? "添加文档" : docAction === "edit" ? "编辑文档" : "查看文档"}
      open={isModalVisible}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose
      afterClose={() => {
        form.resetFields()
      }}
    >
      <Form form={form} initialValues={{}} layout="vertical">
        <Form.Item
          name="catalogNo"
          label="文档目录"
          rules={[{ required: true, message: "请选择文档目录!" }]}
          initialValue={selectNode?.catalogNo}
        >
          <TreeSelect
            showSearch
            style={{
              width: "100%"
            }}
            dropdownStyle={{
              maxHeight: 400,
              overflow: "auto"
            }}
            placeholder="请选择文档目录"
            allowClear
            treeDefaultExpandAll
            fieldNames={{ label: "catalogName", value: "catalogNo" }}
            treeData={docCategories}
          />
        </Form.Item>
        <Form.Item
          name="splitMethodCode"
          label="拆分方式"
          initialValue="SPLIT_BY_RAG_FOR_INSURANCE_PRODUCT"
          rules={[{ required: true, message: "请选择拆分方式!" }]}
          hidden
        >
          <Radio.Group disabled={docAction === "edit"}>
            <Radio value="SPLIT_BY_BLOCK">通用拆分</Radio>
            <Radio value="SPLIT_BY_TITLE">按标题拆分</Radio>
            <Radio value="SPLIT_BY_RAG_FOR_INSURANCE_PRODUCT">保险产品说明书</Radio>
          </Radio.Group>
        </Form.Item>
        {!isInstruction && (
          <Form.Item
            name="title"
            label="文档标题"
            rules={[{ required: true, message: "请输入文档标题!" }]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>
        )}
        {isInstruction && (
          <>
            <Form.Item
              name="splitMethod"
              label="切片方法"
              rules={[{ required: true, message: "请选择切片方式!" }]}
              initialValue="NAIVE"
              tooltip={{
                overlayInnerStyle: { wordBreak: "break-all", maxWidth: "600px" },
                icon: <InfoCircleOutlined />,
                title: (
                  <>
                    &quot;GENERAL&quot;分块方法说明
                    <br />
                    支持的文件格式为DOCX、XLSX、XLS(Excel97~2003)、PPT、PDF、TXT、JPEG、JPG、PNG、TIF、GIF、CSV、JSON、EML、HTML。
                    <br />
                    此方法将简单的方法应用于块文件：系统将使用视觉检测模型将连续文本分割成多个片段；接下来，这些连续的片段被合并成Token数不超过&quot;Token数&quot;的块。
                    <br />
                    <br />
                    &quot;Q&A&quot;分块方法说明
                    <br />
                    此块方法支持 excel和 csv/txt 文件格式。
                    <br />
                    如果文件是
                    excel格式，则应由两个列组成，没有标题：一个提出问题，另一个用于答案，答案列之前的问题列。多张纸只要列是正确结构，就可以接受；如果文件是
                    csv/txt 格式，以 UTF-8 编码且用 TAB 作分开问题和答案的定界符。
                    <br />
                    未能遵循上述规则的文本行将被忽略，并且每个问答对将被认为是一个独特的部分。
                    <br />
                    <br />
                    &quot;RESUME&quot;分块方法说明
                    <br />
                    支持的文件格式为 DOCX、PDF、TXT。
                    <br />
                    简历有多种格式，就像一个人的个性一样，但我们经常必须将它们组织成结构化数据，以便于搜索。我们不是将简历分块，而是将简历解析为结构化数据。作为HR、你可以扔掉所有的简历，您只需与&quot;RAGFIow&quot;交谈即可列出所有符合资格的候选人。
                    <br />
                    <br />
                    &quot;MANUAL&quot;分块方法说明
                    <br />
                    仅支持PDF。
                    <br />
                    我们假设手册具有分层部分结构。我们使用最低的部分标题作为对文档进行切片的枢轴。因此，同一部分中的图和表不会被分割，并且块大小可能会很大。
                    <br />
                    <br />
                    &quot;TABLE&quot;分块方法说明
                    <br />
                    支持XLSX和CSV/TXT格式文件。
                    <br />
                    以下是一些提示:对于 csv 或 txt 文件，列之间的分隔符为
                    TAB。第一行必须是列标题。列标题必须是有意义的术语，以便我们的大语言模型能够理解。列举一些同义词时最好使用斜杠&quot;/&quot;来分隔，甚至更好使用方括号枚举值，例如&quot;qender/sex(male,female)&quot;。
                    <br />
                    以下是标题的一些示例:
                    供应商/供货商&quot;TAB&quot;颜色(黄色、红色、棕色)&quot;TAB&quot;性别(男、女)&quot;TAB&quot;尺码(M、L、XL、XXL)；姓名/名字&quot;TAB&quot;电话/手机/微信&quot;TAB&quot;最高学历(高中，职高，硕士，本科，博士，初中，中技，中专，专科，专升本，MPA，MBA，EMBA)
                    <br />
                    表中的每一行都将被视为一个块。
                    <br />
                    <br />
                    &quot;PAPER&quot;分块方法说明
                    <br />
                    仅支持PDF文件。
                    <br />
                    如果我们的模型运行良好，论文将按其部分进行切片，例如摘要、1.1、1.2等。这样做的好处是
                    LLM
                    可以更好的概括论文中相关章节的内容，产生更全面的答案，帮助读者更好地理解论文。
                    缺点是它增加了 LLM
                    对话的背景并增加了计算成本，所以在对话过程中，你可以考虑减少&quot;topN&quot;的设置。
                    <br />
                    <br />
                    &quot;BOOK&quot;分块方法说明
                    <br />
                    支持的文件格式为 DOCX、PDF、TXT。
                    <br />
                    由于一本书很长，并不是所有部分都有用，如果是 PDF
                    ，请为每本书设置页面范围，以消除负面影响并节省分析计算时间。
                    <br />
                    <br />
                    &quot;LAWS&quot;分块方法说明
                    <br />
                    支持的文件格式为 DOCX、PDF、TXT。
                    <br />
                    法律文件有非常严格的书写格式。我们使用文本特征来检测分割点。Chunk
                    的粒度与&quot;ARTICLE&quot;一致，所有上层文本都会包含在 Chunk 中。
                    <br />
                    <br />
                    &quot;PRESENTATION&quot;分块方法说明
                    <br />
                    演示稿分块，支持的文件格式为PDF、PPTX。
                    <br />
                    每个页面都将被视为一个块。
                    并且每个页面的缩略图都会被存储。您上传的所有PPT文件都会使用此方法自动分块，无需为每个PPT文件进行设置。
                    <br />
                    <br />
                    &quot;ONE&quot;分块方法说明
                    <br />
                    单一文档，支持的文件格式为DOCX、EXCEL、PDF、TXT。
                    <br />
                    对于一个文档，它将被视为一个完整的块，根本不会被分割。如果你要总结的东西需要一篇文章的全部上下文，并且所选
                    LLM 的上下文长度覆盖了文档长度，你可以尝试这种方法。
                  </>
                )
              }}
            >
              <Select
                placeholder="请选择切片方式"
                options={knowledgeDictionaryList}
                fieldNames={{ label: "displayName", value: "code" }}
                onChange={() => form.setFieldsValue({ files: undefined })}
              />
            </Form.Item>
            <Form.Item
              label="建议文本块大小"
              tooltip={{
                overlayInnerStyle: { wordBreak: "break-all", maxWidth: "600px" },
                icon: <InfoCircleOutlined />,
                title:
                  "建议的生成文本块的 token 数阈值。如果切分得到的小文本段 token 数达不到这一阈值就会不断与之后的文本段合并，直至再合并下一个文本段会超过这一阈值为止，此时产生一个最终文本块。如果系统在切分文本段时始终没有遇到文本分段标识符，即便文本段 token 数已经超过这一阈值，系统也不会生成新文本块。"
              }}
            >
              <div className="flex items-center">
                <Form.Item name="chunkSize" noStyle initialValue={512}>
                  <Slider
                    min={0}
                    max={2048}
                    step={1}
                    className="flex-1 mr-4 ml-2"
                    onChange={(value) => form.setFieldsValue({ chunkSize: value })}
                  />
                </Form.Item>
                <Form.Item name="chunkSize" noStyle initialValue={512}>
                  <InputNumber
                    min={0}
                    max={2048}
                    step={1}
                    precision={0}
                    style={{ width: "70px" }}
                    onBlur={(e) =>
                      !e.target.value &&
                      e.target.value !== 0 &&
                      form.setFieldsValue({ chunkSize: 512 })
                    }
                    onChange={(value) => form.setFieldsValue({ chunkSize: value })}
                  />
                </Form.Item>
              </div>
            </Form.Item>
            <Form.Item
              label="文本分段标识符"
              rules={[{ required: true, message: "请输入文本分段标识符" }]}
              name={"splitSymbol"}
              initialValue={"\\n"}
              tooltip={{
                overlayInnerStyle: { wordBreak: "break-all", maxWidth: "600px" },
                icon: <InfoCircleOutlined />,
                title:
                  "支持多字符作为分隔符，多字符用``分隔符包裹。若配置成：\\n`##`；系统将首先使用换行符、两个#号以及分号先对文本进行分割，随后再对分得的小文本块按照「建议文本块大小」设定的大小进行拼装。在设置文本分段标识符前请确保理解上述文本分段切片机制。"
              }}
            >
              <Input placeholder="请输入文本分段标识符" />
            </Form.Item>
          </>
        )}
        <Form.Item
          name="files"
          label="选择文件"
          tooltip={
            isInstruction
              ? {
                  title: (
                    <>
                      • 通用、还标题拆分支持DOCX、PDF、TXT格式
                      <br />
                      •
                      NAIVE切分支持DOCX、XLSX、XLS(Excel97~2003)、PPT、PDF、TXT、JPEG、JPG、PNG、TIF、GIF、CSV、JSON、EML、HTML格式
                      <br />
                      • QA切分支持 excel 和 csv/txt 文件格式
                      <br />
                      • RESUME切分仅支持PDF格式
                      <br />
                      • MANUAL切分支持的文件格式为DOCX、PDF、TXT格式
                      <br />
                      • TABLE切分支持XLSX和CSV/TXT格式
                      <br />
                      • PAPER切分仅支持PDF格式
                      <br />
                      • BOOK切分支持的文件格式为DOCX、PDF、TXT格式
                      <br />
                      • LAWS切分支持的文件格式为DOCX、PDF、TXT格式
                      <br />
                      • PRESENTATION切分支持的文件格式为PDF、PPTX格式
                      <br />• ONE切分支持的文件格式为DOCX、EXCEL、PDF、TXT格式
                    </>
                  ),
                  icon: <InfoCircleOutlined />,
                  overlayInnerStyle: { wordBreak: "break-all", maxWidth: "500px" }
                }
              : undefined
          }
          valuePropName="fileList"
          getValueFromEvent={(e) => {
            const fileList = Array.isArray(e) ? e : e?.fileList
            if (!fileList) {
              return []
            }
            // Only return files that have a success response or are not done yet.
            return fileList.filter((file) => {
              if (file.response) {
                return file.response.success === true
              }
              return true
            })
          }}
          rules={[{ required: true, message: "请选择文件!" }]}
        >
          {/* 当是查看或者编辑状态的时候,只展示一个文件icon以及文件名字 */}
          {docAction === "view" || docAction === "edit" ? (
            <div
              className="flex items-start justify-start align-middle mt-[10px] mb-[10px] rounded-md px-2 py-4"
              style={{ border: "1px solid #E4E7EC" }}
            >
              <img
                src={getFileIcon({ name: currentData?.name })}
                alt=""
                className="w-[40px] h-[40px] mr-2"
              />
              <div className="flex flex-col">
                <div className="mt-[10px]">
                  <a
                    className="ml-1"
                    type="link"
                    href={currentData?.url}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {currentData?.name}
                  </a>
                </div>
              </div>
            </div>
          ) : (
            <>
              <UploadFile
                isDragger={isInstruction}
                multiple={isInstruction}
                promptText={!!extraInfoText && `支持上传${extraInfoText}等格式的文档，可批量上传`}
                onChange={handleUploadChange}
                action={uploadDocumentKnowledgeUrl(selectedKnowledgeBase)}
                accept={getAcceptTypes(extraInfo || "[]")}
                beforeUpload={(file) => {
                  let isTypeAllowed = [
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/pdf",
                    "text/plain",
                    "text/markdown"
                  ].includes(file.type)
                  if (isInstruction) {
                    const allowedTypes = getAllowedMimeTypes(extraInfo || "")
                    // 上传类型限制
                    isTypeAllowed = allowedTypes.includes(file.type)
                  }
                  if (!isTypeAllowed) {
                    message.error("请上传正确的文件类型")
                    return Upload.LIST_IGNORE
                  }
                }}
              />
              {/* <div className="text-[12px] text-gray-400">支持上传PDF、DOC、DOCX、TXT 等格式</div> */}
            </>
          )}
        </Form.Item>
        {!isInstruction && (
          <div className="text-[12px] font-[400] text-[#475467] -mt-[10px] mb-[15px]">
            支持上传pdf, doc, docx, txt等格式的文档
          </div>
        )}
        <Form.Item name="summary" label="摘要说明">
          <Input.TextArea placeholder="请输入摘要说明" />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditDocModal
