.knowledgeBase {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  padding: 10px;
  margin: 10px;
  border-radius: 14px;
  flex: 1;
  cursor: pointer;
}

.iconBox {
  margin-right: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 50px;

  }
}

.topContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

}

.content {
  flex: 1;
}

.inactive {
  opacity: 0.5;
}

.rightBtn {
  position: absolute;
  right: 10px;
  bottom: 10px;
}