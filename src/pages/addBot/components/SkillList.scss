.skill-list-wrapper-v2 {
  .skill-list-wrapper.iframeStyle {
    padding: 20px;
    // max-height: 100%;

    .content {
      background-color: #f7f8fa;
      padding: 20px;
      border-radius: 14px;
    }

    .header {
      margin-bottom: 0px;
      margin-top: 5px;
      .ant-select {
        min-height: 36px;
        width: 0px;
        overflow: hidden;
        // display: none;
        // visibility: hidden;
        // margin-right: 16px;
        transition: all 0.2s;
      }
      .expand-search-style {
        width: 160px !important;
        margin-right: 16px;
        overflow: visible;
        // display: inline-block;
        // visibility: visible;
      }
      .expand-search-style-input {
        transition: all 0.2s;
        width: 0px;
        padding: 0px;
        // border: none;
        height: 36px;
        border-width: 0px;
      }
      .style-input {
        width: 240px;
        margin-right: 15px;
        border-width: 1px;
        padding: 4px 8px;
      }
      .header-search-icon {
        display: flex;
        width: 36px;
        height: 36px;
        padding: var(--Gap-xs, 8px);
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        border-radius: var(--Gap-xs, 8px);
        border: 1px solid var(---, #d0d5dd);
        background: var(---, #fff);
        box-shadow: 0px 1px 2px 0px rgba(24, 27, 37, 0.05);
        color: #98a2b3;
        cursor: pointer;
        transition: all 0.2s;
        &:hover {
          background: var(---, #fcfaff);
          border: 1px solid #cac0ff;
          color: #7f56d9;
        }
      }
    }
  }

  .skill-list-wrapper {
    padding-right: 20px;
    // max-height: calc(100vh - 96px);
    // max-height: calc(100vh - 83px);
    max-height: calc(100vh - 15px);
    overflow-y: auto;
    overflow-x: hidden;
    .form-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 14px;
    }
    .divider {
      font-size: 18px;
      font-weight: 900;
      margin: 40px 0 0;
      letter-spacing: 10px;
    }

    .order-btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        margin-right: 10px;
      }
    }
    .tooltip-bar {
      margin-bottom: 12px;
      color: #525866;
      font-size: 14px;
      font-weight: 500;
    }
    .text-base-img {
      width: 48px;
      display: inline-block;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      vertical-align: middle;
    }

    .text-base {
      color: #181b25;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      vertical-align: middle;
      max-width: 80%;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .skill-type-badge {
      // position: absolute;
      // top: 0px;
      // right: 0px;
      // background-color: #f0f0f0;
      // padding: 2px 6px;
      // border-radius: 4px;
      // font-size: 12px;
      // color: #7F56D9;
    }
    .skill-group-badge {
      background-color: var(--main-2, #f2f2fe);
      padding: 2px 20px;
      border-radius: 4px;
      font-size: 12px;
      color: #7f56d9 !important;
      border-radius: 15px;
      font-weight: bold;
    }

    .desc {
      // min-height: 40px;
      margin-top: 8px;
      font-size: 12px;
      line-height: 18px;
      font-style: normal;
      color: #344054;
      font-weight: 400;

      // 俩行溢出
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }

    .time-tag {
      display: flex;
      padding: 4px var(--Gap-xs, 8px);
      align-items: center;
      gap: 4px;
      border-radius: 100px;
      background: var(---, #f5f7fa);
      .title {
        font-size: 12px !important;
        font-style: normal;
        font-weight: 400;
        color: #475467 !important;
      }
      .time {
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        color: #181b25 !important;
        .beta-tag {
          background: linear-gradient(110.69deg, #7F56D9 0.38%, #FF00D9 100%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          color: #7F56D9;
        }
      }
    }

    .belongTo-botName {
      font-size: 12px;
      color: #b1b1b1;
      white-space: nowrap;
      gap: var(--Gap-xs, 8px);
    }

    .remove-btn {
      // position: absolute;
      // right: 25px;
      // display: flex;
      // justify-content: space-between;
      // align-items: flex-start;
      width: 100%;
      .subscribe-count {
        font-size: 14px;
        margin-right: 12px;
        color: #7f56d9;
        .count-text {
          margin-left: 4px;
        }
      }
      .remove-btn-insert {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        color: #181b25 !important;
      }
      .remove-btn-insert-sub {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
    }

    .subscribe-status {
      min-width: 52px;
      white-space: nowrap;
      display: flex;
      height: var(--Gap-l, 24px);
      padding: 4px var(--Gap-xs, 8px);
      align-items: center;
      gap: var(--Gap-xs, 8px);
      border-radius: 4px;
      text-align: right;
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      background-color: #bfbfbf;
      color: #fff;
      &.using {
        background-color: #7f56d9;
      }
    }

    .card-item.deactivate {
      // border-color: #F3F3F3;
    }

    .card-item {
      height: 212px;
      display: flex;
      padding: 20px;
      flex-direction: column;
      align-items: flex-start;
      flex: 1 0 0;
      border-radius: var(--Gap-s, 12px);
      border: 1px solid var(---, #e4e7ec);
      background: linear-gradient(117.35deg, #ffffff 69.3%, #f2efff 98.8%);
      position: relative;
      transition: all 0.2s;
      &.agent-card-item {
        background: linear-gradient(117.35deg, #ffffff 69.3%, #eef4ff 98.8%);
        &:before {
          background-image: linear-gradient(257.29deg, #2c6cf5 1.45%, #29dfff 100%);
        }
      }
      &.skill-card-item {
        background: linear-gradient(117.35deg, #ffffff 69.3%, #f6f4ff 98.8%);
        &:before {
          background-image: linear-gradient(249.29deg, #3234ff -0.07%, #ff70de 97.52%);
        }
      }
      &:before {
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        z-index: -1;
        border-radius: var(--Gap-s, 13px);
        content: "";
        opacity: 0;
        transition: opacity 0.3s;
      }
      &:hover {
        border: 1px solid transparent;
        box-shadow: 0px 4px 32px 0px rgba(51, 51, 51, 0.12);
        &:before {
          opacity: 1;
        }
      }

      .title {
        color: var(--text-5, #1d2129);
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 400;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .skill-large-icon {
        font-size: 46px;
        color: #dad9d9;
      }

      .skill-item-text {
        max-width: 100%;
        display: flex;
        align-items: center;
        gap: var(--Gap-xs, 12px);
        align-self: stretch;
        overflow: hidden;
        // display: inline-block;
        // overflow: hidden;
        // text-overflow: ellipsis;
      }

      .status {
        color: var(-----700, #178c4e);
        // font-size: 12px;
        // font-weight: 500;
        // border-radius: 2px;
        background: var(-----50, #e0faec);
        min-width: 52px;
        white-space: nowrap;
        // padding: 2px 6px;
        // min-width: 48px;

        display: flex;
        height: var(--Gap-l, 24px);
        padding: 4px var(--Gap-xs, 8px);
        align-items: center;
        gap: var(--Gap-xs, 8px);
        border-radius: 4px;
        text-align: right;
        font-size: 12px;
        font-weight: 500;
        line-height: 18px;
      }

      .status.debug {
        background: var(-----50, #fff1eb);
        color: var(----600, #e97135);
      }

      // 未启用
      .status.not {
        color: #6f7271;
        background: #f2f3f5;
      }
    }

    .swich {
      position: absolute;
      bottom: 20px;
      right: 12px;
    }

    .bottom-center {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      align-self: stretch;
    }
  }
  .tab-skill {
    position: fixed;
    top: 0px;
    z-index: 10;
    // background: #fff;
    left: 0;
    width: 100%;
    padding: 20px;
    padding-top: 0px;
    transition: all 0.2s;
    background: linear-gradient(180deg, #ebeaff -75%, #fff 30.97%) !important;
  }
  .tab-skill-shadow {
    box-shadow: 0px 4px 12px 0px rgba(24, 27, 37, 0.08);
  }

  .tab-skill-no-iframe {
    .ant-tabs-nav {
      margin: 20px 0;
    }
    .ant-tabs-nav-wrap {
      background: #f3f3f3;
      padding: 5px;
      border-radius: 5px;
    }
    .ant-select-selector {
      width: 200px !important;
    }
    .ant-input {
      width: 200px;
      margin-left: 20px;
    }
    .header-search-icon {
      display: none;
    }
    .ant-spin-container > div {
      margin-top: 20px !important;
    }
  }
}

.skill-overlayInnerStyle {
  .ant-tooltip-inner {
    color: #475467 !important;
  }
}
