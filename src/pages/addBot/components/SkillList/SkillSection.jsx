import { Fragment } from "react"
import { Row } from "antd"
import { keys } from "lodash"
import SkillItem from "./SkillItem"
import "../SkillList.scss"

const SkillSection = ({ tabValue, skills, groupList, ...props }) => {
  if (skills.length === 0) return null

  // 直接使用传入的已筛选技能列表，按分组组织
  const skillsOfGroup = skills.reduce((acc, current) => {
    // 获取当前对象的分组值
    const cValue = current.groupTagName || "未分组"

    // 如果 acc 中没有该分组值的数组，则初始化一个
    if (!acc[cValue]) {
      acc[cValue] = []
    }

    // 将当前对象推送到对应分组值的数组中
    acc[cValue].push(current)

    return acc
  }, {})

  const originalSkillsOfGroupKeys = keys(skillsOfGroup)
  // 根据 groupList 的顺序排列分组
  const skillsOfGroupKeys = groupList
    ?.map((item) => {
      if (originalSkillsOfGroupKeys.includes(item.tagDesc)) {
        return item.tagDesc
      }
      return null
    })
    .filter(Boolean)

  // 添加"未分组"到最后
  if (originalSkillsOfGroupKeys.includes("未分组")) {
    skillsOfGroupKeys.push("未分组")
  }

  return (
    <div>
      {skillsOfGroupKeys.map((groupTagName) => {
        return (
          <Fragment key={groupTagName}>
            <div className="mb-2">
              {skillsOfGroup[groupTagName]?.length > 0 && tabValue === "1" && (
                <div className="tooltip-bar">{groupTagName}</div>
              )}
            </div>

            <Row gutter={20}>
              {skillsOfGroup[groupTagName]?.map((skill) => (
                <SkillItem key={skill.skillNo} {...props} skill={skill} />
              ))}
            </Row>
          </Fragment>
        )
      })}
    </div>
  )
}

export default SkillSection
