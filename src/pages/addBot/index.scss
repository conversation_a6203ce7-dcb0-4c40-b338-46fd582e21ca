.addBot-wrapper {
  .avatar-item {
    width: 44px;
    height: 44px;
  }

  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .ant-tabs-ink-bar {
    width: 0 !important;
  }

  .addbot-tabs .ant-tabs-tab-active {
    font-weight: 400;
    color: #7F56D9 !important;
    font-size: 14px !important;
    border-radius: 100px;
    background: var(--main-2, #f2f2fe);
    // padding: 12px 0;
    // padding: 0px 16px !important;
    // padding-left: 16px;
    // padding-right: 16px;
    // padding-top: 5px;
    // padding-bottom: 5px;
    // display: flex !important;
    // flex-direction: column !important;
    // align-items: flex-start !important;
  }

  .addbot-tabs .ant-tabs-tab {
    font-weight: 400;
    font-size: 14px !important;
    padding: 5px 16px;
    box-sizing: border-box;
  }

  .addbot-tabs .ant-tabs-nav::before {
    border: 0;
  }
  .ant-btn-primary:not(.ant-btn-link) {
    // background: #7F56D9;
    // &:hover {
    //   background: #8b91fc !important;
    // }
  }
}

.personal-info-wrapper {
  max-height: calc(100vh - 230px);
  overflow: auto;
}

