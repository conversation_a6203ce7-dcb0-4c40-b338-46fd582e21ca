import { useState, useEffect } from "react"
import { Button, Space, Avatar, Modal, message, Tag, Tooltip } from "antd"
import { ArrowLeftOutlined, LoadingOutlined, DownloadOutlined } from "@ant-design/icons"
import { useNavigate } from "react-router-dom"
import { useReleaseAgentPro } from "@/api/agent"
import { unlockAgent, lockAgent, exportAgentApiDoc, loadAgentVersion } from "@/api/agent/api"
// import MateAgentTranset from "./MateAgentTranset"
import moment from "moment"
import { useQueryClient } from "@tanstack/react-query"
import { QUERY_KEYS } from "@/constants/queryKeys"
import { useSSO } from "@/components/SSOProvider"
import { AGENT_MODE_OPTIONS } from "@/components/CreateAgent"
import ApiDocPreviewModal from "./ApiDocPreviewModal"
import HistoryVersionDrawer from "./HistoryVersionDrawer"

const DetailHeader = ({
  botNo,
  agentDetail,
  agentMode,
  lockData,
  selectedSkills,
  selectedSkill,
  selectedTools,
  knowledgeBases,
  workSkillNo,
  hasUnsavedChanges,
  markdownContent,
  mode,
  selectedModel,
  handleSave,
  setIsDebugSuccessful,
  isSaving,
  setIsSaving,
  releaseNewStatus,
  isOnlyRead,
  originalAgentNo,
  fetchLatestVersion,
  voiceRef // 新增：语音组件的 ref
}) => {
  const navigate = useNavigate()
  const [isAutoSaving, setIsAutoSaving] = useState(false)
  const [isReleaseModalVisible, setIsReleaseModalVisible] = useState(false)
  const [isApiDocModalVisible, setIsApiDocModalVisible] = useState(false)
  const [apiDocHtml, setApiDocHtml] = useState("")
  const [apiDocLoading, setApiDocLoading] = useState(false)
  const [voiceSaving, setVoiceSaving] = useState(false)
  const [historyDrawerVisible, setHistoryDrawerVisible] = useState(false)
  const [selectedSkillNo, setSelectedSkillNo] = useState(undefined)

  useEffect(() => {
    if (Array.isArray(selectedSkill) && selectedSkill.length > 0) {
      const isCurrentSkillStillPresent = selectedSkill.some(
        (skill) => skill.skillNo === selectedSkillNo
      )
      if (!isCurrentSkillStillPresent) {
        setSelectedSkillNo(selectedSkill[0].skillNo)
      }
    } else {
      setSelectedSkillNo(undefined)
    }
  }, [selectedSkill, selectedSkillNo])

  // console.log("agentDetail555====>", agentDetail, releaseNewStatus)

  const queryClient = useQueryClient()
  const { mutate: releaseAgent, isLoading: isReleasing } = useReleaseAgentPro()
  const { userInfo = {} } = useSSO()

  // 自动保存  --- 暂时删除自动保存
  //   useEffect(() => {
  //     let saveTimeout
  //     const autoSave = async () => {
  //       if (!agentDetail) return
  //       setIsAutoSaving(true)
  //       try {
  //         await handleSave(
  //           false,
  //           {
  //             workSkillNo,
  //             skillNos: selectedSkills,
  //             pluginNos: selectedTools,
  //             knowledgeBases: knowledgeBases
  //           },
  //           markdownContent,
  //           mode,
  //           selectedModel
  //         )
  //       } finally {
  //         setIsAutoSaving(false)
  //       }
  //     }

  //     // 每60秒自动保存一次
  //     saveTimeout = setInterval(autoSave, 60000)

  //     return () => {
  //       if (saveTimeout) {
  //         clearInterval(saveTimeout)
  //       }
  //     }
  //   }, [agentDetail, handleSave, markdownContent, mode, selectedModel, workSkillNo, knowledgeBases])

  // 处理发布
  const handleRelease = () => {
    // 如果有未保存的更改，先保存
    if (hasUnsavedChanges) {
      handleSave(
        true,
        { workSkillNo, knowledgeBases: knowledgeBases },
        markdownContent,
        mode,
        selectedModel
      )
    }

    // 显示发布弹窗
    setIsReleaseModalVisible(true)
  }

  // 确认发布
  const confirmRelease = () => {
    releaseAgent(
      {
        agentNo: agentDetail?.agentNo,
        versionNo: agentDetail?.versionNo,
        isCurrent: true,
        versionName: (agentDetail?.name || "Agent") + moment().format("YYYYMMDDHHmmss"),
        description: ""
      },
      {
        onSuccess: (response) => {
          if (response.success) {
            message.success("发布成功")
            queryClient.invalidateQueries([QUERY_KEYS.AGENT_VERSION_LIST])
            setIsReleaseModalVisible(false)
            // 重置调试状态
            setIsDebugSuccessful(false)
            // 重新调用 版本记录接口 latest
            fetchLatestVersion()
          } else {
            message.error(response.message || "发布失败")
          }
        },
        onError: (error) => {
          message.error(error.message || "发布失败")
        }
      }
    )
  }

  // 取消发布
  const cancelRelease = () => {
    setIsReleaseModalVisible(false)
  }

  const fetchApiDoc = async (skillNo) => {
    if (!agentDetail?.agentNo) return
    setApiDocLoading(true)
    try {
      const html = await exportAgentApiDoc({
        agentNo: agentDetail.agentNo,
        previewMode: true,
        skillNo: skillNo
      })
      setApiDocHtml(html)
      if (!isApiDocModalVisible) {
        setIsApiDocModalVisible(true)
      }
    } catch (e) {
      message.error("获取API文档失败")
    } finally {
      setApiDocLoading(false)
    }
  }

  // 预览API文档
  const handlePreviewApiDoc = async () => {
    await fetchApiDoc(selectedSkillNo)
  }

  const handleSkillChange = async (newSkillNo) => {
    setSelectedSkillNo(newSkillNo)
    await fetchApiDoc(newSkillNo)
  }

  // 锁的点击事件
  const onAgentLock = async () => {
    if (lockData?.locked) {
      const result = await unlockAgent({
        botNo,
        agentNo: agentDetail?.agentNo
      })
      if (result.success) {
        message.success("解锁成功！")
      } else {
        message.error(result.message || "解锁失败，请稍后重试")
      }
    } else {
      const result = await lockAgent({
        botNo,
        agentNo: agentDetail?.agentNo
      })
      if (result.success) {
        message.success("锁定成功！")
      } else {
        message.error(result.message || "锁定失败，请稍后重试")
      }
    }
    queryClient.invalidateQueries([QUERY_KEYS.AGENT_LOCK_INFO])
  }

  // 语音设置保存
  const handleVoiceSave = async () => {
    if (!voiceRef?.current) {
      console.log("语音组件 ref 不存在，跳过语音保存")
      return
    }

    try {
      setVoiceSaving(true)
      console.log("开始保存语音设置...")

      // 调用语音组件的保存方法，获取表单中的最新数据并保存
      await voiceRef.current.saveVoiceSettings()

      console.log("语音设置保存成功")
    } catch (error) {
      console.error("语音设置保存失败:", error)
      // 抛出异常，让上层调用者知道保存失败
      throw error
    } finally {
      setVoiceSaving(false)
    }
  }

  // 处理保存（包含语音保存）
  const handleSaveWithVoice = async () => {
    setIsSaving(true)

    try {
      // 如果是语音类型的 Agent，先保存语音设置
      if (agentMode == 2) {
        console.log("检测到语音类型 Agent，开始保存语音设置...")
        try {
          await handleVoiceSave()
        } catch (error) {
          // 语音保存失败，不继续保存 Agent 数据
          console.error("语音设置保存失败，停止后续保存:", error)
          message.error("语音设置保存失败，请检查必填字段后重试")
          return
        }
      }

      // 保存普通的 Agent 数据
      await handleSave(
        true,
        { workSkillNos: workSkillNo, knowledgeBases: knowledgeBases },
        markdownContent,
        mode,
        selectedModel
      )
    } catch (error) {
      console.error("保存失败:", error)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <>
      <div className="flex items-center justify-between px-[20px] py-[12px] border-b border-gray-100 bg-white mb-[8px]">
        <div className="flex items-center ">
          <div className="text-[16px] text-[#181B25] font-[600] flex items-center">
            <ArrowLeftOutlined
              onClick={() => {
                // 构建回到Agent列表页的路径，保持原有参数
                // const searchParams = new URLSearchParams(window.location.search)
                // const isIframe = searchParams.get("isIframe")
                // const workbenchNo = searchParams.get("workbenchNo")
                // const parentOrigin = searchParams.get("parentOrigin")
                // const token = searchParams.get("token")

                // // 构建回退路径到 /agent 页面
                // let backPath = "/agent"
                // const backParams = new URLSearchParams()
                // if (botNo) backParams.set("botNo", botNo)
                // if (isIframe) backParams.set("isIframe", isIframe)
                // if (workbenchNo) backParams.set("workbenchNo", workbenchNo)
                // if (parentOrigin) backParams.set("parentOrigin", parentOrigin)
                // if (token) backParams.set("token", token)

                // const queryString = backParams.toString()
                // if (queryString) {
                //   backPath += `?${queryString}`
                // }
                navigate(-1)
              }}
              className="mr-[8px] text-[14px] cursor-pointer hover:text-[#7f56d9]"
            />
            <Avatar size={30} className="mr-[8px]" src={agentDetail?.icon} />
            {agentDetail?.agentName || "暂未获得名称"}
            {agentMode && (
              <Tag className="ml-[8px]">
                {AGENT_MODE_OPTIONS?.find((v) => v.value == agentMode)?.label?.replace(
                  "Agent",
                  ""
                ) || "--"}
              </Tag>
            )}
            {agentMode == 1 && agentDetail?.type === "single_agent_skill_mode" && (
              <div className="flex items-center">
                {originalAgentNo && (
                  <Tag className="ml-[4px] text-[12px]" bordered={false} color="blue">
                    深拷贝
                  </Tag>
                )}

                <Tag
                  className="ml-[4px] text-[12px]"
                  bordered={false}
                  color={
                    releaseNewStatus === "draft"
                      ? "volcano"
                      : releaseNewStatus === "to_be_released"
                        ? "orange"
                        : "green"
                  }
                >
                  {releaseNewStatus === "draft"
                    ? "草稿"
                    : releaseNewStatus === "to_be_released"
                      ? "待发布"
                      : "已发布"}
                </Tag>
              </div>
            )}
            {isAutoSaving && (
              <span className="text-[10px] text-blue-500 font-[400] ml-[8px]">
                <LoadingOutlined className="mr-[4px]" />
                自动保存中...
              </span>
            )}
            {hasUnsavedChanges && !isAutoSaving && (
              <span className="text-[10px] text-[#B42318] font-[400] ml-[8px]">有未保存的更改</span>
            )}
          </div>
        </div>
        {!isOnlyRead && (
          <Space size="small">
            {/* 历史记录版本 */}
            {agentMode == 1 && agentDetail?.type === "single_agent_skill_mode" && (
              <Tooltip placement="bottom" title="点击查看历史记录">
                <div
                  className="inline-block  mr-2 cursor-pointer transition-all duration-300 px-[10px] py-[0px] align-middle text-center bg-[#F5F7FA] leading-[36px] rounded-[8px] hover:text-[#7F56D9]"
                  onClick={() => setHistoryDrawerVisible(true)}
                >
                  <i className="iconfont icon-lishihuihua hover:text-[#7F56D9]"></i>
                </div>
              </Tooltip>
            )}
            {[1, 2].includes(Number(agentMode)) && (
              <Tooltip
                placement="bottom"
                title={
                  lockData?.locked
                    ? lockData?.lockInfo?.username === userInfo.username
                      ? "当前Agent已为您锁定，锁定期间他人不可保存、调试和发布等操作，使用结束记得解锁哦~"
                      : `【${lockData?.lockInfo?.userRealName}】已锁定当前Agent，如需解锁请联系锁定人${lockData?.lockInfo?.userRealName}/${lockData?.lockInfo?.username}解锁`
                    : `锁定Agent后其他人则不能保存、调试和发布等操作`
                }
              >
                {lockData?.locked ? (
                  <div
                    onClick={onAgentLock}
                    className="inline-block  mr-2 cursor-pointer transition-all duration-300 px-[10px] py-[0px] align-middle text-center bg-[#FFF1EB] leading-[36px] rounded-[8px]"
                  >
                    <i className="iconfont icon-lock text-[#D05E25]"></i>
                  </div>
                ) : (
                  <div
                    onClick={onAgentLock}
                    className="inline-block  mr-2 cursor-pointer transition-all duration-300 px-[10px] py-[0px] align-middle text-center bg-[#F5F7FA] leading-[36px] rounded-[8px] hover:text-[#7F56D9]"
                  >
                    <i className="iconfont icon-unlock hover:text-[#7F56D9]"></i>
                  </div>
                )}
              </Tooltip>
            )}

            {/* API文档预览与下载 */}
            <Tooltip placement="bottom" title="点击预览 Agent API 文档 ">
              <div
                onClick={handlePreviewApiDoc}
                className="inline-block  mr-2 cursor-pointer transition-all duration-300 px-[10px] py-[0px] align-middle text-center bg-[#F5F7FA] leading-[36px] rounded-[8px] hover:text-[#7F56D9]"
              >
                <i className="iconfont icon-API hover:text-[#7F56D9]"></i>
              </div>
            </Tooltip>

            {mode !== "meta_agent_mode" && (
              <Button
                loading={isSaving || voiceSaving}
                onClick={handleSaveWithVoice}
                type={hasUnsavedChanges ? "primary" : "default"}
              >
                保存
                {agentMode == 2 && voiceSaving && "语音设置中..."}
              </Button>
            )}
            <Button
              type="primary"
              onClick={handleRelease}
              disabled={
                agentMode != 2 &&
                mode !== "meta_agent_mode" &&
                releaseNewStatus !== "to_be_released"
              }
              loading={isReleasing}
            >
              发布
            </Button>
          </Space>
        )}
      </div>
      <ApiDocPreviewModal
        open={isApiDocModalVisible}
        loading={apiDocLoading}
        html={apiDocHtml}
        skillNos={selectedSkill}
        selectedSkillNo={selectedSkillNo}
        onSkillChange={handleSkillChange}
        onClose={() => setIsApiDocModalVisible(false)}
        onDownload={() =>
          exportAgentApiDoc({ agentNo: agentDetail?.agentNo, skillNo: selectedSkillNo })
        }
      />
      {/* Release Modal */}
      <Modal
        title="发布确认"
        open={isReleaseModalVisible}
        onOk={confirmRelease}
        onCancel={cancelRelease}
        okText="确认发布"
        cancelText="取消"
        width={400}
        confirmLoading={isReleasing}
      >
        <p className="my-4">确定发布当前版本吗？</p>
      </Modal>
      <HistoryVersionDrawer
        open={historyDrawerVisible}
        onClose={() => setHistoryDrawerVisible(false)}
        agentNo={agentDetail?.agentNo}
        insetCurrentVersion={async (versionNo) => {
          const res = await loadAgentVersion({ agentNo: agentDetail?.agentNo, versionNo })
          console.log("res====>", res)
          if (res.success) {
            message.success("载入成功")
            fetchLatestVersion()
          } else {
            message.error(res.message || "载入失败")
          }

          // fetchLatestVersion("insetCurrentVersion", versionNo)
          setHistoryDrawerVisible(false)
        }}
      />
    </>
  )
}

export default DetailHeader
