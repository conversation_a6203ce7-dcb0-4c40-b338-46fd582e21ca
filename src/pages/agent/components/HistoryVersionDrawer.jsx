import React, { useEffect, useState } from "react"
import { Drawer, Table, Button, Tag, message, Space, Popconfirm } from "antd"
import {
  fetchAgentReleaseVersionList,
  deleteAgentReleaseVersion,
  enableAgentReleaseVersion
} from "@/api/agent/api"

const HistoryVersionDrawer = ({ open, onClose, agentNo, insetCurrentVersion }) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState([])
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [setCurrentLoading, setSetCurrentLoading] = useState(false)
  const refreshList = () => {
    setLoading(true)
    fetchAgentReleaseVersionList({ agentNo })
      .then((res) => {
        setData(res?.data || [])
      })
      .catch(() => {
        message.error("获取历史版本失败")
      })
      .finally(() => setLoading(false))
  }
  useEffect(() => {
    if (open && agentNo) {
      refreshList()
    }
  }, [open, agentNo])

  // 操作列需要用到agentNo，columns需放到组件内
  const columns = [
    {
      title: "Agent版本编号",
      dataIndex: "versionNo",
      key: "versionNo",
      width: 120,
      ellipsis: true
    },
    {
      title: "名称",
      dataIndex: "versionName",
      key: "versionName",
      width: 200,
      ellipsis: true,
      render: (text) => text || "--"
    },
    {
      title: "操作人",
      dataIndex: "modifierDisplayName",
      key: "modifierDisplayName",
      width: 120,
      ellipsis: true
    },
    {
      title: "更新时间",
      dataIndex: "gmtModified",
      key: "gmtModified",
      width: 160,
      render: (text) => (text ? new Date(text).toLocaleString() : "-")
    },
    {
      title: "状态",
      dataIndex: "releaseStatus",
      key: "releaseStatus",
      width: 100,
      render: (status) => {
        let color = "default"
        let label = status
        if (status === "draft") {
          color = "orange"
          label = "草稿"
        } else if (status === "released") {
          color = "green"
          label = "已发布"
        } else {
          color = "magenta"
          label = "待发布"
        }
        return (
          <Tag bordered={false} color={color}>
            {label}
          </Tag>
        )
      }
    },
    {
      title: "备注",
      dataIndex: "description",
      key: "description",
      width: 180,
      ellipsis: true,
      render: (text) => text || "-"
    },
    {
      title: "操作",
      key: "action",
      width: 220,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Popconfirm
            title="确认载入当前版本吗？"
            okText="确定载入"
            cancelText="取消"
            onConfirm={async () => {
              insetCurrentVersion(record.versionNo)
            }}
          >
            <Button size="small" type="link">
              载入
            </Button>
          </Popconfirm>

          {record.inUse ? (
            <Button size="small" disabled type="link">
              当前
            </Button>
          ) : (
            <Popconfirm
              title="确定将该版本设为当前版本吗？"
              okText="设为当前"
              cancelText="取消"
              onConfirm={async () => {
                setSetCurrentLoading(true)
                try {
                  await enableAgentReleaseVersion({ agentNo, versionNo: record.versionNo })
                  message.success("设为当前成功")
                  refreshList()
                } catch (e) {
                  message.error(e?.message || "设为当前失败")
                } finally {
                  setSetCurrentLoading(false)
                }
              }}
            >
              <Button size="small" type="link" loading={setCurrentLoading}>
                设为当前
              </Button>
            </Popconfirm>
          )}
          <Popconfirm
            title="确定要删除该历史版本吗？"
            okText="删除"
            cancelText="取消"
            onConfirm={async () => {
              setDeleteLoading(true)
              try {
                await deleteAgentReleaseVersion({ agentNo, versionNo: record.versionNo })
                message.success("删除成功")
                refreshList()
              } catch (e) {
                message.error(e?.message || "删除失败")
              } finally {
                setDeleteLoading(false)
              }
            }}
          >
            <Button disabled={record.inUse} size="small" danger type="link" loading={deleteLoading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <Drawer
      title="历史版本记录"
      placement="left"
      width={1000}
      open={open}
      onClose={onClose}
      destroyOnClose
    >
      <Table
        rowKey="versionNo"
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        scroll={{ x: 900, y: 520 }}
      />
    </Drawer>
  )
}

export default HistoryVersionDrawer
