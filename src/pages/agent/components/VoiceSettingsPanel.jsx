import {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle
} from "react"
import {
  Select,
  InputNumber,
  TimePicker,
  Slider,
  Form,
  Switch,
  Input,
  Button,
  message,
  Spin,
  Row,
  Col,
  Table,
  Tag
} from "antd"
import {
  PlusOutlined,
  SoundOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from "@ant-design/icons"
import { getTimbreList, getVoiceAgentDetails, synthesisVoice } from "@/api/voiceAgent/api"
import { useCreateOrUpdateVoiceAgent } from "@/api/voiceAgent"
import { useFetchAvailableSkills } from "@/api/skill"
import { useLocation, useNavigate } from "react-router-dom"
import queryString from "query-string"
import CustomEmpty from "@/antd-styles/components/CustomEmpty"
import moment from "moment"
import { fetchPersonalTimbreListV2 } from "@/api/timbre/api"

const { Option } = Select

const VoiceSettingsPanel = forwardRef(
  ({ voiceConfig, onConfigChange, onSave, botNo, agentDetail, refreshKey }, ref) => {
    const [form] = Form.useForm()
    const location = useLocation()
    const navigate = useNavigate()
    const channelConfigRef = useRef(null)

    // 声音设置相关状态
    const [voiceCollapsedState, setVoiceCollapsedState] = useState({
      voiceScript: true,
      general: true,
      channels: true,
      tts: true,
      skills: true,
      details: true
    })

    // 音色选项状态
    const [timbreOptions, setTimbreOptions] = useState([])
    const [timbreLoading, setTimbreLoading] = useState(false)

    // 技能搜索状态
    const [filterText, setFilterText] = useState("")

    // 语音详情加载状态
    const [voiceDetailsLoading, setVoiceDetailsLoading] = useState(false)

    // 保存相关状态
    const [saving, setSaving] = useState(false)
    const [sectionChanges, setSectionChanges] = useState({
      general: false,
      tts: false,
      skills: false,
      details: false
    })
    const [saveTimeouts, setSaveTimeouts] = useState({
      general: null,
      tts: null,
      skills: null,
      details: null
    })
    const [taskId, setTaskId] = useState(null)

    // 试听功能状态
    const [synthesizing, setSynthesizing] = useState(false)
    const [audioUrl, setAudioUrl] = useState("")
    const [isPlaying, setIsPlaying] = useState(false)
    const [audioDuration, setAudioDuration] = useState(0)
    const [currentTime, setCurrentTime] = useState(0)
    const [progress, setProgress] = useState(0)
    const audioRef = useRef(null)

    // 分流比例错误提示
    const [rateError, setRateError] = useState("")

    // 分流比例变动时校验总和
    const handleRateChange = (value, fieldIdx) => {
      const ccConfigs = Array.isArray(form.getFieldValue("ccConfigs"))
        ? form.getFieldValue("ccConfigs")
        : []
      ccConfigs[fieldIdx].rate = value
      const total = ccConfigs.reduce((sum, item) => sum + (Number(item.rate) || 0), 0)
      if (total > 100) {
        setRateError("通道分流比例之和不能超过100%")
      } else {
        setRateError("")
      }
      form.setFieldsValue({ ccConfigs })
    }

    // 新增通道时自动分配分流比例
    const handleAddChannel = () => {
      const ccConfigs = Array.isArray(form.getFieldValue("ccConfigs"))
        ? form.getFieldValue("ccConfigs")
        : []
      const count = ccConfigs.length + 1
      const base = Math.floor(100 / count)
      const rates = Array(count).fill(base)
      rates[count - 1] = 100 - base * (count - 1)
      // 新增时自动分配所有通道分流比例
      const newConfigs = ccConfigs.map((item, idx) => ({ ...item, rate: rates[idx] }))
      newConfigs.push({
        ccPlatform: undefined,
        bizField: "",
        bizTypes: [],
        rate: rates[count - 1],
        extraInfo: "",
        isSkipWebcall: 0
      })
      form.setFieldsValue({ ccConfigs: newConfigs })
      setSectionChanges((prev) => ({ ...prev, channels: true }))
      setTimeout(() => {
        if (channelConfigRef.current) {
          // 滚动到通道设置区域的底部
          const lastChannelConfig = channelConfigRef.current.lastElementChild
          if (lastChannelConfig) {
            lastChannelConfig.scrollIntoView({
              behavior: "smooth",
              block: "nearest"
            })
          }
        }
      }, 100)
    }

    // 删除通道时自动分配分流比例
    const handleRemoveChannel = (fieldName) => {
      const ccConfigs = Array.isArray(form.getFieldValue("ccConfigs"))
        ? form.getFieldValue("ccConfigs")
        : []
      // 先移除指定通道
      const newConfigs = ccConfigs.filter((_, idx) => idx !== fieldName)
      // 重新分配分流比例
      const count = newConfigs.length
      if (count > 0) {
        const base = Math.floor(100 / count)
        const rates = Array(count).fill(base)
        rates[count - 1] = 100 - base * (count - 1)
        for (let i = 0; i < count; i++) {
          newConfigs[i].rate = rates[i]
        }
      }
      form.setFieldsValue({ ccConfigs: newConfigs })
      setSectionChanges((prev) => ({ ...prev, channels: true }))
    }

    // 从URL获取botNo参数
    const searchParams = queryString.parse(location.search) || {}
    const hashParams = queryString.parse(window.location.hash.split("?")[1] || "") || {}
    const currentBotNo = botNo || searchParams.botNo || hashParams.botNo || "20250416001"

    // API函数
    const createOrUpdateVoiceAgent = useCreateOrUpdateVoiceAgent()

    // 语音详情状态
    const [voiceDetail, setVoiceDetail] = useState(null)

    // 获取技能列表
    const { data: availableSkills = {} } = useFetchAvailableSkills({
      botNo: currentBotNo,
      pageSize: 1000,
      pageNum: 1
    })

    // 技能搜索处理
    const handleSearch = (value) => {
      setFilterText(value)
    }

    // 处理技能选项
    const skills = useMemo(() => {
      const selfSkills =
        availableSkills.selfSkills?.map((skill) => {
          return {
            ...skill,
            label: skill.skillName,
            value: skill.skillNo
          }
        }) || []
      const subscribedSkills =
        availableSkills.subscribedSkills?.map((skill) => {
          return {
            ...skill,
            label: skill.skillName,
            value: skill.skillNo
          }
        }) || []
      return [
        {
          label: "来自本机器人",
          options: selfSkills.filter((o) => o.label.includes(filterText))
        },
        {
          label: "来自其他机器人",
          options: subscribedSkills.filter((o) => o.label.includes(filterText))
        }
      ]
    }, [availableSkills, filterText])

    // 获取语音模板详情（支持agentNo参数）
    const fetchVoiceAgentDetails = useCallback(async () => {
      if (!agentDetail?.agentNo) {
        console.log("VoiceSettingsPanel: agentDetail.agentNo 不存在，跳过获取语音详情")
        return
      }

      console.log("VoiceSettingsPanel: 开始获取语音详情", {
        agentNo: agentDetail.agentNo,
        botNo: currentBotNo
      })

      try {
        setVoiceDetailsLoading(true)
        const res = await getVoiceAgentDetails({
          agentNo: agentDetail.agentNo,
          botNo: currentBotNo
        })

        console.log("VoiceSettingsPanel: 获取语音详情响应", res)

        if (res && res.status === 200 && res.data) {
          const detail = res.data
          console.log("VoiceSettingsPanel: 原始语音详情数据", detail)

          // 处理时间格式
          if (detail.allowCallTimeStart) {
            detail.allowCallTimeStart = moment(detail.allowCallTimeStart, "HH:mm")
          }
          if (detail.allowCallTimeEnd) {
            detail.allowCallTimeEnd = moment(detail.allowCallTimeEnd, "HH:mm")
          }

          // 处理开关值 - 将字符串转换为布尔值
          if (detail.taskProcessRateFlag !== undefined) {
            detail.taskProcessRateFlag = detail.taskProcessRateFlag === "1"
          }

          // 处理音色字段映射
          if (detail.voiceId) {
            detail.timbreCode = detail.voiceId
          }

          // 处理其他可能的字段映射
          if (detail.variableConfigs && typeof detail.variableConfigs === "string") {
            try {
              detail.variableConfigs = JSON.parse(detail.variableConfigs)
            } catch (e) {
              console.warn("VoiceSettingsPanel: variableConfigs 解析失败", e)
              detail.variableConfigs = []
            }
          }

          // 确保数组字段的默认值
          if (!Array.isArray(detail.variableConfigs)) {
            detail.variableConfigs = []
          }
          if (!Array.isArray(detail.ccConfigs)) {
            detail.ccConfigs = []
          }

          // 处理音量和音频速率的默认值
          if (detail.volume === null || detail.volume === undefined) {
            detail.volume = 50 // 默认音量50%
          }
          if (detail.speed === null || detail.speed === undefined) {
            detail.speed = 1.05 // 默认音频速率1.05x
          }

          // 处理robotStrategy和crossCallScriptTag的默认值
          if (detail.robotStrategy === null || detail.robotStrategy === undefined) {
            detail.robotStrategy = "merge" // 默认选中合并用户问题
          }
          if (detail.crossCallScriptTag === null || detail.crossCallScriptTag === undefined) {
            detail.crossCallScriptTag = false // 默认关闭跨通话轮询话术
          }

          // 保存taskId用于后续保存操作
          if (detail.taskId) {
            setTaskId(detail.taskId)
          }

          console.log("VoiceSettingsPanel: 处理后的语音详情数据", detail)

          // 保存语音详情数据用于话术语音模块
          setVoiceDetail(detail)

          // 回填表单数据
          form.setFieldsValue(detail)
          console.log("VoiceSettingsPanel: 表单数据已回填")

          // 验证表单数据是否正确设置
          setTimeout(() => {
            const currentFormValues = form.getFieldsValue()
            console.log("VoiceSettingsPanel: 当前表单值", currentFormValues)
          }, 100)

          // 同时通知父组件更新voiceConfig
          if (onConfigChange) {
            onConfigChange(detail)
          }
        } else {
          console.warn("VoiceSettingsPanel: 获取语音详情失败或数据为空", res)
        }
      } catch (error) {
        console.error("VoiceSettingsPanel: 获取语音模板详情失败:", error)
        message.error("获取语音模板详情失败")
      } finally {
        setVoiceDetailsLoading(false)
      }
    }, [agentDetail?.agentNo, currentBotNo, form, onConfigChange])

    // 当agentDetail变化时调用接口
    useEffect(() => {
      console.log("VoiceSettingsPanel: useEffect 触发", {
        agentNo: agentDetail?.agentNo,
        currentBotNo,
        refreshKey
      })

      if (agentDetail?.agentNo && currentBotNo) {
        fetchVoiceAgentDetails()
      }
    }, [agentDetail?.agentNo, currentBotNo, fetchVoiceAgentDetails, refreshKey])

    // 获取音色列表
    const fetchTimbreList = useCallback(async () => {
      try {
        setTimbreLoading(true)
        const res = await fetchPersonalTimbreListV2({
          botNo: currentBotNo
        })

        if (Array.isArray(res)) {
          const options = res.map((item) => ({
            value: item.timbreCode && Number(item.timbreCode),
            label: item.timbreName
          }))
          setTimbreOptions(options)
        }
      } catch (error) {
        console.error("获取音色列表异常:", error)
      } finally {
        setTimbreLoading(false)
      }
    }, [currentBotNo])

    useEffect(() => {
      fetchTimbreList()
    }, [currentBotNo, fetchTimbreList])

    // 处理表单字段变化
    const handleFormChange = (changedFields, allFields) => {
      const currentValues = form.getFieldsValue()
      onConfigChange?.(currentValues)

      // 检测变化的字段属于哪个区域
      if (changedFields && changedFields.length > 0) {
        const changedFieldNames = changedFields.map((field) => field.name[0])

        // 通用设置字段
        const generalFields = [
          "taskName",
          "redisExpireTime",
          "numberWebCall",
          "taskProcessRateFlag",
          "taskProcessRateSleepTime",
          "allowCallTimeStart",
          "allowCallTimeEnd",
          "robotStrategy",
          "crossCallScriptTag",
          "ccConfigs"
        ]

        // 声音设置字段
        const ttsFields = ["audioFormat", "sampleRate", "speed", "volume"]

        // 技能字段
        const skillsFields = [
          "reflectAigcSkillNo",
          "reflectAigcEventSkillNo",
          "intentTagCallSkillNo",
          "reflectErrOverNum"
        ]

        // 通话详情字段
        const detailsFields = ["variableConfigs"]

        // 标记相应区域有变化
        const newSectionChanges = { ...sectionChanges }

        changedFieldNames.forEach((fieldName) => {
          if (generalFields.includes(fieldName)) {
            newSectionChanges.general = true
          }
          if (ttsFields.includes(fieldName)) {
            newSectionChanges.tts = true
          }
          if (skillsFields.includes(fieldName)) {
            newSectionChanges.skills = true
          }
          if (detailsFields.includes(fieldName)) {
            newSectionChanges.details = true
          }
        })

        setSectionChanges(newSectionChanges)
      }
    }

    // 分段保存函数
    const handleSectionSave = async (section) => {
      if (!taskId) {
        message.error("缺少taskId，无法保存")
        return
      }

      if (!agentDetail?.agentNo) {
        message.error("缺少agentNo，无法保存")
        return
      }

      if (!sectionChanges[section]) {
        return // 该区域没有变化，不需要保存
      }

      try {
        setSaving(true)
        // 先校验表单
        await form.validateFields()
        const formValues = form.getFieldsValue()

        // 构建保存参数
        const params = {
          taskId: taskId,
          botNo: currentBotNo,
          agentNo: agentDetail?.agentNo,
          ...formValues,
          // 处理时间格式
          allowCallTimeStart:
            formValues.allowCallTimeStart?.format?.("HH:mm") || formValues.allowCallTimeStart,
          allowCallTimeEnd:
            formValues.allowCallTimeEnd?.format?.("HH:mm") || formValues.allowCallTimeEnd,
          // 处理开关值
          taskProcessRateFlag: formValues.taskProcessRateFlag ? "1" : "0"
        }

        const res = await createOrUpdateVoiceAgent(params)

        if (res.status === 200) {
          console.log(`${section} 区域自动保存成功`)
          // 清除该区域的变化标记
          setSectionChanges((prev) => ({
            ...prev,
            [section]: false
          }))
        } else {
          // 优先显示 data 字段的错误信息，其次是 message
          const errorMsg = res.data || res.message || "保存失败"
          message.error(errorMsg)
        }
      } catch (error) {
        // 校验不通过时不弹窗，表单会自动红字提示
        if (error && error.errorFields) return
        console.error(`${section} 区域保存失败:`, error)
        message.error("保存失败，请重试")
      } finally {
        setSaving(false)
      }
    }

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        // 获取当前表单数据
        getFormData: () => {
          return form.getFieldsValue()
        },
        // 保存语音设置
        saveVoiceSettings: async () => {
          if (!taskId) {
            throw new Error("缺少taskId，无法保存")
          }

          if (!agentDetail?.agentNo) {
            throw new Error("缺少agentNo，无法保存")
          }

          try {
            // 先校验表单
            await form.validateFields()
            const formValues = form.getFieldsValue()

            // 构建保存参数
            const params = {
              taskId: taskId,
              botNo: currentBotNo,
              agentNo: agentDetail?.agentNo,
              ...formValues,
              // 处理时间格式
              allowCallTimeStart:
                formValues.allowCallTimeStart?.format?.("HH:mm") || formValues.allowCallTimeStart,
              allowCallTimeEnd:
                formValues.allowCallTimeEnd?.format?.("HH:mm") || formValues.allowCallTimeEnd,
              // 处理开关值
              taskProcessRateFlag: formValues.taskProcessRateFlag ? "1" : "0"
            }

            const res = await createOrUpdateVoiceAgent(params)

            if (res.status === 200) {
              console.log("语音设置保存成功")
              // 清除所有变化标记
              setSectionChanges({
                general: false,
                tts: false,
                skills: false,
                details: false
              })
              return res
            } else {
              throw new Error(res.data || res.message || "语音设置保存失败")
            }
          } catch (error) {
            // 如果是表单验证错误，提供更友好的错误信息
            if (error.errorFields && error.errorFields.length > 0) {
              const errorMessages = error.errorFields
                .map((field) => {
                  const fieldName = field.name.join(".")
                  const errorMsg = field.errors.join(", ")
                  return `${fieldName}: ${errorMsg}`
                })
                .join("; ")
              throw new Error(`语音设置表单验证失败：${errorMessages}`)
            }
            console.error("语音设置保存失败:", error)
            throw error
          }
        }
      }),
      [taskId, agentDetail?.agentNo, currentBotNo, form, createOrUpdateVoiceAgent]
    )

    // 区域鼠标离开时的防抖保存
    const handleSectionMouseLeave = (section) => {
      if (sectionChanges[section] && !saving) {
        // 清除之前的定时器
        if (saveTimeouts[section]) {
          clearTimeout(saveTimeouts[section])
        }

        // 设置新的定时器，延迟500ms执行保存
        const timeout = setTimeout(async () => {
          console.log(`检测到 ${section} 区域变化，自动保存...`)
          await handleSectionSave(section)
        }, 500)

        setSaveTimeouts((prev) => ({
          ...prev,
          [section]: timeout
        }))
      }
    }

    // 组件卸载时清理所有定时器
    useEffect(() => {
      return () => {
        Object.values(saveTimeouts).forEach((timeout) => {
          if (timeout) {
            clearTimeout(timeout)
          }
        })
      }
    }, [saveTimeouts])

    // 监听音频进度
    useEffect(() => {
      const handleTimeUpdate = () => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime)
          const progressValue = (audioRef.current.currentTime / audioDuration) * 100
          setProgress(progressValue)
        }
      }

      const handleLoadedMetadata = () => {
        if (audioRef.current) {
          setAudioDuration(audioRef.current.duration)
        }
      }

      const audioElement = audioRef.current
      if (audioElement) {
        audioElement.addEventListener("timeupdate", handleTimeUpdate)
        audioElement.addEventListener("loadedmetadata", handleLoadedMetadata)
        // 清理时移除事件监听
        return () => {
          audioElement.removeEventListener("timeupdate", handleTimeUpdate)
          audioElement.removeEventListener("loadedmetadata", handleLoadedMetadata)
        }
      }
    }, [audioRef.current, audioDuration])

    // 处理语音合成
    const handleSynthesis = async () => {
      try {
        const values = form.getFieldsValue()
        const { synthesisText, timbreCode, audioFormat, sampleRate, speed, volume } = values

        // 验证必填参数
        if (!agentDetail?.agentNo) {
          message.warning("缺少agentNo，无法进行语音合成")
          return
        }

        if (!timbreCode) {
          message.warning("请先在声音设置中选择音色")
          return
        }

        if (!synthesisText) {
          message.warning("请输入要合成的文本")
          return
        }

        if (!audioFormat) {
          message.warning("请先在声音设置中选择录音格式")
          return
        }

        if (!sampleRate) {
          message.warning("请先在声音设置中选择音频采样率")
          return
        }

        if (speed === null || speed === undefined) {
          message.warning("请先在声音设置中设置音频速率")
          return
        }

        if (volume === null || volume === undefined) {
          message.warning("请先在声音设置中设置音量")
          return
        }

        setSynthesizing(true)
        setAudioUrl("") // 重置之前的音频

        const params = {
          botNo: currentBotNo,
          content: synthesisText,
          timbreCode: timbreCode,
          agentNo: agentDetail.agentNo,
          audioFormat: audioFormat,
          sampleRate: sampleRate,
          speed: speed,
          volume: volume
        }

        const res = await synthesisVoice(params)

        if (res && res.status === 200) {
          if (res.data) {
            message.success("语音合成成功")
            setAudioUrl(res.data)
            setProgress(0)
            setCurrentTime(0)
            setIsPlaying(false) // 重置播放状态
          } else {
            message.error("语音合成失败，未能获取到有效的音频文件。")
          }
        } else {
          message.error(res?.message || "语音合成失败")
        }
      } catch (error) {
        console.error("语音合成失败:", error)
        message.error("语音合成失败")
      } finally {
        setSynthesizing(false)
      }
    }

    // 播放或暂停音频
    const togglePlay = () => {
      if (audioRef.current) {
        if (isPlaying) {
          audioRef.current.pause()
        } else {
          audioRef.current.play()
        }
        setIsPlaying(!isPlaying)
      }
    }

    // 音频播放结束时的处理函数
    const handleAudioEnded = () => {
      setIsPlaying(false)
      setProgress(100)
    }

    // 格式化时间为 mm:ss 格式
    const formatTime = (time) => {
      if (isNaN(time) || time === 0) return "00:00"
      const minutes = Math.floor(time / 60)
      const seconds = Math.floor(time % 60)
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
    }

    // 处理进度条点击事件
    const handleProgressClick = (e) => {
      if (audioRef.current && audioDuration) {
        const progressBar = e.currentTarget
        const rect = progressBar.getBoundingClientRect()
        const offsetX = e.clientX - rect.left
        const newProgress = (offsetX / progressBar.offsetWidth) * 100
        const newTime = (newProgress / 100) * audioDuration

        audioRef.current.currentTime = newTime
        setProgress(newProgress)
        setCurrentTime(newTime)
      }
    }

    // 根据 voiceDetail 动态生成话术语音数据
    const voiceScriptData = voiceDetail?.scriptInfo
      ? [
          {
            key: "1",
            updateTime: voiceDetail.scriptInfo.dataTime || "--",
            nodeCount: voiceDetail.scriptInfo.count || 0,
            operation: voiceDetail.scriptInfo.count === 0 ? "编辑" : "查看"
          }
        ]
      : []

    // 话术语音表格列配置
    const voiceScriptColumns = [
      {
        title: "更新时间",
        dataIndex: "updateTime",
        key: "updateTime"
      },
      {
        title: "话术数量",
        dataIndex: "nodeCount",
        key: "nodeCount"
      },
      {
        title: "操作",
        dataIndex: "operation",
        key: "operation",
        width: 100,
        render: (text, record) => (
          <Button type="link" size="small" onClick={handleScriptNavigation}>
            {text}
          </Button>
        )
      }
    ]

    // 处理话术跳转
    const handleScriptNavigation = () => {
      if (taskId) {
        navigate(`/voice/scriptManage?id=${taskId}&botNo=${currentBotNo}`)
      } else {
        message.error("缺少必要参数，无法跳转")
      }
    }

    const [channelConfigError, setChannelConfigError] = useState(false)

    const ccConfigsEmpty = (form.getFieldValue("ccConfigs") || []).length === 0
    const variableConfigsEmpty = (form.getFieldValue("variableConfigs") || []).length === 0

    // 计算话术语音是否为空
    const voiceScriptEmpty = !voiceDetail?.scriptInfo || !voiceDetail.scriptInfo.count
    // 计算音色是否为空
    const timbreCodeEmpty = !form.getFieldValue("timbreCode")
    // 计算技能是否全部为空
    const skillsEmpty =
      !form.getFieldValue("reflectAigcSkillNo") &&
      !form.getFieldValue("reflectAigcEventSkillNo") &&
      !form.getFieldValue("intentTagCallSkillNo")

    return (
      <div>
        <div className="text-[14px] text-[#181B25] font-[500] mb-[20px]">语音设置</div>

        <Spin spinning={voiceDetailsLoading} tip="正在加载声音设置...">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              audioFormat: "wav",
              sampleRate: 8000,
              speed: 1.05,
              volume: 50,
              redisExpireTime: 86400,
              taskProcessRateFlag: false,
              variableConfigs: [],
              ccConfigs: [],
              robotStrategy: "merge",
              crossCallScriptTag: false
            }}
            onFieldsChange={handleFormChange}
          >
            {/* 话术语音 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      voiceScript: !voiceCollapsedState.voiceScript
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.voiceScript ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        话术语音
                      </span>
                      {voiceScriptEmpty && (
                        <Tag color="red" bordered={false} className="ml-2">
                          空
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.voiceScript
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
              >
                <div className="mt-3">
                  <Table
                    columns={voiceScriptColumns}
                    dataSource={voiceScriptData}
                    pagination={false}
                    size="small"
                    className="mb-4"
                  />
                </div>
              </div>
            </div>

            {/* 通用设置 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      general: !voiceCollapsedState.general
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.general ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        通用设置
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.general
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
                onMouseLeave={() => handleSectionMouseLeave("general")}
              >
                <div className="mt-3 space-y-2">
                  <Form.Item name="taskName" label="语音模版名称" className="mb-2">
                    <Input placeholder="请输入语音模版名称" allowClear />
                  </Form.Item>
                  <Form.Item
                    name="robotStrategy"
                    label="用户问题边界"
                    tooltip="当 AI 回复的时候用户正好说话，是否合并上一轮问题一起处理"
                    className="mb-2"
                    initialValue="merge"
                  >
                    <Select placeholder="请选择处理方式">
                      <Option value="merge">合并用户问题</Option>
                      <Option value="default">默认</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="crossCallScriptTag"
                    label="跨通话轮询话术"
                    tooltip="话术是否跨通话轮询，增加重复触达用户话术体验，节点递进话术多，重复触发大的情况再推荐开启"
                    valuePropName="checked"
                    className="mb-2"
                    layout="horizontal"
                  >
                    <Switch size="small" checkedChildren="开" unCheckedChildren="关" />
                  </Form.Item>
                  <Form.Item name="redisExpireTime" label="补呼时间间隔" className="mb-2">
                    <InputNumber
                      placeholder="请输入"
                      className="w-full"
                      addonAfter="秒"
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item name="numberWebCall" label="手机防疲劳" className="mb-2">
                    <InputNumber
                      placeholder="拨打最大数量"
                      className="w-full"
                      addonAfter="通"
                      allowClear
                    />
                  </Form.Item>

                  {/* 开启拨打限流 */}
                  <Form.Item
                    name="taskProcessRateFlag"
                    label="开启拨打限流"
                    valuePropName="checked"
                    tooltip="用于控制权录音或者全 tts 任务拨打速率"
                    className="mb-2"
                    layout="horizontal"
                  >
                    <Switch size="small" checkedChildren="开" unCheckedChildren="关" />
                  </Form.Item>

                  {/* 拨打限流开启后显示的字段 */}
                  <Form.Item noStyle dependencies={["taskProcessRateFlag"]}>
                    {({ getFieldValue }) =>
                      getFieldValue("taskProcessRateFlag") ? (
                        <Form.Item
                          name="taskProcessRateSleepTime"
                          label="拨打间隔时长"
                          tooltip="当启用拨打速率控制时，拨打睡眠时长"
                          className="mb-2"
                        >
                          <InputNumber
                            placeholder="请输入间隔时长"
                            className="w-full"
                            addonAfter="毫秒"
                            allowClear
                          />
                        </Form.Item>
                      ) : null
                    }
                  </Form.Item>
                  <div className="grid grid-cols-2 gap-2">
                    <Form.Item name="allowCallTimeStart" label="开始时间" className="mb-2">
                      <TimePicker
                        format="HH:mm"
                        placeholder="开始时间"
                        className="w-full"
                        allowClear
                      />
                    </Form.Item>
                    <Form.Item name="allowCallTimeEnd" label="结束时间" className="mb-2">
                      <TimePicker
                        format="HH:mm"
                        placeholder="结束时间"
                        className="w-full"
                        allowClear
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
            </div>

            {/* 通道设置 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      channels: !voiceCollapsedState.channels
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.channels ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        通道设置
                      </span>
                      {ccConfigsEmpty && (
                        <Tag color="red" bordered={false}>
                          空
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.channels
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
                onMouseLeave={() => handleSectionMouseLeave("channels")}
              >
                <div className="mt-3" ref={channelConfigRef}>
                  <Form.List name="ccConfigs">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map((field, index) => (
                          <div
                            key={field.key}
                            className="mb-4 p-4 border border-solid border-gray-200 rounded-md relative"
                          >
                            <h4 className="text-sm font-medium mb-3">通道配置{index + 1}</h4>
                            <Button
                              type="link"
                              danger
                              onClick={() => handleRemoveChannel(field.name)}
                              className="absolute right-2 top-2 text-xs"
                              icon={<PlusOutlined className="text-gray-500" rotate={45} />}
                            />
                            <div className="space-y-3">
                              <Form.Item
                                {...field}
                                name={[field.name, "ccPlatform"]}
                                label="通道平台"
                                className="mb-0"
                                rules={[{ required: true, message: "请选择通道平台" }]}
                              >
                                <Select placeholder="请选择通道平台">
                                  <Option value="cti">天润</Option>
                                  <Option value="dt">灯塔</Option>
                                  <Option value="zc">智齿</Option>
                                  <Option value="xc">XC</Option>
                                </Select>
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "bizField"]}
                                label="通道 ID"
                                className="mb-0"
                              >
                                <Input placeholder="请输入通道 ID" allowClear />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "bizTypes"]}
                                label="BizTypes"
                                className="mb-0"
                              >
                                <Select
                                  mode="tags"
                                  placeholder="请输入BizTypes"
                                  allowClear
                                  tokenSeparators={[","]}
                                />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "rate"]}
                                label="分流比例"
                                tooltip="多通道各自占比"
                                className="mb-3"
                                initialValue={100}
                                rules={[
                                  {
                                    required: true,
                                    validator: (_, value) => {
                                      const ccConfigs = Array.isArray(
                                        form.getFieldValue("ccConfigs")
                                      )
                                        ? form.getFieldValue("ccConfigs")
                                        : []
                                      const total = ccConfigs.reduce(
                                        (sum, item) => sum + (Number(item.rate) || 0),
                                        0
                                      )
                                      if (total > 100) {
                                        return Promise.reject("通道分流比例之和不能超过100%")
                                      }
                                      return Promise.resolve()
                                    }
                                  }
                                ]}
                              >
                                <Form.Item {...field} name={[field.name, "rate"]} noStyle>
                                  <InputNumber
                                    min={0}
                                    max={100}
                                    precision={0}
                                    className="w-20"
                                    formatter={(value) => `${value}%`}
                                    parser={(value) => value?.replace("%", "")}
                                    onChange={(value) => handleRateChange(value, field.name)}
                                  />
                                </Form.Item>
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "extraInfo"]}
                                label="拓展参数"
                                tooltip="JSON 格式，用来扩展额外配置"
                                className="mb-0 mt-2"
                              >
                                <Input.TextArea
                                  placeholder="请输入拓展配置参数"
                                  rows={3}
                                  allowClear
                                />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "isSkipWebcall"]}
                                label="外部发起外呼"
                                className="mb-0"
                                getValueFromEvent={(checked) => (checked ? 1 : 0)}
                                getValueProps={(value) => ({ checked: value === 1 })}
                                initialValue={0}
                                layout="horizontal"
                              >
                                <Switch size="small" checkedChildren="开" unCheckedChildren="关" />
                              </Form.Item>
                            </div>
                          </div>
                        ))}
                        {fields.length === 0 && (
                          <CustomEmpty description="暂无通道配置，请点击下方按钮添加" />
                        )}
                        {/* 分流比例总和错误提示 */}
                        {rateError && (
                          <div className="text-red-500 text-xs mb-2 mt-1">{rateError}</div>
                        )}
                        <Form.Item className="mt-4">
                          <Button
                            type="link"
                            onClick={handleAddChannel}
                            icon={<PlusOutlined />}
                            className="!text-purple-600 !p-0"
                          >
                            创建通道配置
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>
                </div>
              </div>
            </div>

            {/* TTS设置 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      tts: !voiceCollapsedState.tts
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.tts ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        声音设置
                      </span>
                      {timbreCodeEmpty && (
                        <Tag color="red" bordered={false} className="ml-2">
                          音色空
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.tts
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
                onMouseLeave={() => handleSectionMouseLeave("tts")}
              >
                <div className="mt-3 space-y-2">
                  <Form.Item name="timbreCode" label="音色" className="!mb-4">
                    <Select
                      placeholder="请选择音色"
                      loading={timbreLoading}
                      showSearch
                      optionFilterProp="label"
                      options={timbreOptions}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item name="audioFormat" label="录音格式" className="!mb-4">
                    <Select placeholder="请选择" allowClear>
                      <Option value="mp3">mp3</Option>
                      <Option value="wav">wav</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item name="sampleRate" label="音频采样率" className="!mb-4">
                    <Select placeholder="请选择" allowClear>
                      <Option value={8000}>8000 Hz</Option>
                      <Option value={16000}>16000 Hz</Option>
                      <Option value={32000}>32000 Hz</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="音频速率" className="mb-2">
                    <div className="flex items-center gap-3">
                      <div className="flex-1">
                        <Form.Item name="speed" noStyle>
                          <Slider
                            min={0.6}
                            max={2.5}
                            step={0.01}
                            tooltip={{ formatter: (value) => `${value}` }}
                          />
                        </Form.Item>
                      </div>
                      <Form.Item name="speed" noStyle>
                        <InputNumber
                          min={0.6}
                          max={2.5}
                          step={0.01}
                          precision={2}
                          className="w-20"
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>
                  <Form.Item label="音量" className="mb-2">
                    <div className="flex items-center gap-3">
                      <div className="flex-1">
                        <Form.Item name="volume" noStyle>
                          <Slider
                            min={0}
                            max={100}
                            tooltip={{ formatter: (value) => `${value}%` }}
                          />
                        </Form.Item>
                      </div>
                      <Form.Item name="volume" noStyle>
                        <InputNumber
                          min={0}
                          max={100}
                          precision={0}
                          className="w-20"
                          formatter={(value) => `${value}%`}
                          parser={(value) => value?.replace("%", "")}
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>

                  {/* 试听功能 */}
                  <div className="mt-4">
                    <Row gutter={16}>
                      <Col span={24}>
                        <Form.Item name="synthesisText" label="试听">
                          <Input.TextArea
                            placeholder="请输入要合成试听的文本"
                            rows={3}
                            className="flex-1 mr-2"
                          />
                        </Form.Item>
                        <div className="text-left">
                          <Button
                            type="primary"
                            icon={<i className="iconfont icon-zhinengyouhua"></i>}
                            onClick={handleSynthesis}
                            loading={synthesizing}
                            style={{
                              background:
                                "linear-gradient(83.59deg, #E9E8FF 6.73%, #EEC7FF 131.73%)",
                              color: "#7F56D9"
                            }}
                          >
                            合成试听
                          </Button>
                        </div>
                      </Col>
                    </Row>

                    {/* 音频播放器 */}
                    {audioUrl && (
                      <div className="mt-3 p-3 border rounded-md bg-gray-100">
                        <div className="flex items-center mb-2">
                          <div className="mr-2 flex items-center justify-center w-9 h-9 bg-orange-500 p-1 text-white rounded-md">
                            <span className="text-xs font-bold">MP3</span>
                          </div>
                          <div className="flex-1 text-sm font-medium text-gray-700 truncate">
                            已生成语音文件
                          </div>
                          {isPlaying ? (
                            <PauseCircleOutlined
                              className="text-2xl text-[#7F56D9] cursor-pointer"
                              onClick={togglePlay}
                            />
                          ) : (
                            <PlayCircleOutlined
                              className="text-2xl text-[#7F56D9] cursor-pointer"
                              onClick={togglePlay}
                            />
                          )}
                        </div>

                        <div className="flex items-center">
                          <div className="text-xs text-gray-500 mr-2">
                            {formatTime(currentTime)}
                          </div>
                          <div
                            className="flex-1 bg-gray-200 h-1 rounded cursor-pointer relative overflow-hidden"
                            onClick={handleProgressClick}
                          >
                            <div
                              className="absolute h-full bg-[#7F56D9] rounded-lg"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 ml-2">
                            {formatTime(audioDuration)}
                          </div>
                        </div>

                        <audio
                          ref={audioRef}
                          src={audioUrl}
                          onEnded={handleAudioEnded}
                          preload="metadata"
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 技能 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      skills: !voiceCollapsedState.skills
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.skills ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        技能
                      </span>
                      {skillsEmpty && (
                        <Tag color="red" bordered={false} className="ml-2">
                          空
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.skills
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
                onMouseLeave={() => handleSectionMouseLeave("skills")}
              >
                <div className="mt-3 space-y-4">
                  <Form.Item name="reflectAigcSkillNo" label="垫词技能关联" className="mb-2">
                    <Select
                      showSearch
                      placeholder="请选择技能"
                      onSearch={handleSearch}
                      filterOption={false}
                      options={skills}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item name="reflectAigcEventSkillNo" label="事件处理技能" className="mb-2">
                    <Select
                      showSearch
                      placeholder="请选择技能"
                      onSearch={handleSearch}
                      filterOption={false}
                      options={skills}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item name="intentTagCallSkillNo" label="通话打标技能" className="mb-2">
                    <Select
                      showSearch
                      placeholder="请选择技能"
                      onSearch={handleSearch}
                      filterOption={false}
                      options={skills}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item
                    name="reflectErrOverNum"
                    label="报错兜底次数"
                    tooltip="调用灵犀技能超时或者报错，外呼兜底技能处理次数，超过这个次数就会直接挂机"
                    className="mb-2"
                  >
                    <InputNumber
                      placeholder="请输入"
                      className="w-full"
                      addonAfter="次"
                      allowClear
                    />
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* 通话详情 */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center gap-[4px] cursor-pointer group w-full"
                  onClick={() =>
                    setVoiceCollapsedState({
                      ...voiceCollapsedState,
                      details: !voiceCollapsedState.details
                    })
                  }
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-[6px]">
                      <i
                        className={`text-[16px] text-[#475467] iconfont icon-Up transition-transform duration-300 group-hover:text-[#7f56d9] ${
                          voiceCollapsedState.details ? "rotate-90" : "rotate-180"
                        }`}
                      ></i>
                      <span className="text-[14px] text-[#475467] font-[500] group-hover:text-[#7f56d9]">
                        业务自定义字段
                      </span>
                      {variableConfigsEmpty && (
                        <Tag color="red" bordered={false}>
                          空
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className={`transition-all duration-300 origin-top ${
                  voiceCollapsedState.details
                    ? "transform scale-y-0 h-0 opacity-0"
                    : "transform scale-y-100 opacity-100"
                }`}
                onMouseLeave={() => handleSectionMouseLeave("details")}
              >
                <div className="mt-3">
                  <Form.List name="variableConfigs">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map((field, index) => (
                          <div
                            key={field.key}
                            className="mb-4 p-3 border border-solid border-gray-200 rounded-md relative"
                          >
                            <h4 className="text-sm font-medium mb-3">自定义字段{index + 1}</h4>
                            <Button
                              type="link"
                              danger
                              onClick={() => remove(field.name)}
                              className="absolute right-2 top-2 text-xs"
                              icon={<PlusOutlined className="text-gray-500" rotate={45} />}
                            />
                            <div className="space-y-2">
                              <Form.Item
                                {...field}
                                name={[field.name, "name"]}
                                label="字段中文名称"
                                className="mb-2"
                              >
                                <Input placeholder="请输入字段中文名称" allowClear />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "fieldName"]}
                                label="字段Key"
                                className="mb-2"
                              >
                                <Input placeholder="请输入字段Key" allowClear />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "expression"]}
                                label="字段取值表达式"
                                tooltip="通过 EL表达式构建字段的值"
                                className="mb-2"
                              >
                                <Input.TextArea
                                  placeholder="请输入字段取值表达式"
                                  rows={3}
                                  allowClear
                                />
                              </Form.Item>
                              <Form.Item
                                {...field}
                                name={[field.name, "isShow"]}
                                label="在通话记录中展示"
                                className="mb-2"
                                getValueFromEvent={(checked) => (checked ? 1 : 0)}
                                getValueProps={(value) => ({ checked: value === 1 })}
                              >
                                <Switch size="small" checkedChildren="开" unCheckedChildren="关" />
                              </Form.Item>
                            </div>
                          </div>
                        ))}
                        {fields.length === 0 && (
                          <CustomEmpty description="暂无自定义字段，请点击下方按钮添加" />
                        )}
                        <Form.Item className="mt-4">
                          <Button
                            type="link"
                            onClick={() =>
                              add({ name: "", fieldName: "", expression: "", isShow: 0 })
                            }
                            icon={<PlusOutlined />}
                            className="!text-purple-600 !p-0"
                          >
                            创建自定义字段
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>
                </div>
              </div>
            </div>
          </Form>
        </Spin>

        <div className="h-[1px] bg-[#E4E7EC]"></div>
      </div>
    )
  }
)

VoiceSettingsPanel.displayName = "VoiceSettingsPanel"

export default VoiceSettingsPanel
