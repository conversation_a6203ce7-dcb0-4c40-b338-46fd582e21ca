import React, { useState, useEffect, useCallback, useRef, useMemo } from "react"
import { message, Spin, Form } from "antd"
import { useSearchParams } from "react-router-dom"
import {
  getAgentLatestVersion,
  getAgentInUseVersion,
  saveAgentVersion,
  getAgentReleaseVersionByVersionNo
} from "@/api/agent/api"
import { convertAgentTypeForSave } from "@/utils/agentUtils"
import { useFetchMetaAgentModelList, useFetchAgentLockInfo } from "@/api/agent"
import "./detail.scss"
import PreviewAndDebug from "./components/PreviewAndDebug"
import SkillsAndKnowledge from "./components/SkillsAndKnowledge"
import ConversationFlow from "./components/ConversationFlow"

import MateAgentContent from "./components/MateAgentContent"
// import MateAgentTrainingDrawer from "./components/MateAgentTrainingDrawer"
import VoiceCanvasMode from "./components/VoiceCanvasMode"
import VoiceModeSelectionModal from "./components/VoiceModeSelectionModal"
import { useVoiceModeSelection } from "./hooks/useVoiceModeSelection"

import DetailHeader from "./components/DetailHeader"
import DetailEditor from "./components/DetailEditor"
import DetailAgentMode from "./components/DetailAgentMode"
import DetailSkill from "./components/DetailSkill"
import { useFetchLlmFilterModelType } from "@/api/common"

export const modeEnum = {
  single_agent_llm_mode: "智能模式",
  single_agent_skill_mode: "工作流模式",
  meta_agent_mode: "MetaAgent模式",
  // 语音类型专用模式
  voice_intelligent_mode: "智能模式",
  voice_canvas_mode: "画布模式",
  voice_script_mode: "剧本模式"
}

const AgentDetail = () => {
  const [searchParams] = useSearchParams()
  const agentNo = searchParams.get("agentNo")
  const queryBotNo = searchParams.get("botNo")
  const agentMode = searchParams.get("agentMode") // 2 => 语音 agent 模式，默认工作流模式
  const isShowDetail = searchParams.get("mode") === "showDetail"
  const isAgentCanRead = searchParams.get("agentCanRead") === "true"
  const originalAgentNo = searchParams.get("originalAgentNo")
  const [agentDetail, setAgentDetail] = useState(null)

  const botNo = useMemo(() => {
    return isShowDetail && agentDetail?.botNo ? agentDetail.botNo : queryBotNo
  }, [agentDetail?.botNo, isShowDetail, queryBotNo])

  const [loading, setLoading] = useState(false)
  // const [mode, setMode] = useState(agentMode == 2 ? "voice_canvas_mode" : "single_agent_llm_mode")
  const [mode, setMode] = useState(
    agentMode == 4
      ? "meta_agent_mode"
      : agentMode == 2
        ? "voice_intelligent_mode"
        : "single_agent_llm_mode"
  )
  const [markdownContent, setMarkdownContent] = useState("")
  const [selectedTools, setSelectedTools] = useState([])
  const [selectedSkills, setSelectedSkills] = useState([])
  const [knowledgeBases, setKnowledgeBases] = useState([])
  const [conversationFlows, setConversationFlows] = useState([])
  const [lastSavedTime, setLastSavedTime] = useState(null)
  const [isSaving, setIsSaving] = useState(false)
  const [selectedModel, setSelectedModel] = useState("default")
  const [workSkillNos, setWorkSkillNos] = useState([])
  const { data: modelList = [] } = useFetchLlmFilterModelType(botNo)
  const [agentVars, setAgentVars] = useState([])
  const [voiceConfig, setVoiceConfig] = useState({
    engine: "azure",
    voice: "xiaoxiao",
    language: "zh-CN",
    speed: 1.0,
    pitch: 1.0,
    volume: 80,
    enableASR: true,
    enableInterrupt: false,
    silenceTimeout: 3,
    customPrompts: ""
  })
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isDebugSuccessful, setIsDebugSuccessful] = useState(false)
  const [releaseForm] = Form.useForm()
  const [selectedSkill, setSelectedSkill] = useState(null)
  const [releaseNewStatus, setReleaseNewStatus] = useState(null)
  const [isNeedExecuteDefaultAgent, setIsNeedExecuteDefaultAgent] = useState(true)
  const [voiceRefreshKey, setVoiceRefreshKey] = useState(0)

  // 记录语音-通话详情数据
  const [variableConfigs, setvariableConfigs] = useState(null)

  // 创建语音组件的 ref
  const voiceSettingsPanelRef = useRef(null) // 用于 voice_intelligent_mode
  const voiceCanvasModeRef = useRef(null) // 用于 voice_canvas_mode 和 voice_script_mode

  // 使用语音模式选择Hook
  const {
    showVoiceModeModal,
    isVoiceModeSelecting,
    isVoiceModeSelected,
    checkAndHandleVoiceModeSelection,
    createHandleVoiceModeSelection
  } = useVoiceModeSelection()

  const { data: metaAgentModelList, isLoading: isMetaAgentModelListLoading } =
    useFetchMetaAgentModelList({
      botNo
    })

  const { data: lockData } = useFetchAgentLockInfo({
    botNo,
    agentNo
  })

  // 获取最新版本
  const fetchLatestVersion = async (type = "", versionNo = "") => {
    if (!agentNo) return
    setLoading(true)
    try {
      // type === "insetCurrentVersion" // 载入历史版本
      const apiFetch =
        type === "insetCurrentVersion" // 载入历史版本
          ? getAgentReleaseVersionByVersionNo
          : isShowDetail
            ? getAgentInUseVersion
            : getAgentLatestVersion
      let response = null
      if (type === "insetCurrentVersion") {
        // 载入历史版本
        response = await apiFetch({ agentNo, versionNo })
      } else {
        response = await apiFetch(agentNo, metaAgentModelList?.[0])
      }
      if (response.success) {
        setAgentDetail(response.data)
        setMarkdownContent(response.data.prompt || "")
        setSelectedTools(response.data.pluginNos || [])
        setSelectedSkills(response.data.skillNos || [])
        setKnowledgeBases(response.data.knowledgeBases || [])
        setWorkSkillNos(response.data.workSkillNos || [])
        setAgentVars(response.data.vars || [])
        setIsNeedExecuteDefaultAgent(response.data.isNeedExecuteDefaultAgent)

        // 设置模型
        if (response.data.model) {
          setSelectedModel(response.data.model)
        }

        // 设置对话流
        if (response.data.conversationFlows) {
          setConversationFlows(response.data.conversationFlows)
        }
        // 设置模式
        if (response.data.type) {
          // 如果是语音 agent 模式(agentMode=2)，则需要根据语音详情的flowType设置模式
          if (agentMode == 2) {
            // 需要调用语音详情接口获取flowType来确定具体的语音模式
            try {
              const { getVoiceAgentDetails } = await import("@/api/voiceAgent/api")
              const voiceDetailRes = await getVoiceAgentDetails({
                agentNo: response.data.agentNo,
                botNo: botNo
              })

              if (voiceDetailRes && voiceDetailRes.status === 200 && voiceDetailRes.data) {
                const flowType = voiceDetailRes.data.flowType
                setvariableConfigs(voiceDetailRes.data.variableConfigs)

                // 使用Hook处理语音模式选择逻辑
                const selectedMode = checkAndHandleVoiceModeSelection(flowType)
                setMode(selectedMode)
              } else {
                // 如果获取语音详情失败，默认使用智能模式
                const selectedMode = checkAndHandleVoiceModeSelection(3) // 默认智能模式
                setMode(selectedMode)
              }
            } catch (error) {
              console.error("获取语音详情失败:", error)
              // 如果获取语音详情失败，默认使用智能模式
              setMode("voice_intelligent_mode")
            }
          } else if (agentMode == 4) {
            setMode("meta_agent_mode")
          } else {
            setMode(response.data.type)
          }
        }

        // 更新语音设置刷新key，触发VoiceSettingsPanel重新获取详情
        if (agentMode == 2) {
          setVoiceRefreshKey((prev) => prev + 1)
        }
      } else {
        response.message && message.error(response.message)
      }
    } catch (error) {
      console.error("Failed to fetch agent:", error)
      message.error("获取Agent失败")
    } finally {
      setLoading(false)
    }
  }

  // 创建语音模式选择处理函数
  const handleVoiceModeSelection = createHandleVoiceModeSelection(
    agentDetail,
    setMode,
    fetchLatestVersion
  )

  // 处理工具授权变更
  const handleToolApproveChange = useCallback(() => {
    fetchLatestVersion()
  }, [agentNo, JSON.stringify(metaAgentModelList)])

  // 刷新releaseStatus状态
  const refreshReleaseStatus = async (agentNo) => {
    if (!agentNo) return
    try {
      const apiFetch = isShowDetail ? getAgentInUseVersion : getAgentLatestVersion
      const response = await apiFetch(agentNo, metaAgentModelList?.[0])
      if (response.success) {
        // 只更新发布状态，不影响其他状态
        setReleaseNewStatus(response.data.releaseStatus) //
      } else {
        response.message && message.error(response.message)
      }
    } catch (error) {
      console.error("Failed to refresh release status:", error)
    }
  }

  useEffect(() => {
    !isMetaAgentModelListLoading && fetchLatestVersion()
  }, [agentNo, isMetaAgentModelListLoading])

  // 检测未保存的更改
  useEffect(() => {
    if (agentDetail) {
      // 简单检查是否有未保存的更改
      const originalPrompt = agentDetail.prompt || ""
      const hasChanges = originalPrompt !== markdownContent
      setHasUnsavedChanges(hasChanges)
    }
  }, [agentDetail, markdownContent])

  // 保存函数
  const handleSave = useCallback(
    async (
      isMsg = false,
      {
        pluginNos,
        skillNos,
        knowledgeBases: newKnowledgeBases,
        workSkillNos = [],
        isNeedExecuteAgent
      } = {},
      content,
      type,
      model
    ) => {
      if (!agentDetail) return
      if (!isMsg && lockData?.locked) isMsg = true
      try {
        // 处理type转换：voice_intelligent_mode 需要转换为 single_agent_llm_mode
        const currentType = type || mode
        let convertedType = convertAgentTypeForSave(currentType)
        // 如果是语音 agent 模式(agentMode=2)，则需要转换为 single_agent_llm_mode
        if (agentMode == 2) {
          convertedType = "single_agent_llm_mode"
        }

        const payload = {
          agentNo: agentDetail.agentNo,
          versionNo: agentDetail.versionNo,
          prompt: content,
          pluginNos: pluginNos || selectedTools,
          skillNos: skillNos || selectedSkills,
          knowledgeBases: newKnowledgeBases || knowledgeBases,
          vars: agentVars.map(({ varNo, isNew, ...rest }) => rest) || [],
          type: convertedType,
          model: model || selectedModel,
          agentMode: agentMode || undefined,
          voiceTemplateNo: agentMode == 2 ? workSkillNos[0] || undefined : undefined,
          isNeedExecuteDefaultAgent:
            mode === "meta_agent_mode"
              ? (isNeedExecuteAgent ?? isNeedExecuteDefaultAgent)
              : undefined
        }

        // Only include workSkillNo in workflow mode
        if (type === "single_agent_skill_mode") {
          payload.workSkillNos = workSkillNos && workSkillNos.length ? workSkillNos : []
          payload.conversationFlows = conversationFlows
        }

        const response = await saveAgentVersion(payload)
        if (response.success) {
          isMsg && message.success("保存成功")
          typeof isNeedExecuteAgent === "boolean" &&
            setIsNeedExecuteDefaultAgent(isNeedExecuteAgent)
          setLastSavedTime(new Date())
          setHasUnsavedChanges(false)
        } else {
          isMsg && message.error(response.message || "保存失败")
        }
      } catch (error) {
        console.error("Failed to save agent:", error)
        isMsg && message.error("保存失败")
      } finally {
        setIsSaving(false)
      }
    },
    [
      lockData,
      agentDetail,
      selectedTools,
      selectedSkills,
      knowledgeBases,
      agentVars,
      mode,
      selectedModel,
      agentMode,
      conversationFlows,
      isNeedExecuteDefaultAgent
    ]
  )

  // 处理工具和技能的更新
  const handleSkillsAndToolsUpdate = useCallback(
    ({ pluginNos, skillNos, knowledgeBases: newKnowledgeBases }) => {
      // 检查是否有实际变化
      const hasToolsChanged = JSON.stringify(pluginNos) !== JSON.stringify(selectedTools)
      const hasSkillsChanged = JSON.stringify(skillNos) !== JSON.stringify(selectedSkills)
      const hasKnowledgeChanged =
        JSON.stringify(newKnowledgeBases) !== JSON.stringify(knowledgeBases)

      if (hasToolsChanged || hasSkillsChanged || hasKnowledgeChanged) {
        setSelectedTools(pluginNos)
        setSelectedSkills(skillNos)
        setKnowledgeBases(newKnowledgeBases)
        handleSave(
          false,
          { workSkillNos, skillNos, pluginNos, knowledgeBases: newKnowledgeBases },
          markdownContent,
          mode,
          selectedModel
        )
      }
    },
    [
      selectedTools,
      selectedSkills,
      knowledgeBases,
      handleSave,
      markdownContent,
      mode,
      selectedModel,
      workSkillNos
    ]
  )

  useEffect(() => {
    setReleaseNewStatus(agentDetail?.releaseStatus)
  }, [agentDetail?.releaseStatus])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#EFF1F4]">
      {/* Header */}
      <DetailHeader
        botNo={botNo}
        agentMode={agentMode}
        lockData={lockData}
        agentDetail={agentDetail}
        selectedSkills={selectedSkills}
        selectedTools={selectedTools}
        knowledgeBases={knowledgeBases}
        workSkillNo={workSkillNos}
        hasUnsavedChanges={hasUnsavedChanges}
        markdownContent={markdownContent}
        selectedSkill={selectedSkill}
        mode={mode}
        selectedModel={selectedModel}
        handleSave={(...args) => {
          handleSave(...args)
          if (agentMode == 1) {
            // 保存后调用 调用 状态接口
            refreshReleaseStatus(agentNo)
          }
        }}
        setIsDebugSuccessful={setIsDebugSuccessful}
        isSaving={isSaving}
        setIsSaving={setIsSaving}
        releaseNewStatus={releaseNewStatus}
        isOnlyRead={isShowDetail}
        originalAgentNo={originalAgentNo !== "undefined" ? originalAgentNo : undefined}
        fetchLatestVersion={(type = "", versionNo = "") => {
          if (type === "insetCurrentVersion") {
            // 载入历史版本 == getAgentReleaseVersionByVersionNo
            fetchLatestVersion(type, versionNo)
          } else {
            fetchLatestVersion()
          }
        }}
        voiceRef={
          mode === "voice_intelligent_mode"
            ? voiceSettingsPanelRef
            : mode === "voice_canvas_mode" || mode === "voice_script_mode"
              ? voiceCanvasModeRef
              : null
        }
      />

      {/* Sub Header */}
      <div className="flex items-center justify-between h-[calc(100vh-68px)] overflow-hidden border-b border-gray-100">
        <div className="flex gap-[8px] w-full h-full">
          <div
            className={`bg-white rounded-[8px] transition-all min-w-[635px] duration-300 ease-in-out ${mode === "single_agent_llm_mode" || mode === "voice_intelligent_mode" ? "w-[60%]" : "w-[60%]"}`}
          >
            <div className="flex items-center justify-between px-[20px] py-[18px]">
              <div className="flex items-center">
                <span className="text-[#181B25] text-[16px] font-[500]">编排</span>
                {mode !== "meta_agent_mode" && (
                  <DetailAgentMode
                    mode={mode}
                    agentMode={agentMode}
                    setMode={setMode}
                    fetchLatestVersion={fetchLatestVersion}
                    agentDetail={agentDetail}
                    isOnlyRead={isShowDetail}
                    isVoiceModeSelected={isVoiceModeSelected}
                  />
                )}
              </div>
              <div className="flex items-center space-x-2">
                {!["single_agent_skill_mode", "meta_agent_mode"].includes(mode) && (
                  <DetailSkill
                    modelList={modelList}
                    selectedModel={selectedModel}
                    agentDetail={agentDetail}
                    onDropClick={({ key }, filteredModelList) => {
                      const selectedModel = filteredModelList?.find((model) => model.code === key)
                      if (selectedModel?.supportToolCall) {
                        setSelectedModel(key)
                        if (agentDetail) {
                          handleSave(
                            false,
                            { workSkillNos, knowledgeBases: knowledgeBases },
                            markdownContent,
                            mode,
                            key
                          )
                        }
                      }
                    }}
                    setSelectedModel={setSelectedModel}
                    isOnlyRead={isShowDetail}
                  />
                )}
              </div>
            </div>
            <div className="h-[1px] bg-[#E4E7EC]"></div>
            <div>
              {isShowDetail && !isAgentCanRead ? (
                <div className="text-[#777] text-[18px] w-[100%] flex items-center justify-center h-[calc(100vh-68px)]">
                  Agent详情未开放
                </div>
              ) : (
                <div className="flex">
                  {/* Left Content - 角色与逻辑回复 */}
                  {["single_agent_llm_mode", "meta_agent_mode", "voice_intelligent_mode"].includes(
                    mode
                  ) && (
                    <DetailEditor
                      onOptimizeSubmit={({ type, content }) => {
                        if (type === "agent") {
                          setMarkdownContent(content)
                          handleSave(
                            false,
                            { workSkillNos, knowledgeBases: knowledgeBases },
                            content,
                            mode,
                            selectedModel
                          )
                        }
                      }}
                      onEditorBlur={(e) => {
                        handleSave(
                          false,
                          { workSkillNos, knowledgeBases: knowledgeBases },
                          e.target.value,
                          mode,
                          selectedModel
                        )
                      }}
                      markdownContent={markdownContent}
                      setMarkdownContent={setMarkdownContent}
                      agentNo={agentNo}
                      disabled={isShowDetail}
                    />
                  )}

                  {/* Middle Content - 优化区域 */}
                  <div
                    className={`flex-1 max-h-[calc(100vh-130px)] overflow-y-auto min-w-[300px] py-[20px] ${mode === "single_agent_llm_mode" || mode === "voice_intelligent_mode" || mode === "voice_canvas_mode" || mode === "voice_script_mode" ? "w-[60%] pr-[20px] pl-[10px]" : "px-[20px]"}`}
                  >
                    {mode === "single_agent_llm_mode" && (
                      <SkillsAndKnowledge
                        botNo={botNo}
                        agentDetail={agentDetail}
                        selectedTools={selectedTools}
                        setSelectedTools={setSelectedTools}
                        selectedSkills={selectedSkills}
                        setSelectedSkills={setSelectedSkills}
                        knowledgeBases={knowledgeBases}
                        setKnowledgeBases={setKnowledgeBases}
                        onSave={handleSkillsAndToolsUpdate}
                        onToolApproveChange={handleToolApproveChange}
                        prompt={markdownContent}
                        type={mode}
                        model={selectedModel}
                        agentMode={agentMode}
                        voiceTemplateNo={workSkillNos[0]}
                        agentVars={agentVars}
                        setAgentVars={setAgentVars}
                        isOnlyRead={isShowDetail}
                      />
                    )}
                    {mode === "single_agent_skill_mode" && (
                      <div className="flex-1 flex flex-col gap-[20px]">
                        <ConversationFlow
                          onAddFlow={async (skills, workSkillNos) => {
                            if (!workSkillNos) return
                            setSelectedSkill(skills)
                            setWorkSkillNos(workSkillNos)
                            await handleSave(
                              false,
                              { workSkillNos, knowledgeBases: knowledgeBases },
                              markdownContent,
                              mode,
                              selectedModel
                            )
                            if (agentMode == 1) {
                              // 保存后调用 调用 状态接口
                              refreshReleaseStatus(agentNo)
                            }
                          }}
                          initSelectedSkill={(skills) => {
                            setSelectedSkill(skills)
                          }}
                          botNo={botNo}
                          agentDetail={{ ...agentDetail, workSkillNos }}
                          agentMode={agentMode}
                          originalAgentNo={
                            originalAgentNo !== "undefined" ? originalAgentNo : undefined
                          }
                          isOnlyRead={isShowDetail}
                          isNeedExecuteDefaultAgent={isNeedExecuteDefaultAgent}
                        />
                      </div>
                    )}

                    {mode === "meta_agent_mode" && (
                      <div className="flex-1 flex flex-col gap-[20px]">
                        <MateAgentContent
                          knowledgeBases={knowledgeBases}
                          selectedSkills={selectedSkills}
                          setSelectedSkills={setSelectedSkills}
                          selectedTools={selectedTools}
                          setSelectedTools={setSelectedTools}
                          onSave={handleSkillsAndToolsUpdate}
                          metaAgentModelList={metaAgentModelList}
                          loading={loading}
                          agentDetail={agentDetail}
                          isNeedExecuteDefaultAgent={isNeedExecuteDefaultAgent}
                          handleSave={(bool) =>
                            handleSave(
                              false,
                              {
                                workSkillNo: workSkillNos[0],
                                knowledgeBases: knowledgeBases,
                                isNeedExecuteAgent: bool
                              },
                              markdownContent,
                              mode,
                              selectedModel
                            )
                          }
                          isOnlyRead={isShowDetail}
                        />
                      </div>
                    )}

                    {/* 语音智能模式 */}
                    {mode === "voice_intelligent_mode" && (
                      <SkillsAndKnowledge
                        ref={voiceSettingsPanelRef}
                        botNo={botNo}
                        agentDetail={agentDetail}
                        selectedTools={selectedTools}
                        setSelectedTools={setSelectedTools}
                        selectedSkills={selectedSkills}
                        setSelectedSkills={setSelectedSkills}
                        knowledgeBases={knowledgeBases}
                        setKnowledgeBases={setKnowledgeBases}
                        onSave={handleSkillsAndToolsUpdate}
                        onToolApproveChange={handleToolApproveChange}
                        prompt={markdownContent}
                        type={mode}
                        model={selectedModel}
                        agentMode={agentMode}
                        voiceTemplateNo={workSkillNos[0]}
                        agentVars={agentVars}
                        setAgentVars={setAgentVars}
                        voiceConfig={voiceConfig}
                        onVoiceConfigChange={setVoiceConfig}
                        onVoiceConfigSave={(config) => {
                          console.log("保存语音配置:", config)
                        }}
                        isOnlyRead={isShowDetail}
                        voiceRefreshKey={voiceRefreshKey}
                      />
                    )}

                    {/* 语音画布模式 */}
                    {mode === "voice_canvas_mode" && (
                      <VoiceCanvasMode
                        ref={voiceCanvasModeRef}
                        botNo={botNo}
                        agentDetail={agentDetail}
                      />
                    )}

                    {/* 语音剧本模式 */}
                    {mode === "voice_script_mode" && (
                      <VoiceCanvasMode
                        ref={voiceCanvasModeRef}
                        botNo={botNo}
                        agentDetail={agentDetail}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <PreviewAndDebug
            isChat={isShowDetail}
            className={`transition-all duration-300 ease-in-out ${mode === "single_agent_llm_mode" || mode === "voice_intelligent_mode" || mode === "voice_canvas_mode" || mode === "voice_script_mode" ? "w-[40%]" : "w-[50%]"}`}
            onDebugSuccess={() => setIsDebugSuccessful(true)}
            botNo={botNo}
            agentName={agentDetail?.agentName}
            selectedSkill={selectedSkill}
            agentNo={agentNo}
            agentVersionNo={agentDetail?.versionNo}
            mode={mode}
            refreshReleaseStatus={refreshReleaseStatus}
            agentMode={agentMode}
            variableConfigs={variableConfigs}
          />
        </div>
      </div>

      {/* 语音模式选择弹窗 */}
      <VoiceModeSelectionModal
        visible={showVoiceModeModal}
        onConfirm={handleVoiceModeSelection}
        loading={isVoiceModeSelecting}
      />
    </div>
  )
}

export default AgentDetail
