.loadingWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F8F8F9;
  padding: 12px 16px;
  border-radius: 16px 16px 16px 0px;
}

.text {
  color: #000;
  font-size: 14px;
  font-weight: 400;
  margin-right: 8px;
}
.circle {
  width: 10px;
  height: 10px;
  margin: 0 2px;
  border-radius: 50%;
  background-color: rgba(93, 95, 239, 1);
  animation: circle-animation 2s infinite ease-in-out;
}

.circle:nth-child(2) {
  animation-delay: 0.8s;
}

.circle:nth-child(3) {
  animation-delay: 1.6s;
}

@keyframes circle-animation {
  0%,
  72% {
    opacity: 0.3;
  }
  12%,
  84% {
    opacity: 0.6;
  }
  24%,
  96% {
    opacity: 1;
  }
}
