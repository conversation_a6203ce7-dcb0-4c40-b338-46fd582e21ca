import { useSaveTool, useSaveInputVariables, useSaveOutputVariables } from "@/api/pluginTool"
import { Space, Button, message } from "antd"
import queryString from "query-string"

function FooterHandle({
  current,
  total,
  form,
  pluginNo,
  toolNo,
  setCurrent,
  queryParams,
  navigate,
  location,
  botNo,
  outputParamsRef
}) {
  const hasNext = current < total - 1
  const hasPrev = current > 0
  const { mutate: saveTool } = useSaveTool()
  const { mutate: saveInputVariables } = useSaveInputVariables()
  const { mutate: saveOutputVariables } = useSaveOutputVariables()
  const { type = "NORMAL", mcpToolNo } = queryParams

  const handleSubmit = () => {
    if (current === 3) {
      submitDebug()
      return
    }
    form.validateFields().then((values) => {
      console.log(values)
      switch (current) {
        case 0:
          submitBasicInfo(values)
          break
        case 1:
          submitInputParams(values)
          break
        case 2:
          submitOutputParams(values)
          break
        default:
          break
      }
      // handleNext()
    })
  }

  const submitBasicInfo = (values) => {
    saveTool(
      {
        ...values,
        pluginNo,
        toolNo: toolNo ?? "",
        botNo,
        type: values.type || type,
        mcpToolNo: type === "MCP" ? mcpToolNo : undefined
      },
      {
        onSuccess: (e) => {
          if (e.success === true) {
            message.info(e.message)
            handleNext({ toolNo: e.data })
          } else {
            message.error(e.message)
          }
        },
        onError: (e) => {
          message.error(e.message)
        }
      }
    )
  }
  const submitInputParams = (values) => {
    const dataArray = Object.entries(values).map(([id, value]) => {
      return { ...value, id }
    })
    console.log("保存输入参数:", { values, type, mcpToolNo })
    saveInputVariables(
      {
        pluginNo,
        toolNo,
        variables: dataArray,
        type,
        mcpToolNo: type === "MCP" ? mcpToolNo : undefined
      },
      {
        onSuccess: (e) => {
          if (e.success === true) {
            message.info(e.message)
            handleNext()
          } else {
            message.error(e.message)
          }
        },
        onError: (e) => {
          message.error(e.message)
        }
      }
    )
  }

  const submitOutputParams = (values) => {
    form.validateFields().then(() => {
      const formData = outputParamsRef.current.getValues()
      console.log("保存输出参数:", { formData, type, mcpToolNo })
      saveOutputVariables(
        {
          pluginNo,
          toolNo,
          variables: formData,
          type,
          mcpToolNo: type === "MCP" ? mcpToolNo : undefined
        },
        {
          onSuccess: (e) => {
            if (e.success === true) {
              message.info(e.message)
              if (e.data) {
                handleNext({ toolNo: e.data })
              } else {
                handleNext()
              }
            } else {
              message.error(e.message)
            }
          },
          onError: (e) => {
            message.error(e.message)
          }
        }
      )
    })
  }
  const submitDebug = () => {
    navigate(`/plugin/${pluginNo}?botNo=${botNo}`)
  }

  const handleNext = (params = {}) => {
    if (current < 4) {
      const next = current + 1
      setCurrent(current)
      const updatedSearch = queryString.stringify({
        ...queryParams,
        step: next + 1,
        ...params
      })
      navigate({
        ...location,
        search: updatedSearch
      })
    }
  }

  const handlePrev = () => {
    if (current >= 1) {
      const prev = current - 1
      setCurrent(prev)
      const updatedSearch = queryString.stringify({
        ...queryParams,
        step: current
      })
      navigate({
        ...location,
        search: updatedSearch
      })
    }
  }

  return (
    <Space>
      {hasPrev && <Button onClick={handlePrev}>上一步</Button>}
      <Button onClick={handleSubmit} type="primary">
        {hasNext ? "保存并继续" : "完成"}
      </Button>
    </Space>
  )
}

export default FooterHandle
