import React, { useEffect, useState, useRef } from "react"
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Popconfirm,
  Select,
  Switch,
  Table,
  Tooltip
} from "antd"
import { DeleteOutlined, InfoCircleOutlined, PlusOutlined } from "@ant-design/icons"
import { fieldTypeOptions, inputMethodOptions } from "../../constants"
import { getRandomString } from "@/utils"
import ExpandableTextArea from "../ExpandableTextArea"

const EditableContext = React.createContext(null)

export const EditableRow = ({ form, ...props }) => {
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  )
}

export const EditableCell = ({ children, ...restProps }) => {
  return <td {...restProps}>{children}</td>
}

export const Title = ({ text, required = false, tip = "" }) => {
  return (
    <div>
      {text}
      {required && <span className="text-red-500 ml-1">*</span>}
      {tip && (
        <Tooltip title={tip}>
          <InfoCircleOutlined className="ml-1" />
        </Tooltip>
      )}
    </div>
  )
}

const InputParams = ({ form, formData, toolType }) => {
  const [dataSource, setDataSource] = useState([])
  const [count, setCount] = useState(2)
  const [_, forceUpdate] = useState({})
  const isMcpType = toolType === "MCP"

  useEffect(() => {
    if (formData) {
      const newFormData = [...formData].map((item) => {
        return {
          rowId: `${Math.random()}`,
          ...item
        }
      })
      setDataSource(newFormData)
    }
  }, [formData])

  const handleDelete = (key) => {
    const newData = dataSource.filter((item) => item.rowId !== key)
    setDataSource(newData)
  }

  const handleAdd = () => {
    const newData = {
      rowId: getRandomString(),
      variableName: "",
      variableDesc: "",
      variableValueType: "",
      inputMethod: "",
      variableRequire: "",
      defaultValue: "",
      isOpen: true
    }
    setDataSource([...dataSource, newData])
    setCount(count + 1)
  }

  const handleInputParamsTypeChange = (value, pathArray = []) => {
    console.log(value, pathArray)
    forceUpdate({})
  }

  const components = {
    body: {
      row: (props) => <EditableRow {...props} form={form} />,
      cell: (props) => <EditableCell {...props} />
    }
  }

  const defaultColumns = [
    {
      title: <Title text="参数名称" required={true} tip="参数名称不能为空且不能重复" />,
      message: "参数名称不能为空且不能重复",
      placeholder: "请输入参数名称",
      dataIndex: "variableName",
      width: "200px",
      render: (text, record) => {
        return (
          <Form.Item
            name={[record.rowId, "variableName"]}
            initialValue={text}
            rules={[
              {
                required: true,
                message: `请输入参数名称`
              }
            ]}
          >
            <Input placeholder="请输入参数名称" disabled={isMcpType && !!text} />
          </Form.Item>
        )
      }
    },
    {
      title: <Title text="参数描述" required={true} tip="参数描述不能为空" />,
      message: "参数描述不能为空",
      placeholder: "请输入参数描述",
      dataIndex: "variableDesc",
      render: (text, record) => {
        return (
          <Form.Item
            name={[record.rowId, "variableDesc"]}
            initialValue={text}
            rules={[
              {
                required: true,
                message: `请输入参数描述`
              }
            ]}
          >
            <Input placeholder="请输入参数描述" />
          </Form.Item>
        )
      }
    },
    {
      title: <Title text="参数类型" required={true} tip="参数类型不能为空" />,
      dataIndex: "variableValueType",
      width: "173px",
      render: (text, record) => {
        return (
          <Form.Item
            name={[record.rowId, "variableValueType"]}
            initialValue={text || "string"}
            rules={[
              {
                required: true,
                message: "参数类型不能为空"
              }
            ]}
          >
            <Select
              placeholder="请输入请求方法"
              options={fieldTypeOptions}
              onChange={(value) => {
                handleInputParamsTypeChange(value, [record.rowId, "variableValueType"])
              }}
            />
          </Form.Item>
        )
      }
    },
    {
      title: <Title text="传入方法" required={true} tip="传入方法不能为空" />,
      dataIndex: "inputMethod",
      width: "173px",
      render: (text, record) => {
        return (
          <Form.Item
            name={[record.rowId, "inputMethod"]}
            initialValue={text || "Body"}
            rules={[
              {
                required: true,
                message: "传入方法不能为空"
              }
            ]}
          >
            <Select placeholder="请输入传入方法" options={inputMethodOptions} />
          </Form.Item>
        )
      }
    },
    {
      title: "是否必填",
      dataIndex: "variableRequire",
      width: "90px",
      render: (text, record) => {
        return (
          <Form.Item
            name={[record.rowId, "variableRequire"]}
            valuePropName="checked"
            initialValue={text === false ? false : true}
          >
            <Checkbox defaultChecked={text} />
          </Form.Item>
        )
      }
    },
    {
      title: "默认值",
      dataIndex: "defaultValue",
      width: "120px",
      render: (text, record) => {
        const variableValueType = form.getFieldValue([record.rowId, "variableValueType"])
        const defaultValue =
          text || variableValueType === "json" ? "{}" : variableValueType === "array" ? "[]" : ""
        return (
          <Form.Item
            name={[record.rowId, "defaultValue"]}
            initialValue={text || defaultValue}
            rules={[
              {
                required: false,
                message: `请输入默认值`
              }
            ]}
          >
            {["json", "array"].includes(variableValueType) ? (
              <ExpandableTextArea
                title="默认值"
                type={variableValueType}
                placeholder={`请输入默认值,${variableValueType}格式`}
              />
            ) : (
              <Input placeholder="请输入默认值" />
            )}
          </Form.Item>
        )
      }
    },
    {
      title: "开启",
      dataIndex: "isOpen",
      width: "90px",
      render: (text, record) => {
        return (
          <Form.Item name={[record.rowId, "isOpen"]} initialValue={text}>
            <Switch defaultChecked={text} />
          </Form.Item>
        )
      }
    },
    {
      title: "操作",
      width: "90px",
      dataIndex: "operation",
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Popconfirm title="你确定要删除吗?" onConfirm={() => handleDelete(record.rowId)}>
            <a className=" cursor-pointer">
              <DeleteOutlined />
            </a>
          </Popconfirm>
        ) : null
    }
  ]

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        rowId: record.rowId,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        message: col.message,
        placeholder: col.placeholder,
        required: col.required
      })
    }
  })

  return (
    <div>
      <h4 className="text-xl font-bold mb-3">配置输入参数</h4>
      <Table
        components={components}
        rowClassName={() => "editable-row"}
        bordered
        pagination={false}
        dataSource={dataSource}
        columns={columns}
        rowKey="rowId"
      />
      <Col span={24}>
        <Button icon={<PlusOutlined />} className=" mt-2 mb-8" onClick={handleAdd}>
          新增参数
        </Button>
      </Col>
    </div>
  )
}

export default InputParams
