.lg\:prose-xl {
  font-size: 1.1rem !important;
}

.catalogs-collapse {
  border-radius: 0;

  .ant-collapse-item {
    border-radius: 0 !important;
  }

  .ant-collapse-content-box {
    padding: 0 !important;
  }

  .catalogs-collapse-item {
    .ant-collapse-header {
      background: #fff;
      cursor: auto;
      color: #000;
    }
  }
}

.call-logs-table {
  .ant-table-column-title {
    overflow: hidden;
  }
}

.history-content {
  max-height: 0;
  overflow: hidden;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.history-content.open {
  max-height: 2000px; /* 设置一个足够大的值以适应内容 */
  visibility: visible;
}

.custom-collapse {
  .ant-collapse-content-box {
    padding: 0 !important;
  }

  .ant-collapse-header {
    padding: 0 !important;
  }
}

.item-title {
  color: #475467;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.item-value {
  color: #181B25;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
}

.optimize-advice-panel {
  p {
    font-size: 14px;
  }
  .ai-optimize-advice-h {
    background-image: url("https://alicdn.zaticdn.com/zaip/zaip-toolweb-file-service/upload/9Nq8yQTFFdE82e69U6YSXt-AI-star.png");
    background-size: 18px 18px;
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 22px;
  }
}
