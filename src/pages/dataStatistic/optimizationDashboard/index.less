.overview-card {
  .overview-items {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    padding: 24px;
    background: #fff;
    border-radius: 8px;

    .overview-item {
      flex: 1;
      text-align: center;
      padding: 0 24px;
      position: relative;

      &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 60%;
        background-color: #e5e7eb;
      }

      .label {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .value {
        font-size: 28px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;

        &.purple {
          color: #8b5cf6;
        }
      }

      .sub-label {
        color: #6b7280;
        font-size: 12px;
        margin-top: 12px;
      }

      .sub-value {
        color: #1f2937;
        font-size: 14px;
        margin-top: 4px;
      }
    }
  }
}

.chart-period {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  border-radius: 4px;
  background-color: #f3f4f6;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;

  span {
    padding: 2px 8px;
    border-radius: 2px;
    transition: all 0.3s;

    &:hover {
      background-color: #e5e7eb;
    }

    &.active {
      background-color: #fff;
      color: #1f2937;
    }
  }
}

:global {
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

    .ant-card-head {
      min-height: 48px;
      padding: 0 16px;
      border-bottom: none;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
      }

      .ant-card-extra {
        padding: 12px 0;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  .ant-tabs-nav {
    margin-bottom: 16px !important;
  }
}
