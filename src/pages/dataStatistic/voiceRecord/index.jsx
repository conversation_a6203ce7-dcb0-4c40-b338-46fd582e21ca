import { useMemo, useRef, useState, useCallback } from "react"
import TableRender from "table-render"
import { getSchema } from "./schema"
import { searchApi } from "./request"
import { cloneDeep, pad } from "lodash"
import { Button, Form } from "antd"
import { useFetchAgentList } from "@/api/agent"
import RangeTimePicker from "@/components/RangeTime"
import { useLocation } from "react-router-dom"
import queryString from "query-string"
import { TableFilter } from "@/utils/tableFliter"
import ConsultRecordDrawer from "../voiceRecordDetail/Drawer"
import "./index.less"

const maskPhoneNumber = (phoneNumber, start = 3, length = 4, mask = "*") => {
  if (typeof phoneNumber !== "string") return phoneNumber || "-"
  const maskString = pad("", length, mask)
  return phoneNumber.slice(0, start) + maskString + phoneNumber.slice(start + length)
}

const formatDuration = (totalSeconds) => {
  const days = Math.floor(totalSeconds / (3600 * 24))
  const hours = Math.floor((totalSeconds % (3600 * 24)) / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  const parts = []
  if (days > 0) parts.push(`${days}d`)
  if (hours > 0) parts.push(`${hours}h`)
  if (minutes > 0) parts.push(`${minutes}m`)
  if (seconds > 0) parts.push(`${seconds}s`)

  return parts.join("") || "0s"
}

const VoiceRecord = () => {
  const location = useLocation()
  const { search } = location
  const queryParams = queryString.parse(search)
  const { botNo } = queryParams
  const [form] = Form.useForm()
  const [columnsSettingValue, setColumnsSettingValue] = useState([]) // 列设置
  const [realTotal, setRealTotal] = useState(0)
  const [searchParams, setSearchParams] = useState({})
  const [drawerData, setDrawerData] = useState({})
  const searchInput = useRef(null)
  const tableRef = useRef(null)

  const { data: agentList = [] } = useFetchAgentList({
    botNo,
    agentMode: 2
  })

  const refreshTableData = () => {
    tableRef.current?.refresh?.({ stay: true }) // 停留在原也买
  }

  const actionRow = useMemo(
    () => ({
      title: "操作",
      key: "action",
      dataIndex: "action",
      hidden: false,
      fixed: "right",
      width: 130,
      render: (_, record) => (
        <Button type="link" onClick={() => setDrawerData({ open: true, id: record.webcallId })}>
          查看
        </Button>
      )
    }),
    []
  )

  const formatColumns = useCallback(
    (columns) => {
      const _columns = cloneDeep(columns)
      const columnsWithKeys = _columns.map((col) => {
        const baseColumn = {
          key: col.fieldKey, // 列的唯一标识
          dataIndex: col.fieldKey,
          title: col.fieldName || col.title,
          width: col.width || 200,
          sorter: col.supportSort || false,
          align: "left",
          search: false,
          filters: false,
          render: col.render,
          ...(col.inputType === "select" && col.enums
            ? {
                enums: col.enums
              }
            : {})
        }
        if (col.inputType === "input") {
          return {
            ...baseColumn,
            ...TableFilter({
              form,
              searchParams,
              searchInput,
              refresh: refreshTableData,
              dataIndex: col.fieldKey,
              fieldType: "input"
            })
          }
        }
        if (col.inputType === "select") {
          return {
            ...baseColumn,
            ...TableFilter({
              form,
              searchParams,
              searchInput,
              refresh: refreshTableData,
              dataIndex: col.fieldKey,
              fieldType: "select",
              enums: col.enums
            })
          }
        }
        if (col.inputType === "inputNumberGroup") {
          return {
            ...baseColumn,
            ...TableFilter({
              form,
              searchParams,
              searchInput,
              refresh: refreshTableData,
              dataIndex: col.fieldKey,
              fieldType: "inputNumberGroup"
            })
          }
        }
        return {
          ...baseColumn,
          render: col.render || ((text) => text || "--")
        }
      })
      columnsWithKeys.push({
        ...actionRow,
        key: "action", // 使用简单的 key
        align: "left",
        search: false,
        filters: false
      })
      setColumnsSettingValue(columnsWithKeys)
      return columnsWithKeys
    },
    [actionRow, form, searchParams]
  )

  const onColumnsSettingChange = (setting) => {
    // 更新列设置状态
    setColumnsSettingValue([...setting])
  }

  const tableColumns = useMemo(() => {
    const columns = [
      {
        fieldKey: "callId",
        fieldName: "通话ID",
        inputType: "input"
      },
      {
        fieldKey: "agentName",
        fieldName: "Agent名称",
        inputType: "select",
        enums: agentList?.map(({ agentName, agentNo }) => ({ value: agentNo, desc: agentName }))
      },
      {
        fieldKey: "startTime",
        fieldName: "拨打时间",
        supportSort: true
      },
      {
        fieldKey: "endTime",
        fieldName: "挂机时间",
        supportSort: true
      },
      {
        fieldKey: "tag",
        fieldName: "第三方小号",
        inputType: "input"
      },
      {
        fieldKey: "number",
        fieldName: "客户手机号",
        render: (text) => maskPhoneNumber(text)
      },
      {
        fieldKey: "waitDuration",
        fieldName: "振铃时长",
        inputType: "inputNumberGroup",
        render: (text) => formatDuration(text)
      },
      {
        fieldKey: "totalDuration",
        fieldName: "通话时长",
        inputType: "inputNumberGroup",
        render: (text) => formatDuration(text)
      }
    ].map((item, index) => ({
      ...item,
      orderNo: index + 1
    }))
    return formatColumns(columns)
  }, [formatColumns, JSON.stringify(agentList)])

  const request = async (params, sorter) => {
    const searchParam = form.getFieldsValue()
    setSearchParams(searchParam)
    const { waitDuration, totalDuration, agentName, ...otherParams } = searchParam
    const requestParams = {
      ...otherParams,
      ...params,
      agentNo: agentName,
      robotCode: botNo,
      waitDurationMin: waitDuration?.min,
      waitDurationMax: waitDuration?.max,
      totalDurationMin: totalDuration?.min,
      totalDurationMax: totalDuration?.max,
      orderColumn: sorter?.field,
      orderType: sorter?.field ? (sorter?.order === "ascend" ? "asc" : "desc") : undefined
    }
    const res = await searchApi(requestParams)
    setRealTotal(res?.total)
    return res
  }

  return (
    <div className="overflow-auto h-full table-xrender-container">
      <div className="table-xrender-container-insert">
        <Form form={form}>
          <TableRender
            className="call-logs-table call-logs-render-v2"
            ref={tableRef}
            rowKey={Math.random}
            search={{
              schema: getSchema(),
              widgets: { RangeTimePicker },
              props: { botNo },
              mode: "simple",
              retainBtn: []
            }}
            scroll={{ x: 900, y: "77vh" }}
            pagination={{
              showSizeChanger: true,
              size: "default",
              showTotal: (total) => `共${total}条`
            }}
            request={request}
            columns={tableColumns}
            toolbarAction={{
              enabled: ["refresh", "columnsSetting", "density"],
              // @ts-ignore
              columnsSettingValue: columnsSettingValue?.filter(Boolean) || [],
              onColumnsSettingChange
            }}
            sortMultiple={true}
            toolbarRender={<div />}
          />
        </Form>
      </div>
      <ConsultRecordDrawer drawerData={drawerData} setDrawerData={setDrawerData} />
    </div>
  )
}

export default VoiceRecord
