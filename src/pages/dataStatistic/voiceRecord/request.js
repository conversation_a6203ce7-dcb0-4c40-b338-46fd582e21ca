import dayjs from "dayjs"
import { fetchAudioPage } from "@/api/voiceRecord/api"

export const searchApi = async ({ current: pageNum, startTime, endTime, ...restParams }) => {
  const requestParams = {
    pageNum,
    startTime: startTime && dayjs(startTime).format("YYYY-MM-DD HH:mm:ss"),
    endTime: endTime && dayjs(endTime).format("YYYY-MM-DD HH:mm:ss"),
    ...restParams
  }
  const { list = [], total = 0 } = await fetchAudioPage(requestParams)
  return {
    data: list,
    total
  }
}
