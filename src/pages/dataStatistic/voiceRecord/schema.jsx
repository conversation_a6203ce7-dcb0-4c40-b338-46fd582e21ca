import dayjs from "dayjs"

export const currentDayStartSecond = dayjs().subtract(6, "days").startOf("day") // 7天前的 00:00:00
export const currentDayEndSecond = dayjs().endOf("day") // 今天的 23:59:59

export const getSchema = () => {
  return {
    type: "object",
    labelWidth: 90,
    properties: {
      timeRange: {
        // title: "时间范围",
        span: 8,
        type: "array",
        widget: "RangeTimePicker",
        bind: ["startTime", "endTime"],
        default: [currentDayStartSecond, currentDayEndSecond],
        props: {
          showTime: true
        }
      }
    }
  }
}
