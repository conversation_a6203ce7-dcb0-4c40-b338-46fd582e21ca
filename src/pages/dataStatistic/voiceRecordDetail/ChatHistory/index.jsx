import ClientAvatar from "../img/clientAvatar.svg"
import DeskAvatar from "../img/deskAvatar.svg?react"
import { MSG_ID_PREFIX } from "./utils/utils"
import classnames from "classnames"
import { memo, useCallback, useRef } from "react"
import { Tag, Space } from "antd"
import { formatDate } from "./utils/ui"
import "./index.less"

const ChatHistory = ({ list }) => {
  const bubbleRefs = useRef({})
  const containerRef = useRef(null)

  const renderAvatar = useCallback((item) => {
    return (
      <div className={"avatar"}>
        {item.side === 0 ? <DeskAvatar style={{ color: "#B692F6" }} /> : <img src={ClientAvatar} />}
      </div>
    )
  }, [])

  const renderBubble = useCallback(
    (item) => (
      <div className={classnames("w90", item.side === 1 && "alignR")}>
        <div className={"time"}>{formatDate(item.beginTime)}</div>
        <div className={classnames("bubble")} dangerouslySetInnerHTML={{ __html: item.content }} />
        <div className={"action"} style={item.side === 1 ? { justifyContent: "flex-end" } : {}}>
          <Space size={4}>
            {!!item.eventInfo && <Tag>事件：{item.eventInfo}</Tag>}
            {!!item.intentionName && <Tag>意图：{item.intentionName}</Tag>}
            {!!item.faqName && <Tag>FAQ：{item.faqName}</Tag>}
          </Space>
        </div>
      </div>
    ),
    []
  )

  const renderItem = useCallback(
    (item, index) => {
      if (item.side === 0) {
        return (
          <div
            id={`${MSG_ID_PREFIX}_${index}`}
            key={index}
            ref={(ref) => ref && (bubbleRefs.current[index] = ref)}
          >
            <div className={"item"}>
              {renderAvatar(item)}
              {renderBubble(item)}
            </div>
          </div>
        )
      } else if (item.side === 1) {
        return (
          <div
            id={`${MSG_ID_PREFIX}_${index}`}
            key={index}
            ref={(ref) => ref && (bubbleRefs.current[index] = ref)}
          >
            <div className={classnames("item", "reverseItem")}>
              {renderBubble(item)}
              {renderAvatar(item)}
            </div>
          </div>
        )
      }
      return null
    },
    [renderBubble, renderAvatar]
  )

  return (
    <div className={"chatHistoryContainer"} ref={containerRef}>
      {list?.map((item, index) => {
        return (
          <div id={`msgRow-${index}`} data-rowid={index} className="msgRow" key={index}>
            {renderItem(item, index)}
          </div>
        )
      })}
    </div>
  )
}

export default memo(ChatHistory)
