.chatHistoryContainer {
  overflow-x: hidden;
  position: relative;
  .tips {
    padding-bottom: 16px;
    font-size: 14px;
    text-align: center;
    color: #666;
  }

  .item {
    display: flex;
    padding-bottom: 15px;
    justify-content: flex-start;
  }

  .icon {
    color: #d0d2d5;
    font-size: 35px;
  }

  .custorm {
    color: #82bcff;
  }

  .servant {
    color: #f6a3c8;
  }

  .voiceicon {
    font-size: 20px;
    line-height: 0;
    text-align: center;
    cursor: pointer;
  }

  .bgcolor {
    background: #e3e8f1;
  }

  .name {
    font-size: 12px;
    color: #333;
    margin-left: 20px;
    padding-top: 7px;
  }

  .textAlignR {
    margin-right: 20px;
    text-align: right;
  }

  .action {
    width: 100%;
    display: flex;
    margin-top: 4px;
    .ant-space {
      margin-inline-start: 8px;
      .ant-tag {
        margin: 0;
        color: #525866;
        font-size: 12px;
        border-color: #e1e4ea;
        background: #f5f7fa;
      }
    }
  }

  .time {
    font-size: 10px;
    line-height: 10px;
    color: #4e5969;
    margin-left: 8px;
    margin-bottom: 4px;
    span {
      padding-right: 7px;
      margin-right: 7px;
      border-right: 1px solid #c9cdd4;
    }
  }

  .alignL {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .w90 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: calc(100% - 130px);
    word-break: break-all;
  }

  .alignR {
    align-items: flex-end;

    .action {
      .ant-space {
        margin-inline-start: 0;
        margin-inline-end: 8px;
        .ant-tag {
          margin: 0;
        }
      }
    }

    .asrText {
      margin-right: 8px;
    }

    :global {
      .ant-spin-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }
    }
  }

  .imgStyle {
    width: 150px;
    height: 150px;
    overflow: hidden;
  }

  .checks {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
  }

  .font16 {
    font-size: 15px;
    color: #f06767;
    margin-left: 1px;
    cursor: pointer;
    margin-top: 5px;
  }

  .bubble {
    position: relative;
    font-size: 14px;
    color: #1d212f;
    background: rgba(220, 213, 255, 0.5);
    word-wrap: break-word;
    border-radius: 0 16px 16px 16px;
    padding: 12px 16px;
    margin: 0 8px;
  }

  .playingright {
    transform: rotateY(-180deg);
  }

  .highLight {
    border-color: red;
    border: 1px solid red;
  }

  .reverseItem {
    justify-content: flex-end;

    .time {
      text-align: right;
      margin-right: 8px;
    }

    .imgStyle {
      margin-left: 10px;
    }

    .bubble {
      margin-right: 8px;
      background: #f0f0f0;
      border-radius: 16px 0 16px 16px;
      padding: 12px 16px;
    }
  }

  .simpleContainer {
    :global {
      .msgRow {
        margin-bottom: 20px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .simpleRow {
      display: flex;
    }
    .highLight {
      border-color: red;
      border: 1px solid red;
    }
    .imgStyle {
      max-height: 100%;
    }
    .bubble {
      background: none;
      color: #1d212f;
      padding: 0;
      font-size: 14px;
      margin-left: 0;
      border-radius: 4px;
    }
    .beginTime {
      font-size: 10px;
      line-height: 10px;
      color: #4e5969;
      margin-bottom: 4px;
      span {
        padding-left: 7px;
        margin-left: 7px;
        border-left: 1px solid #c9cdd4;
      }
    }
    .simpleRowContent {
      margin-left: 8px;
    }
    .tips {
      padding-bottom: 0;
    }
    .bubble.currentMsg {
      padding: 2px;
      border-radius: 4px;
    }
  }

  .bubble.currentMsg {
    background-color: rgba(112, 82, 240, 0.9);
    color: #fff;
  }
  .bubble.hit {
    border: 1px solid rgba(255, 45, 32, 1);
  }

  .asrText {
    border-radius: 8px;
    padding: 12px 16px 12px 16px;
    background-color: #f0f0f0;
    color: #1d212f;
    font-size: 14px;
    margin-top: 4px;
    margin-left: 6px;
  }
}
