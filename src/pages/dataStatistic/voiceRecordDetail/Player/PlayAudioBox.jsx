import { ConfigProvider, Slider } from "antd"
import { forwardRef, memo, useCallback } from "react"

const theme = {
  token: {
    colorBgSpotlight: "#fff",
    colorTextLightSolid: "#000B15"
  },
  components: {
    Slider: {
      handleColor: "#7052F0",
      handleActiveColor: "#7052F0",
      handleLineWidth: 1,
      handleLineWidthHover: 1,
      railBg: "#EFF1F4",
      railHoverBg: "#EFF1F4",
      railSize: 8,
      trackBg: "#7052F0",
      trackHoverBg: "#7052F0",
      dotBorderColor: "#7052F0",
      dotActiveBorderColor: "#7052F0",
      dotSize: 12,
      controlSize: 12,
      handleSize: 12,
      handleSizeHover: 12
    }
  }
}

function formatterTime(milliseconds) {
  // 将毫秒转换为秒
  const totalSeconds = Math.floor(milliseconds / 1000)
  // 计算分钟数和剩余的秒数
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  // 格式化分钟数和秒数，确保它们的长度为2位
  const formattedMinutes = String(minutes).padStart(2, "0")
  const formattedSeconds = String(seconds).padStart(2, "0")
  // 拼接成 mm:ss 格式的字符串
  return `${formattedMinutes}:${formattedSeconds}`
}

const PlayAudioBox = forwardRef(
  (
    {
      currentTime,
      duration,
      url,
      autoPlay,
      onError,
      onLoadedMetadata,
      onChange,
      setPlaying,
      handleTimeUpdate,
      onEnded
    },
    ref
  ) => {
    const onPlay = useCallback(() => setPlaying?.(true), [setPlaying])

    const onPause = useCallback(() => setPlaying?.(false), [setPlaying])

    return (
      <div className={"progress"}>
        <ConfigProvider theme={theme}>
          <Slider
            value={currentTime}
            onChange={onChange}
            tooltip={{
              formatter: (value) => (
                <span key={value}>{value ? formatterTime(value * 1000) : "00:00"}</span>
              )
            }}
            step={0.01}
            max={duration}
            min={0}
          />
        </ConfigProvider>
        {!!url && (
          <audio
            onLoadedMetadata={onLoadedMetadata}
            onPlay={onPlay}
            onPause={onPause}
            onEnded={onEnded}
            onError={onError}
            style={{ position: "absolute", opacity: 0, left: -9999 }}
            onTimeUpdate={handleTimeUpdate}
            ref={ref}
            src={url}
            controls
            autoPlay={autoPlay}
          />
        )}
      </div>
    )
  }
)

export default memo(PlayAudioBox)
