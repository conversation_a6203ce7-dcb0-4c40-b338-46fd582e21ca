import { useFetchAudioInfo } from "@/api/voiceRecord"
import { useMemo } from "react"
import BaseInfo from "./BaseInfo"
import ChatHistory from "./ChatHistory"
import Player from "./Player"
import "./index.less"

const ConsultRecord = ({ id, isContainer, goBack }) => {
  const { data } = useFetchAudioInfo({ webcallId: id })

  const audioUrl = useMemo(() => {
    return data?.ossLink || ""
  }, [data?.ossLink])

  return (
    <div className={"consultContainer"} style={{ height: "100vh", padding: 0 }}>
      <Player url={audioUrl} callId={data?.callId} goBack={goBack} isContainer={isContainer} />
      <div className={"consultRrecord"} style={{ margin: 0 }}>
        <div className={"consultMessage"}>
          <div className={"consultTitle"}>
            <div style={{ fontWeight: 600, display: "flex", alignItems: "center" }}>会话详情</div>
          </div>
          <div className={"scrollWrapper"}>
            <ChatHistory list={data?.records || []} />
          </div>
        </div>
        <div className={"consultInfo"}>
          <div className={"consultTitle"}>基本信息</div>
          <div className={"scrollWrapper"}>
            <BaseInfo baseInfoLists={data?.baseInfo || []} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConsultRecord
