.consultContainer {
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  .topLine {
    padding: 16px;
    padding-bottom: 6px;
  }
}
.consultRrecord {
  flex: 1;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;
  overflow-y: auto;
  display: flex;
  .consultMessage,
  .consultInfo {
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
  .consultTitle {
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eff1f4;
    padding: 0 16px;
    justify-content: space-between;
    font-weight: 600;
  }
  .consultMessage {
    width: calc((100% - 8px) * 0.65);
    margin-right: 8px;
  }
  .consultInfo {
    width: calc((100% - 8px) * 0.35);
    .consultInfoTabs {
      .ant-tabs-tab {
        padding: 11px 0;
        font-weight: 500;
        &:not(.ant-tabs-tab-active) {
          color: #4e5969;
        }
      }
    }
  }
  .scrollWrapper {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
  }
}

.consultDrawer {
  .ant-drawer-content-wrapper {
    width: 100% !important;
    box-shadow: none !important;
    .ant-drawer-header {
      display: none;
    }
    .ant-drawer-content {
      .ant-drawer-body {
        padding: 0;
        background-color: #f5f5f5;
      }
    }
  }
}
