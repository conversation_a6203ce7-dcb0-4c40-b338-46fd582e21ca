import { useLocation } from "react-router-dom"
import "./index.scss"
import queryString from "query-string"
import KnowledgeBaseWrapper from "../addBot/components/KnowledgeBaseWrapper"

function KnowledgeManage() {
  const location = useLocation()
  const { search } = location
  const queryParams = queryString.parse(search)
  const { knowledgeData, catalogNo } = queryParams

  return (
    <div>
      <KnowledgeBaseWrapper selectedKnowledgeBase={knowledgeData} catalogNo={catalogNo} />
    </div>
  )
}

export default KnowledgeManage
