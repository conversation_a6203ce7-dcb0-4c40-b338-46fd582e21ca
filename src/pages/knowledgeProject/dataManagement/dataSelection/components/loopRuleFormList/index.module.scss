$primary-color: #7F56D9;

.cardBox {
  :global {
    .ant-card-body {
      padding: 0;
    }
  }

  .cardList {
    padding: 0 20px 20px;
    margin-bottom: 24px;
    border-bottom: 1px dashed #f0f0f0;
    position: relative;

    &:first-child {
      padding-top: 20px;
    }

    .addButton {
      position: absolute;
      left: -15px;
      bottom: -15px;
    }
  }

  &.childCardBox {
    &:not(:first-child) {
      margin-top: 20px;
    }

    :global {
      .ant-card-body {
        padding: 0;
      }
    }

    .cardList {
      padding-bottom: 0;
    }
  }

  .optionsBox {
    display: flex;
    align-items: center;

    .optionBtn {
      border-color: transparent;
      text-align: center;
      &:not(:disabled) {
        color: $primary-color;
        background: rgba(93, 95, 239, 0.4);
      }
    }

    :global {
      .ant-btn {
        padding: 4px 8px;

        &:not(:first-child) {
          visibility: hidden;
        }
      }
    }

    &:hover {
      :global {
        .ant-btn {
          &:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }

          &:not(:first-child) {
            visibility: visible;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }

          &:not(:first-child):not(:last-child) {
            border-radius: 0;
          }
        }
      }
    }
  }
}
