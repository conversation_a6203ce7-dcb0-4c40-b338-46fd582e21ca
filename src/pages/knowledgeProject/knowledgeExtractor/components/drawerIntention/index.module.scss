.Intention {

    height: 100%;
}

.messagebox {
    // padding: 0px 5px;
    height: 100%;
  
}
.mainbox{
    :global{
        .ant-card-body{
            height: 90%;
            padding: 10px;
            padding-top: 5px;
            // background-color: red;
        }
    }
  
}
.mainleftbox{
    :global{
        .ant-card-body{
            padding: 10px;
            // background-color: red;
        }
    }
  
}

.flex {
    display: flex;
}

.history {
    // background-color:rgba(0, 0, 0, 0.04);
    // border-radius: 3px;
    // padding: 5px;
    // width: 80%;

    display: inline-block;
    box-sizing: border-box;
    background: #fff3eb;
    border-radius: 6px;
    padding: 8px 9px 9px 10px;
}

.AI {
    text-align: right;
}

.flexRight {
    display: flex;
    flex-direction: row-reverse;
}

.usericon {
    font-size: 16px;
    align-items: flex-start;
    // margin-right: 10px;
}

.user,
.AI {
    margin-bottom: 10px;
}

.user {

    // .name{
    //     color: #fff3eb;
    //     }
    .history {
        background-color: #e8e8e9;
    }
}

.intentionbox {
    padding: 10px;
    // border-top-right-radius: 5px;
    // border-top-left-radius: 5px;
    background-color: rgb(93, 95, 239, 0.1);

}

.summary {
    // background-color:#d9d9d9;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    text-align: center;
    padding: 5px;
    border: 1px solid rgb(93, 95, 239, 0.1);
    border-top: none;
    margin-bottom: 10px;
    font-size: 12px;
}

.summarytop {
    // background-color:#d9d9d9;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    text-align: center;
    padding: 5px;
    border: 1px solid rgb(93, 95, 239, 0.1);
    border-bottom: none;
    font-size: 12px;
    // margin-bottom: 10px;
}

.name {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999;
    line-height: 14px;
    margin-bottom: 3px;
}

.recordbox {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    // justify-content: center;
}

.empty {
    width: 100%;
    text-align: center;
    font-size: 16px;
    margin-top: 10px;
    height: 100%;
}

.timeStr {
    margin-top: 3px;
    font-size: 12px;
    color: #653918;
}

.cardbox {
    // height: calc;
    border: 1px solid #d9d9d9;
    padding: 10px;
    // border-radius: 5px;
    overflow: auto;
    max-height: calc(100% - 140px);
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

}

.cardboxHead {
    box-sizing: border-box;
    // background-color: red;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border: 1px solid #d9d9d9;
    border-bottom: none;
    background-color: #f7f8fa;
    padding: 10px;
    // display: flex;
    // justify-content: center;
}
.infobox{
    // flex: 1;
    flex-wrap: nowrap;
    .title{
        color: rgba(0, 0, 0, 0.88);
        // font-weight: 600;
        font-size: 14px;
        line-height: 1.5;
    }
    .value{
        color: rgba(0, 0, 0, 0.88);
        // font-weight: 600;
        font-size: 14px;
        line-height: 1.5;
    }
display: flex;
}
.detailbox {
    margin-bottom: 10px;
}
.audiobox{
    height: 30px;
    // width: 100%;
    margin-top: 10px;
}
.audiobox::-webkit-media-controls-panel {
    width: 100%;
    // background-color: #fafafa;
    // background-color: rgba(93, 95, 239, 0.1);
    // color: #fff;
    // border-radius: 30px;
    /* Add more styles as needed */
  }
  .textAreaBox{
    textarea[disabled]{
        color: #cecece;
    }
    
}