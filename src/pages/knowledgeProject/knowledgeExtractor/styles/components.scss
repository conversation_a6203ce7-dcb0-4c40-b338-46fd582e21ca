.extractor-header {
  border-radius: 20px;
  background: #f7f8fa;
  padding: 24px 20px;
  margin-bottom: 20px;
  box-sizing: border-box;
  .title {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    .anticon-left {
      margin-right: 10px;
    }
  }
  .extractor-title-descriptions {
    font-size: 16px;
    margin-left: 20px;
    color: #666;
    font-weight: normal;
  }
}

.extractor-container {
  padding: 0 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
  > div {
    padding: 24px 20px;
    box-sizing: border-box;
    border-radius: 20px;
    background: #f7f8fa;
  }
}

.extractor-status-bar {
  .ant-badge.ant-badge-status {
    margin-right: 5px;
  }
}

.split-title {
  height: 60px;
  width: 100%;
  position: relative;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  > span {
    background: white;
    padding: 0 10px;
    z-index: 2;
  }
}
.split-title::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #eee;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
