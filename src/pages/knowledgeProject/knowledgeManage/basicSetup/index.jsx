import { Tabs } from "antd"
import PageContainer from "@/components/PageContainer"
import BusinessScenario from "./components/businessScenario"
import SceneHierarchy from "./components/sceneHierarchy"
import styles from "./index.module.scss"

const { TabPane } = Tabs

const BasicSetup = () => {
  return (
    <PageContainer showHeader={false}>
      <Tabs className={styles["basic-setup-tabs"]} defaultActiveKey="1" destroyInactiveTabPane>
        <TabPane tab="业务场景" key="1">
          <BusinessScenario />
        </TabPane>
        <TabPane tab="场景层级" key="2">
          <SceneHierarchy />
        </TabPane>
      </Tabs>
    </PageContainer>
  )
}

export default BasicSetup
