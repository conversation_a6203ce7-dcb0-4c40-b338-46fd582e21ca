/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-19 10:00:00
 * @Descripttion: 声音市集组件
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-12-19 10:00:00
 * @FilePath: /za-aigc-platform-admin-static/src/pages/market/components/itemModule/TimbreModule.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
import React, { useState, useEffect } from "react"
import { Col, Row, Button, Tooltip, Spin, message, Popconfirm } from "antd"
import { StarOutlined } from "@ant-design/icons"
import QueueAnim from "rc-queue-anim"
import CopyToClipboard from "react-copy-to-clipboard"
import { useInView } from "react-intersection-observer"
import { useInfiniteTimbreMarketListApi } from "@/api/timbre"

import { updateTimbreSubscriptionStatus } from "@/api/timbre/api"

import { useMarket } from "../../"
import { getThemeConfig, marketCode } from "@/constants/market"
import { cancelBubble } from "@/utils"
import CustomEmpty from "@/antd-styles/components/CustomEmpty"
import styles from "./index.module.scss"
import queryString from "query-string"
import { useLocation } from "react-router-dom"
import { SkeletonModule } from "./skeleton"
import TimbreDrawer from "@/pages/voice/components/TimbreDrawer"

// 声音图标
import timbreIcon from "@/assets/img/voiceTitle.png"
import defaultAvatar from "@/assets/img/empty.png"
import defaultTimbreGirl from "@/assets/img/defaultTimbreGirl.png"
import defaultTimbreBoy from "@/assets/img/defaultTimbreBoy.png"

import "./TimbreModule.scss"

export const TimbreModule = () => {
  const { marketSearch } = useMarket()
  const location = useLocation()
  const { search } = location
  const queryParams = queryString.parse(search) ?? {}
  const { botNo } = queryParams
  const [pagination, setPagination] = useState({ current: 1, pageSize: 12 })
  const { color, hover, title, border } = getThemeConfig(marketCode.TIMBRE)

  // 详情抽屉状态
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [currentTimbreId, setCurrentTimbreId] = useState(null)

  useEffect(() => {
    setPagination({ ...pagination, current: 1 })
  }, [marketSearch])

  // 使用无限滚动获取声音列表
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: listLoading,
    refetch // 新增
  } = useInfiniteTimbreMarketListApi({
    pageSize: pagination.pageSize,
    botNo,
    ...(marketSearch === "" ? {} : { timbreName: marketSearch })
  })

  // 当前是否滚动到底部
  const { ref, inView } = useInView({
    threshold: 0
  })

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage()
    }
  }, [inView, fetchNextPage, hasNextPage])

  // 合并所有页面的数据
  const list = data?.pages?.flatMap((page) => page.list) || []

  // 处理订阅
  const handleSubscribe = async (e, timbreId) => {
    cancelBubble(e)
    if (!botNo) {
      message.warning("当前未获取到机器人，请重试")
      return
    }
    try {
      const res = await updateTimbreSubscriptionStatus({ botNo, timbreId, subscriptionStatus: "Y" })
      if (res?.status === 200 || res?.code === 200 || res?.code === "200") {
        message.success("订阅成功")
        refetch && refetch() // 强制刷新市集列表
      } else {
        let msg = "操作失败"
        if (typeof res?.message === "string") {
          msg = res.message
        } else if (typeof res?.message === "object" && res?.message?.message) {
          msg = res.message.message
        } else if (typeof res?.message === "object") {
          msg = JSON.stringify(res.message)
        }
        message.error(msg)
      }
    } catch (err) {
      let msg = "操作失败"
      if (typeof err?.message === "string") {
        msg = err.message
      } else if (typeof err?.message === "object" && err?.message?.message) {
        msg = err.message.message
      } else if (typeof err?.message === "object") {
        msg = JSON.stringify(err.message)
      }
      message.error(msg)
    }
  }

  // 处理取消订阅
  const handleCancelSubscribe = async (e, timbreId) => {
    cancelBubble(e)
    if (!botNo) {
      message.warning("当前未获取到机器人，请重试")
      return
    }
    try {
      const res = await updateTimbreSubscriptionStatus({ botNo, timbreId, subscriptionStatus: "N" })
      if (res?.status === 200 || res?.code === 200 || res?.code === "200") {
        message.success("取消订阅成功")
        refetch && refetch() // 强制刷新市集列表
      } else {
        let msg = "操作失败"
        if (typeof res?.message === "string") {
          msg = res.message
        } else if (typeof res?.message === "object" && res?.message?.message) {
          msg = res.message.message
        } else if (typeof res?.message === "object") {
          msg = JSON.stringify(res.message)
        }
        message.error(msg)
      }
    } catch (err) {
      let msg = "操作失败"
      if (typeof err?.message === "string") {
        msg = err.message
      } else if (typeof err?.message === "object" && err?.message?.message) {
        msg = err.message.message
      } else if (typeof err?.message === "object") {
        msg = JSON.stringify(err.message)
      }
      message.error(msg)
    }
  }

  // 卡片点击事件
  const onCardClick = (item) => {
    setCurrentTimbreId(item.timbreId)
    setDrawerVisible(true)
  }

  return (
    <div className={styles["market-item-module"]} key={marketCode.TIMBRE}>
      <Spin spinning={listLoading}>
        {list?.length && !listLoading ? (
          <div className={styles["list-container"]}>
            <Row gutter={15}>
              {list?.map((item, i) => {
                const {
                  timbreId,
                  timbreCode,
                  timbreName,
                  timbreModel,
                  gender,
                  avatarUrl,
                  source,
                  subscriptionStatus,
                  enabled,
                  description
                } = item || {}

                const hasSubscribed = subscriptionStatus === "Y"
                const isEnabled = enabled === "Y"

                return (
                  <Col xs={24} sm={12} md={12} lg={8} xl={8} xxl={6} key={timbreId}>
                    <QueueAnim delay={50 * i} type="top" className="queue-simple">
                      <div
                        className={`${styles["card-item"]} ${styles["card-item-timbre"]} cursor-pointer`}
                        // style={{
                        //   marginBottom: "20px",
                        //   borderColor: border,
                        //   opacity: isEnabled ? 1 : 0.6
                        // }}
                        style={{ marginBottom: "20px" }}
                        onClick={() => onCardClick(item)}
                      >
                        <div className="flex justify-between items-start w-[100%]">
                          <div className="w-[100%]">
                            <div className={styles["title-wrapper"]}>
                              <div
                                className={`${styles["card-icon"]} mr-[12px] flex items-center justify-center w-[48px] h-[48px] rounded-[8px] bg-gray-100`}
                              >
                                <img
                                  src={
                                    avatarUrl
                                      ? avatarUrl
                                      : gender === "female"
                                        ? defaultTimbreGirl
                                        : defaultTimbreBoy
                                  }
                                  className={styles["text-base-img"]}
                                  style={{ width: "32px", height: "32px", borderRadius: "4px" }}
                                />
                              </div>
                              <div className="flex flex-1 flex-col items-start overflow-hidden">
                                <Tooltip title={timbreName}>
                                  <span className={styles["text-base"]}>
                                    {timbreName ?? "暂无"}
                                  </span>
                                </Tooltip>
                                {/* <CopyToClipboard
                                  text={timbreCode}
                                  onCopy={() => message.success("复制成功")}
                                >
                                  <Tooltip title="点击复制编号">
                                    <span
                                      className="text-[#626263] cursor-pointer leading-[18px] text-[12px] mt-[8px]"
                                      onClick={(e) => cancelBubble(e)}
                                    >
                                      {timbreCode}
                                    </span>
                                  </Tooltip>
                                </CopyToClipboard> */}
                                <span
                                  className="text-[#626263] cursor-pointer leading-[18px] text-[12px] mt-[8px]"
                                  onClick={(e) => cancelBubble(e)}
                                >
                                  {timbreModel}
                                </span>
                              </div>
                            </div>
                            <div className={styles["skill-item-text"]}>
                              {/* <div className={styles["time-tag"]}>
                                <span className={styles["title"]}>模型</span>
                                <span className={styles["time"]}>{timbreModel}</span>
                              </div> */}
                              <div className={styles["time-tag"]}>
                                <span className={styles["title"]}>来源</span>
                                <span className={styles["time"]}>
                                  {source === "CREATED" ? "自建" : source}
                                </span>
                              </div>
                              <div className={styles["time-tag"]}>
                                <span className={styles["time"]}>
                                  {gender === "female" ? "女声" : "男声"}
                                </span>
                              </div>
                            </div>
                            <Tooltip title={description}>
                              <div className={styles["desc"]}>
                                {description || "这个人很懒，暂未填写描述～"}
                              </div>
                            </Tooltip>
                          </div>
                        </div>
                        <div className={styles["remove-btn"]}>
                          <div className={styles["subscribe-count"]}>
                            <StarOutlined className="text-[#D0D5DD] text-[15px] mr-[5px] mt-[2px]" />
                            <span className={styles["count-text"]}>0</span>
                          </div>
                          {!hasSubscribed ? (
                            <Button
                              type="link"
                              className="p-0"
                              onClick={(e) => handleSubscribe(e, timbreId)}
                            >
                              <i className="iconfont icon-dingyue inline-block !mt-[1px] mr-[3px] !text-[#D0D5DD] group-hover:!text-[#F6B51E]"></i>
                              订阅
                            </Button>
                          ) : (
                            <Popconfirm
                              title=""
                              description={`【取消订阅】将无法使用对应声音。`}
                              okText="确认"
                              onConfirm={(e) => handleCancelSubscribe(e, timbreId)}
                              cancelText="取消"
                              onPopupClick={(e) => cancelBubble(e)}
                            >
                              <Button type="link" className="p-0" onClick={(e) => cancelBubble(e)}>
                                <i className="iconfont icon-dingyue align-middle -mt-[1px] mr-[3px] text-[#F6B51E]"></i>
                                取消订阅
                              </Button>
                            </Popconfirm>
                          )}
                        </div>
                      </div>
                    </QueueAnim>
                  </Col>
                )
              })}
            </Row>
            {isFetchingNextPage && <div>Loading more...</div>}
            <div ref={ref} style={{ height: 20 }} /> {/* 用于检测滚动到底部 */}
          </div>
        ) : null}
        {!listLoading && !list?.length ? (
          <div className="my-[50px]">
            <CustomEmpty />
          </div>
        ) : null}
        {listLoading ? <SkeletonModule /> : null}
      </Spin>
      {/* TimbreDrawer 详情抽屉 */}
      <TimbreDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        timbreId={currentTimbreId}
        botNo={botNo}
        mode="marketView"
      />
    </div>
  )
}
