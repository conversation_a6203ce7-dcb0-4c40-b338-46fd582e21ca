.market-item-module {
  .divider {
    font-size: 18px;
    font-weight: 900;
    margin: 40px 0;
    letter-spacing: 10px;
  }
}

.list-container {
  :global {
    .ant-col {
      margin-bottom: 20px;
    }
    .ant-row {
      margin: 0 -10px;
    }
  }
}

.card-item {
  height: 212px;
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  flex: 1 0 0;
  border-radius: var(--Gap-s, 12px);
  border: 1px solid var(---, #e4e7ec);
  position: relative;
  transition: all 0.2s;

  &.card-item-AGENT {
    background: linear-gradient(117.35deg, #ffffff 69.3%, #eef4ff 98.8%);
    &:before {
      background-image: linear-gradient(257.29deg, #2c6cf5 1.45%, #29dfff 100%);
    }
    .card-icon {
      background: #f4f7ff;
    }
  }
  &.card-item-SKILL {
    background: linear-gradient(117.35deg, #ffffff 69.3%, #f6f4ff 98.8%);
    &:before {
      background-image: linear-gradient(249.29deg, #3234ff -0.07%, #ff70de 97.52%);
    }
    .card-icon {
      background: rgba(239, 235, 255, 0.5);
    }
  }
  &.card-item-PLUG_IN {
    background: linear-gradient(117.35deg, #ffffff 69.3%, #f3feff 98.8%);
    &:before {
      background-image: linear-gradient(249.29deg, #00d3ae -0.07%, #55fd5a 97.52%);
    }
    .card-icon {
      background: rgba(224, 250, 236, 0.5);
    }
  }
  &.card-item-ROBOT {
    background: linear-gradient(117.35deg, #ffffff 69.3%, #effafc 98.8%);
    &:before {
      background-image: linear-gradient(249.29deg, #2fbae6 -0.07%, #79f2b7 97.52%);
    }
    .card-icon {
      background: rgba(224, 246, 250, 0.5);
    }
  }
  &.card-item-timbre {
    background: linear-gradient(117.35deg, #ffffff 69.3%, #fffaf1 98.8%);
    &:before {
      background-image: linear-gradient(249.29deg, #f59a2c -0.07%, #ff29ff 97.52%);
    }
    .card-icon {
      background: rgba(237, 247, 237, 0.5);
    }
  }
  &:before {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    z-index: -1;
    border-radius: var(--Gap-s, 13px);
    content: "";
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    border: 1px solid transparent;
    box-shadow: 0px 4px 32px 0px rgba(51, 51, 51, 0.12);
    &:before {
      opacity: 1;
    }
  }

  .title-wrapper {
    display: flex;
    width: 100%;
    overflow: hidden;
  }
  .text-base-img {
    width: 48px;
    display: inline-block;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    vertical-align: middle;
  }

  .text-base {
    color: #181b25;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    vertical-align: middle;
    max-width: 80%;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .desc {
    margin-top: 8px;
    font-size: 12px;
    line-height: 18px;
    font-style: normal;
    color: #344054;
    font-weight: 400;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  .skill-item-text {
    max-width: 100%;
    display: flex;
    align-items: center;
    gap: var(--Gap-xs, 12px);
    align-self: stretch;
    margin-top: 12px;
  }

  .time-tag {
    display: flex;
    padding: 4px var(--Gap-xs, 8px);
    align-items: center;
    gap: 4px;
    border-radius: 100px;
    background: var(---, #f5f7fa);

    .title {
      font-size: 12px !important;
      font-style: normal;
      font-weight: 400;
      color: #475467 !important;
    }
    .time {
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      color: #181b25 !important;
    }
  }

  .remove-btn {
    width: 100%;
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid #e4e7ec;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 15%;
    padding-right: 15%;

    .subscribe-count {
      font-size: 14px;
      color: #181b25 !important;

      .count-text {
        margin-left: 4px;
      }
    }

    :global {
      .ant-btn-link {
        padding: 0;
        color: #181b25;
        font-size: 12px;
        &:hover {
          color: #7f56d9;
        }
        .iconfont {
          margin-right: 3px;
          margin-top: -1px;
          vertical-align: middle;
          &.icon-dingyue {
            color: #f6b51e;
          }
        }
      }
    }
  }
}

.empty {
  :global {
    .ant-empty-image {
      height: auto;
    }
  }
  img {
    width: 180px;
    margin: 20px 0;
  }
}
