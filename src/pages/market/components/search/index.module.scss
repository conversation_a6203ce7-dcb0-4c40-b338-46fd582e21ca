.market-search {
  position: relative;
  margin: 0 auto 60px auto;
  width: 600px;
}
.market-search::before {
  content: "";
  position: absolute;
  left: -3px;
  top: -3px;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  border-radius: 15px;
  background-image: linear-gradient(45deg, #13be13, #01efef, #0095ff, #8b00ff);
  background-size: 400%;
  animation: borderAnimate 3s linear infinite;
}
@keyframes borderAnimate {
  0%,
  100% {
    background-position: 0%, 50%;
  }
  50% {
    background-position: 100%, 50%;
  }
}

.market-search-container {
  box-sizing: border-box;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: white;
  z-index: 2;
  position: relative;
  border-radius: 15px;
  :global {
    .ant-select-selection-item {
      width: 50px;
      text-align: right;
    }
    .ant-select,
    .ant-select-selector,
    .ant-input-affix-wrapper,
    input {
      border: 0 !important;
      box-shadow: none;
    }
    .ant-select-selector:focus,
    .ant-input-affix-wrapper:focus,
    input:focus {
      border: 0 !important;
      box-shadow: none;
    }
  }
}
