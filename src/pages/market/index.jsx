/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-04-16 18:56:57
 * @Descripttion:
 * @LastEditors:  <EMAIL>
 * @LastEditTime: 2024-04-24 18:14:24
 * @FilePath: /za-aigc-platform-admin-static/src/pages/market/index.jsx
 * Copyright (c) 2024 by ZA-智能中台, All Rights Reserved.
 */
import styles from "./index.module.scss"
import { MarketSearch } from "./components/search"
import { ItemModule } from "./components/itemModule"
import { TimbreModule } from "./components/itemModule/TimbreModule"
import { useState } from "react"
import { marketCode, marketOptions, subtitleOptions } from "../../constants/market"
import { createContext, useContext } from "react"
import TextAnimation from "@/components/TextAnimation"
import { TableObjectPro } from "@/components/TableObjectPro"
import { SearchOutlined, PlusOutlined } from "@ant-design/icons"
import { Input, Select, Menu, But<PERSON> } from "antd"
import banner from "@/assets/img/banner.png"
import banner1 from "@/assets/img/banner1.png"
import banner2 from "@/assets/img/banner2.png"
import banner3 from "@/assets/img/banner3.png"
import banner4 from "@/assets/img/banner4.png"

export const MarketContext = createContext({
  marketType: marketOptions?.[1]?.value,
  setMarketType: (e) => {},
  marketSearch: "",
  setMarketSearch: (e) => {}
})
export const MarketProvider = (props) => {
  const [marketType, setMarketType] = useState(marketOptions?.[1]?.value)
  const [marketSearch, setMarketSearch] = useState("")
  const values = { marketType, setMarketType, marketSearch, setMarketSearch }
  return <MarketContext.Provider value={values}>{props.children}</MarketContext.Provider>
}
export const useMarket = () => useContext(MarketContext)

const SearchBox = () => {
  const { setMarketType, setMarketSearch } = useMarket()
  const [value, setValue] = useState("")
  const onChangeOptions = (v) => setMarketType(v)
  const handlerSearch = () => setMarketSearch(value)

  const onChange = (e) => {
    const val = e?.target?.value || ""
    setValue(val)
    if (val === "") {
      setMarketSearch("")
    }
  }

  return (
    <div className={styles["market-header-content"]}>
      <div className={styles["banner-container"]}>
        <MarketBanner />
      </div>
      <div className={styles["search-container"]}>
        <div className={styles["market-search"]}>
          <Input
            className={styles["style-input"]}
            placeholder="搜索声音名称"
            value={value}
            onChange={onChange}
            onPressEnter={handlerSearch}
            allowClear
            suffix={
              <SearchOutlined
                style={{ color: "#bfbfbf", cursor: "pointer" }}
                onClick={handlerSearch}
              />
            }
          />
        </div>
      </div>
    </div>
  )
}

const MarketBanner = () => {
  const { marketType, setMarketType } = useMarket()
  const backgroundOptions = [
    { value: "AGENT", img: banner4 },
    { value: "SKILL", img: banner3 },
    { value: "PLUG_IN", img: banner2 },
    { value: "ROBOT", img: banner1 },
    { value: "TIMBRE", img: banner3 }
  ]
  const title = marketOptions.find((item) => item.value === marketType).label
  const subtitle = subtitleOptions.find((item) => item.value === marketType).label
  const background = backgroundOptions.find((item) => item.value === marketType).img
  return (
    <div
      className={styles["market-banner"]}
      style={{
        backgroundImage: `url(${background})`
      }}
    >
      <div
        className={styles["banner-logo"]}
        style={{
          backgroundImage: `url(${banner})`
        }}
      />
      <div className={styles["banner-text"]}>
        <div className={styles["banner-text-title"]}>{title}</div>
        <div className={styles["banner-text-subtitle"]}>{subtitle}</div>
      </div>
    </div>
  )
}

const SideMenu = () => {
  const { marketType, setMarketType } = useMarket()

  const handleMenuClick = (e) => {
    setMarketType(e.key)
  }

  return (
    <Menu
      mode="inline"
      selectedKeys={[marketType]}
      onClick={handleMenuClick}
      className={styles["customMenu"]}
      items={marketOptions.map((option) => {
        if (option.value !== "ALL") return { key: option.value, label: option.label }
      })}
    />
  )
}

export const MarketComponents = () => {
  const { marketType } = useMarket()
  const show = (module) => marketType === marketCode.ALL || marketType === module
  return (
    <div className={styles["market-container"]}>
      <div className={styles["market-side-menu"]}>
        <SideMenu />
      </div>
      <div className={styles["market-main"]}>
        {/* <div className={styles["title"]}>
        <TextAnimation letterSpacing={10}>灵犀市集</TextAnimation>
      </div>
      <MarketSearch /> */}
        <SearchBox />

        {/* 本期不做应用 TODO！ */}
        {/* {show(marketCode.APP) && <ItemModule type={marketCode.APP} />} */}
        {show(marketCode.AGENT) && <ItemModule type={marketCode.AGENT} />}
        {show(marketCode.SKILL) && <ItemModule type={marketCode.SKILL} />}
        {show(marketCode.PLUG_IN) && <ItemModule type={marketCode.PLUG_IN} />}
        {show(marketCode.ROBOT) && <ItemModule type={marketCode.ROBOT} />}
        {show(marketCode.TIMBRE) && <TimbreModule />}
      </div>
    </div>
  )
}

export const Market = () => (
  <MarketProvider>
    <MarketComponents />
  </MarketProvider>
)
