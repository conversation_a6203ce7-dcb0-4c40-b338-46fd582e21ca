.market-container {
  display: flex;
  width: 100%;
}

.market-side-menu {
  position: fixed;
  top: 0;
  left: 20px;
  width: 204px;
  height: 100%;
  border-inline-end: 1px solid rgba(5, 5, 5, 0.06);
}
.customMenu {
  margin-top: 22px;
  height: 100%;
  border-inline-end: none !important;
}

.market-header-content {
  position: fixed;
  top: 0;
  left: 225px;
  right: 0;
  padding: 30px 30px 0px;
  background-color: #fff;
  z-index: 1000;
  height: calc(8vw + 46px + 40px); //banner高度+search高度+30px

  display: flex;
  flex-direction: column;
}

.banner-container {
  width: 100%;
}

.search-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.market-main {
  flex: 1;
  box-sizing: border-box;
  padding: 20px 30px 30px 30px;
  min-height: 100%;
  margin-top: calc(8vw + 46px + 40px);
  margin-left: 214px;

  .market-banner {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    width: 100%;
    height: 8vw;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .banner-logo {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    width: 12%;
    height: 40%;
    margin-left: 40px;
  }
  .banner-text {
    width: 30%;
    margin-left: 20px;

    .banner-text-title {
      color: #ffffff;
      font-size: 1.3vw; /* 基于视口宽度 */
      letter-spacing: 2px;
      font-style: normal;
      margin-bottom: 8px;
    }
    .banner-text-subtitle {
      color: #d7d7d7;
      font-size: 0.8vw; /* 基于视口宽度 */
      letter-spacing: 1px;
      font-style: normal;
    }
  }
}

.title {
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 10px;
  text-align: center;
  line-height: 80px;
  margin-bottom: 20px;
}

.market-search {
  position: relative;
  width: 240px;
  height: 46px;

  display: flex;
  align-items: center;
}
.expand-search-style-input {
  transition: all 0.2s;
  width: 0px;
  padding: 0px;
  // border: none;
  height: 36px;
  border-width: 0px;
}
.style-input {
  width: 240px;
  border-width: 1px;
  padding: 4px 8px;
}
.header-search-icon {
  display: flex;
  width: 36px;
  height: 36px;
  padding: var(--Gap-xs, 8px);
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  border-radius: var(--Gap-xs, 8px);
  border: 1px solid var(---, #d0d5dd);
  background: var(---, #fff);
  box-shadow: 0px 1px 2px 0px rgba(24, 27, 37, 0.05);
  color: #98a2b3;
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    background: var(---, #fcfaff);
    border: 1px solid #cac0ff;
    color: #7f56d9;
  }
}

.market-search-container {
  :global {
    .ant-input-group-addon {
      padding: 0;
      border: none;
      background: none;

      .ant-select {
        .ant-select-selector {
          padding: 0 8px 0 0 !important;
          border: none !important;
          box-shadow: none;
        }
      }
    }

    .ant-input-affix-wrapper {
      border: none;
      box-shadow: none;
      padding: 4px;

      input.ant-input {
        font-size: 14px;
        &::placeholder {
          color: #86909c;
        }
      }
    }

    .ant-select-selection-item {
      color: #1d2129;
      font-size: 14px;
    }
  }
}

@keyframes borderAnimate {
  0%,
  100% {
    background-position: 0%, 50%;
  }
  50% {
    background-position: 100%, 50%;
  }
}
