.plugin-detail-section {
  overflow: hidden;
  width: 100%;
  // background: #f7f8fa;
  height: calc(100% - 100px);
  position: relative;
  padding: 20px;
  padding-right: 40px;
  overflow-y: auto;
}
.plugin-detail {
  box-sizing: border-box;
  padding-bottom: 50px;
  .header {
    display: flex;
    justify-content: space-between;
  }
  .header-info {
    display: flex;
    .detail-icon {
      border-radius: 16px;
      border-radius: 16px;
      height: 60px;
      width: 60px;
      padding: 5px;
      background: #5d5fee;
    }
    .center {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      :global {
        .ant-btn {
          width: 80px;
        }
      }
    }
    .title {
      margin-bottom: 5px;
      width: 100%;
      min-height: 32px;
    }
  }
}

.plugin-container {
  box-sizing: border-box;
  // padding: 0 100px;
  margin-top: 50px;
}

.header-line {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  img {
    width: 20px;
    border-radius: 5px;
    margin-right: 10px;
    border: 0;
  }
  .time {
    color: rgba(29, 28, 35, 0.35);
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 16px;
    text-align: left;
  }
}

.statistic-info {
  display: flex;
  align-items: top;
  :global {
    .ant-divider {
      height: 40px;
      margin: 0 26px;
    }
  }
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    > span:first-child {
      color: rgba(29, 28, 35, 0.6);
      font-size: 28px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 40px;
      text-align: left;
    }
    > span:last-child {
      color: rgba(29, 28, 35, 0.6);
      font-size: 12px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 16px;
      text-align: left;
    }
  }
}

.kit-container {
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  display: -webkit-box;
  overflow: hidden;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  margin-top: 32px;
  -webkit-font-smoothing: antialiased;
  line-height: 1.625;
  outline: none;
  overflow-wrap: break-word;
  position: relative;
  white-space: pre-wrap;
  color: #1f2329;
}
.header-divider {
  margin: 24px 0;
}
.title-container {
  align-items: center;
  display: flex;
  height: 32px;
  justify-content: space-between;
  width: 100%;
  color: rgb(56, 55, 67);
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  margin: 0;
  margin-bottom: 24px;
}
.plugin-tool-selector {
  display: flex;
  width: 100%;

  .selector {
    align-self: flex-start;
    height: 0;
    width: 186px;
    button {
      padding: 8px 16px;
      height: 40px;
    }
    :global {
      .ant-btn-text:not(:disabled):not(.ant-btn-disabled):hover {
        color: rgba(0, 0, 0, 0.88);
        background: none;
      }
    }
    .active {
      background-color: rgba(28, 28, 35, 0.05) !important;
      font-weight: 700;
    }
  }
  .selector-detaile {
    display: flex;
    flex: 1 1;
    :global {
      .ant-divider {
        height: 100%;
        margin-left: 12px;
        margin-right: 24px;
      }
      .ant-table {
        background: none !important;
      }
    }
    .kit-container {
      margin-top: 0;
      margin-bottom: 20px;
    }
    .detail {
      display: flex;
      flex-direction: column;
      width: 100%;
      .table-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
        margin: 0;
        margin-bottom: 20px;
      }
      thead > tr > th {
        color: #1d1c23;
        font-size: 12px;
        font-weight: normal;
        letter-spacing: 0;
        line-height: 20px;
        text-align: left;
        padding-bottom: 8px;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 8px;
        position: relative;
      }
      tr > td:nth-child(2) {
        color: rgba(29, 28, 35, 0.6);
      }
    }
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
