
.tabpane-wrapper {
  padding: 10px 20px;
}
.share-line {
  display: flex;
  align-items: baseline;

  .share-line-label {
    margin-right: 20px;
  }
}
.desc {
  color: #666;
  margin-left: 20px;
}
.workspace {
  width: 400px;
  margin-bottom: 20px;

  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
  }
  .add-share {
    color: #7F56D9;
    margin-left: 10px;
  }
}
.workspace-title {
  padding-left: 100px;
  margin-bottom: 10px;
}
.add-share-line {
  cursor: pointer;
  display: inline-block;
}
.dynamic-delete-button {
  position: relative;
  top: 0px;
  margin: 0 8px;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}
.dynamic-add-button {
  color: #7F56D9;
  text-align: left;
  padding-left: 5px;
}
.creat-icon-box {
  margin: 20px;
  display: flex;
  justify-content: center;
}
.creat-icon {
  font-size: 40px;
  color: #fff;
  background: #7F56D9;
  border-radius: 4px;
}
.header-list-box {
  position: relative;
}
.header-list-add {
  position: absolute;
  top: -25px;
  right: 10px;
  color: #7F56D9;
}
.header-list-table {
  border: 1px solid rgba(29, 28, 35, .12);
  border-radius: 8px;
  max-height: 348px;
  overflow: auto;
  padding: 0 16px;
}
.header-list-title {
  border-bottom: 1px solid rgba(29, 28, 35, .12);
}
.header-list-title-item {
  padding-left: 4px;
  padding-right: 4px;
  line-height: 16px;
  padding: 12px 0;
}
.header-list-content {
  padding: 12px 0;
}
