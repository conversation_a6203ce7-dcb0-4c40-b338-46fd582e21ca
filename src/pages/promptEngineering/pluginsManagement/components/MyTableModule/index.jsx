import React, { useState } from "react"
import styles from "./index.module.scss"
import {
  Button,
  Col,
  Row,
  message,
  Divider,
  ConfigProvider,
  Tooltip,
  Switch,
  Modal,
  Tag
} from "antd"
import { StarOutlined } from "@ant-design/icons"
import CopyToClipboard from "react-copy-to-clipboard"
import QueueAnim from "rc-queue-anim"
import queryString from "query-string"
import { useLocation, useNavigate } from "react-router-dom"
import { getThemeConfig } from "@/constants/market"
import { EmptyPro } from "@/components/Empty"
import { usePlginsManage } from "../.."
import { QUERY_KEYS } from "@/constants/queryKeys"
import { useQueryClient } from "@tanstack/react-query"
import { cancelBubble } from "@/utils"
import {
  usePluginListApi,
  useCancelSubscribeApi,
  useSubscribeApi,
  usePluginChangeStatus,
  usePluginDelete
} from "@/api/pluginManage"

// @ts-ignore
import toolImg from "@/assets/img/tool.png"
import "./index.scss"
import { Empty } from "antd"
import CustomEmpty from "@/antd-styles/components/CustomEmpty"

export const MyTableModule = (props) => {
  const {
    type = "app"
    // dataSource = [0, 1, ...new Array(10).fill(1).map((v, i) => `${2 * i}`)],
  } = props || {}
  // 获取链接上参数
  const navigate = useNavigate()
  const location = useLocation()
  const { search } = location
  const queryParams = queryString.parse(search) ?? {}
  const { botNo } = queryParams
  // 定义表格翻页
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 })
  // 获取统一样式
  const { color, hover, icon, title, border } = getThemeConfig("PLUG_IN")
  const { searchKey, setVisible, setIsEditing, setCurrentRecord } = usePlginsManage()
  const theme = {
    components: {
      Button: {
        colorPrimary: color,
        defaultBorderColor: color,
        defaultColor: color,
        colorPrimaryHover: hover,
        colorPrimaryActive: hover
      },
      Switch: {
        colorPrimary: color,
        colorPrimaryHover: hover
      }
    }
  }
  // 获取列表
  const { data, isLoading: listLoading } = usePluginListApi({
    // pageNum: pagination.current,
    // pageSize: pagination.pageSize,
    botNo,
    searchText: searchKey
  })
  const dataSource = data || []
  const totalCount = data?.totalCount ?? 0
  const queryClient = useQueryClient()
  const updateHome = () => queryClient.invalidateQueries([QUERY_KEYS.PLUGIN_LIST])
  // 订阅
  const { mutate: mutateSubscribe } = useSubscribeApi(updateHome)
  const { mutate: mutateCancelSubscribe } = useCancelSubscribeApi(updateHome)
  // 修改状态
  const { mutate: mutateChangeStatus } = usePluginChangeStatus(updateHome)
  // 删除
  const { mutate: mutateDelete } = usePluginDelete(updateHome)
  // 取消订阅
  const handleCancelSubscribe = (e, record) => {
    mutateCancelSubscribe({
      botNo,
      pluginNo: record.pluginNo,
      status: record.bizNo
    })
  }
  // 订阅
  const handleSubscribe = (e, record) => {
    mutateSubscribe({ botNo, bizType: type, bizNo: record.bizNo })
  }
  // 修改状态
  const handleChangeStatus = (checked, record, e) => {
    cancelBubble(e)
    const status = checked ? 1 : 0
    const confirmContent =
      status === 1
        ? "启用后，将启用该插件所有相关调用，是否确认？"
        : "停用后，将停止该插件所有相关调用，是否确认？"
    Modal.confirm({
      title: "提示",
      content: confirmContent,
      onOk: () => {
        mutateChangeStatus({ botNo, pluginNo: record.pluginNo, status: status })
      },
      onCancel: () => {
        // 如果用户点击取消，不做任何操作
      }
    })
  }
  // 删除
  const handleDelete = (e, record) => {
    mutateDelete({ botNo, bizType: type, bizNo: record.bizNo })
  }
  // 设置
  const handleEdit = (e, record) => {
    cancelBubble(e)
    setCurrentRecord(record)
    setIsEditing(true)
    setVisible(true)
  }
  // 跳转至工具
  const handleGoTool = (record) => {
    const { botNo, pluginNo } = record || {}
    const urls = queryString.stringifyUrl({
      url: `/plugin/${pluginNo}`,
      query: {
        botNo
      }
    })
    navigate(urls)
  }

  return (
    <div className={styles["market-item-module"]} key={type}>
      {/* <Divider className={styles["divider"]}>我的工具</Divider> */}
      <ConfigProvider theme={theme}>
        {dataSource.length > 0 ? (
          <>
            <div className={styles["list-container"]}>
              <Row gutter={15} className="promptEngineering">
                {dataSource.map((v, i) => (
                  <Col xs={24} sm={12} md={12} lg={8} xl={8} xxl={6} key={v.id}>
                    {/* <QueueAnim
                      delay={50 * i}
                      type="top"
                      className="queue-simple"
                    >
                    </QueueAnim> */}
                    <div
                      className={styles["card-item"]}
                      key={i}
                      // style={{ borderColor: border }}
                      onClick={() => handleGoTool(v)}
                    >
                      <div className="w-[100%]">
                        {/* className={styles["card"]} */}
                        <div>
                          <div className={styles["info"]}>
                            <div className={styles["text-base"]}>
                              <div className="flex items-center justify-center mr-[12px] bg-[#E0FAEC] bg-opacity-50 w-[48px] h-[48px] rounded-[8px]">
                                <img className={styles["text-base-img"]} src={toolImg} alt="" />
                              </div>
                              <div className="flex flex-1 flex-col items-start overflow-hidden">
                                <Tooltip
                                  title={v.name}
                                  className="overflow-hidden text-ellipsis whitespace-nowrap w-[80%]"
                                >
                                  {v.name}
                                </Tooltip>
                                <CopyToClipboard
                                  text={v.pluginNo}
                                  onCopy={() => message.success("复制成功")}
                                >
                                  <Tooltip title="点击复制工具编号">
                                    <span
                                      className="text-[#626263] cursor-pointer leading-[18px] text-[12px] mt-[8px] font-normal"
                                      onClick={(e) => cancelBubble(e)}
                                    >
                                      {v.pluginNo}
                                    </span>
                                  </Tooltip>
                                </CopyToClipboard>
                              </div>
                            </div>

                            <div className={styles["custom-left"]}>
                              <Switch
                                size="small"
                                value={v.status === 1}
                                onClick={(checked, e) => handleChangeStatus(checked, v, e)}
                              />
                            </div>
                          </div>
                          {/* <span className={styles["iconimg"]}>{icon}</span> */}
                        </div>
                        <div className="skill-item-text belongTo-botName">
                          {v.type === "MCP" && (
                            <div className="time-tag">
                              <span className="title">类型</span>
                              <span className="time">MCP</span>
                            </div>
                          )}
                          <div className="time-tag">
                            <span className="title">订阅量</span>
                            <span className="time">{v?.subscriberCount}</span>
                          </div>
                          {/* <div className="time-tag">
                              {v?.subscribeCount > 0 && (
                                <div className="time-tag">
                                  <span className="title">订阅量</span>
                                  <span className="time">
                                    {v?.subscribeCount}
                                  </span>
                                </div>
                              )}
                            </div> */}
                        </div>
                        <Tooltip title={v.description} className={styles["description"]}>
                          {v.description || "这个人很懒，暂未填写描述～"}
                        </Tooltip>
                        <Divider className="!my-[18px] !mt-[18px]" />
                        <div className="bottom-center ">
                          <div className="remove-btn">
                            {/* v.share */}
                            {v.share ? (
                              <div className="-mt-[8px]">
                                <StarOutlined className="mr-[12px]" />
                                <span>{v.subscriberCount}</span>
                              </div>
                            ) : null}
                            <Button
                              type="link"
                              onClick={(e) => handleEdit(e, v)}
                              className="p-0 !text-[#181B25] !hover:text-[#7F56D9] text-[12px]"
                            >
                              <i className="iconfont icon-peizhi align-middle -mt-[1px] mr-[3px]"></i>
                              使用设置
                            </Button>
                            {/* <Button
                                type="link"
                                className="p-0 !text-[#181B25] !hover:text-[#7F56D9]"
                                onClick={(e) => handleSetCurrentSkill(e, skill)}
                              >
                                <i className="iconfont icon-peizhi align-middle mt-[1px] mr-[10px]"></i>
                                使用设置
                              </Button> */}
                          </div>
                        </div>
                        {/* <div className={styles["custom"]}> */}
                        {/* <div className={styles["custom-right"]}> */}
                        {/* {v.share ? (
                                <div
                                  className={styles["count"]}
                                  style={{ color: color }}
                                >
                                  <StarOutlined />
                                  <span>{v.subscriberCount}</span>
                                </div>
                              ) : null}
                              <Button
                                type="link"
                                onClick={(e) => handleEdit(e, v)}
                                style={{ color: color }}
                              >
                                使用设置
                              </Button> */}
                        {/* <Popconfirm
                                title={"【删除】后，将停止该插件所有相关调用"}
                                okText="确认"
                                cancelText="取消"
                                onConfirm={(e) => handleDelete(e, v)}
                              >
                                <Button type="link">删除</Button>
                              </Popconfirm> */}
                        {/* </div> */}
                        {/* </div> */}
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
              {/* <Pagination
                className="pr-2"
                current={pagination?.current ?? 1}
                pageSize={pagination?.pageSize ?? 10}
                total={totalCount}
                onChange={(page, pageSize) => {
                  const p = { current: page, pageSize }
                  setPagination(p)
                }}
                showSizeChanger={true}
                style={{ marginTop: "15px", textAlign: "right" }}
                showTotal={(total) => `共 ${total} 条`}
              /> */}
            </div>
          </>
        ) : (
          <div className="mt-[34vh]">
            <CustomEmpty description={`您还未创建任何工具`}></CustomEmpty>
          </div>
        )}
      </ConfigProvider>
    </div>
  )
}
