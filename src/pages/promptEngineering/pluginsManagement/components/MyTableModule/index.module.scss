.market-item-module {
  .divider {
    font-size: 18px;
    font-weight: 900;
    margin: 40px 0;
    letter-spacing: 10px;
  }
  :global {
    .ant-card-body {
      padding: 15px;
    }
  }
}
.list-container {
  :global {
    .ant-col {
      margin-bottom: 15px;
    }
    .ant-btn {
      height: auto !important;
      padding: 0 16px;
    }
  }
}
.card {
  .card-info {
    display: flex;
    justify-content: space-between;
    .info {
      width: 200px;
      flex: none;
      > span:first-child {
        font-size: 20px;
        font-weight: bold;
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-bottom: 5px;
      }
      .description {
        font-size: 14px;
        color: #333;
        display: block;
        margin-bottom: 10px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
  }
  .origin {
    font-size: 12px;
    color: #999;
    display: block;
  }
  .iconimg {
    font-size: 50px;
    color: #f0f0f0;
    margin-bottom: 20px;
  }
  .custom {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    .count {
      font-size: 12px;
      line-height: 24px;
      margin-right: 5px;
      :global {
        .anticon {
          margin-right: 10px;
        }
      }
    }
  }
}
.card-item {
  height: 212px;
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  flex: 1 0 0;
  border-radius: var(--Gap-s, 12px);
  border: 1px solid var(---, #e4e7ec);
  background: linear-gradient(117.35deg, #ffffff 69.3%, #f3feff 98.8%);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  &:before {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    z-index: -1;
    border-radius: var(--Gap-s, 13px);
    content: "";
    background-image: linear-gradient(249.29deg, #00d3ae -0.07%, #55fd5a 97.52%);
    opacity: 0;
    transition: opacity 0.3s;
  }
  &:hover {
    border: 1px solid transparent;
    box-shadow: 0px 4px 32px 0px rgba(51, 51, 51, 0.12);
    &:before {
      opacity: 1;
    }
  }
}
.text-base-img {
  width: 48px;
  display: inline-block;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  vertical-align: middle;
}

.text-base {
  color: #181b25;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  display: flex;
  vertical-align: middle;
  overflow: hidden;
  flex: 1;
}

.info {
  display: flex;
  justify-content: space-between;
  align-self: stretch;
  width: 100%;
}
.description {
  color: var(---, #344054);
  font-size: 12px;
  font-style: normal;
  margin-top: 8px;
  font-weight: 400;
  width: 100%;
  line-height: 18px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
.empty {
  :global {
    .ant-empty-image {
      height: auto;
    }
  }
  img {
    width: 180px;
    margin: 20px 0;
  }
}

.custom-right {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  :global {
    .ant-btn-link {
      padding: 0 1px;
    }
  }
}
.list-card {
  min-width: 280px;
}
