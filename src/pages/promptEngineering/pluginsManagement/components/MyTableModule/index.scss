.promptEngineering {
  .skill-item-text {
    max-width: 100%;
    display: flex;
    align-items: center;
    gap: var(--Gap-xs, 8px);
    align-self: stretch;
    margin-top: 12px;
    // display: inline-block;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }

  .time-tag {
    display: flex;
    padding: 4px var(--Gap-xs, 8px);
    align-items: center;
    gap: 4px;
    border-radius: 100px;
    background: var(---, #f5f7fa);
    .title {
      font-size: 12px !important;
      font-style: normal;
      font-weight: 400;
      color: #475467 !important;
    }
    .time {
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      color: #181b25 !important;
    }
  }

  .belongTo-botName {
    font-size: 12px;
    color: #b1b1b1;
    white-space: nowrap;
  }

  .bottom-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
  }

  .remove-btn {
    // position: absolute;
    // right: 25px;
    // display: flex;
    // justify-content: space-between;
    // align-items: flex-start;
    width: 100%;
    display: flex;
    justify-content: space-around; //space-between;
    padding: 0 0%;
    // align-items: flex-start;
    align-self: stretch;
    .subscribe-count {
      font-size: 14px;
      margin-right: 12px;
      color: #7f56d9;
      .count-text {
        margin-left: 4px;
      }
    }
    .remove-btn-insert {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      color: #181b25 !important;
    }
    .remove-btn-insert-sub {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }
  }

  .status {
    color: var(-----700, #178c4e);
    // font-size: 12px;
    // font-weight: 500;
    // border-radius: 2px;
    background: var(-----50, #e0faec);
    min-width: 52px;
    white-space: nowrap;
    // padding: 2px 6px;
    // min-width: 48px;

    display: flex;
    height: var(--Gap-l, 24px);
    padding: 4px var(--Gap-xs, 8px);
    align-items: center;
    gap: var(--Gap-xs, 8px);
    border-radius: 4px;
    text-align: right;
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }

  .status.debug {
    background: var(-----50, #fff1eb);
    color: var(----600, #e97135);
  }

  // 未启用
  .status.not {
    color: #6f7271;
    background: #f2f3f5;
  }

  .tab-skill {
    position: fixed;
    top: 0px;
    z-index: 10;
    // background: #fff;
    left: 0;
    width: 100%;
    padding: 20px;
    padding-top: 0px;
    padding-bottom: 5px !important;
    transition: all 0.2s;
    // background: #fff !important;
    background: linear-gradient(180deg, #ebeaff -75%, #fff 30.97%) !important;
  }
  .tab-skill-shadow {
    box-shadow: 0px 4px 12px 0px rgba(24, 27, 37, 0.08);
  }
}
