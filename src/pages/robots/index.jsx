import Iconfont from "@/components/Icon"
import { SKILLICONTYPE } from "@/constants"
import { StarOutlined } from "@ant-design/icons"
import { newAvatarList } from "@/assets/imgUrl"
import {
  <PERSON><PERSON>,
  Card,
  Col,
  ConfigProvider,
  Divider,
  Input,
  Modal,
  Popconfirm,
  Row,
  Switch,
  Tooltip,
  Typography,
  message
} from "antd"
import { useMemo, useState } from "react"
// import "./../addBot/components/SkillList.scss"
import "./skillList.scss"

import { cancelBubble, postMessageForLX } from "@/utils"
import CardSettingModal from "./cardSettingModal"
import { useRobotsList, useUpdateRobotStatus, useUpdateRobotUsingSetting } from "@/api/robots"
import { useLocation } from "react-router-dom"
import queryString from "query-string"
import { useQueryClient } from "@tanstack/react-query"
import { QUERY_KEYS } from "@/constants/queryKeys"
import UsingSetting from "./usingSetting"
import { ROBOT_TYPE_MINE, ROBOT_TYPE_SUBSCRIBE } from "./constants"
import { useCancelSubscribeApi, useMarketHomeListApi } from "@/api/market"
import { debounce } from "lodash"
import { useFetchSubscribeSkillListByPage } from "@/api/skill"
import { getThemeConfig, marketCode } from "@/constants/market"
import { MarketProvider } from "../market"
import { MessageType } from "@/constants/postMessageType"
const { Text } = Typography

function Robots() {
  const [searchText, setSearchText] = useState("")
  const location = useLocation()
  const { search } = location
  const queryParams = queryString.parse(search)
  const { botNo } = queryParams
  const { data: robotList = [] } = useRobotsList({ botNo, searchText })
  const { data: subscribeData, isLoading: subscribeRobotListLoading = false } =
    useFetchSubscribeSkillListByPage({
      pageNum: 1,
      pageSize: 20,
      botNo,
      bizType: "ROBOT",
      query: searchText
    })

  const subscribeRobotList = useMemo(() => {
    return subscribeData?.list || []
  }, [subscribeData])
  const debounceSetSearch = debounce((value) => {
    setSearchText(value)
  }, 800)

  const dividerStyle = {
    fontSize: "18px",
    fontWeight: "900",
    margin: "40px 0",
    letterSpacing: "10px"
  }

  return (
    <div className="admin-container skill-list-wrapper-old">
      <div className="admin-header" style={{ display: "block" }}>
        <div className="flex items-center justify-between w-full pb-4">
          <h2 className="flex items-center">应用管理</h2>
        </div>
        <Input
          onChange={(e) => debounceSetSearch(e.target.value)}
          placeholder="搜索应用名称/描述"
          style={{ width: "400px" }}
        />
      </div>
      <div className="admin-content skill-list-wrapper">
        <Divider style={dividerStyle}>我的应用</Divider>
        <Row gutter={16}>
          {robotList.map((item) => {
            return <RobotItem robot={item} key={item.botNo} botNo={botNo} type={ROBOT_TYPE_MINE} />
          })}
        </Row>
        <Divider style={dividerStyle}>订阅应用</Divider>
        <Row gutter={16}>
          {subscribeRobotList.map((item) => {
            return (
              <RobotItem robot={item} key={item.botNo} botNo={botNo} type={ROBOT_TYPE_SUBSCRIBE} />
            )
          })}
        </Row>
      </div>
    </div>
  )
}

const RobotItem = ({ robot, type = "", botNo }) => {
  const {
    bizNo,
    iconUrl,
    name,
    robotDesc: desc,
    status,
    showInChatWnd,
    belongToBotName,
    statusDisplayName,
    subscribeCount,
    isSubscribed = true,
    jumpUrl,
    resourceCode
  } = robot
  const robotNo = robot.robotNo || bizNo

  const [switchChecked, setSwitchChecked] = useState(status === "1")
  const [currentRobot, setCurrentRobot] = useState({})
  const [cardModalVisible, setCardModalVisible] = useState(false)
  const [usingSettingVisible, setUsingSettingVisible] = useState(false)

  const { mutate: updateRobotStatus } = useUpdateRobotStatus()
  const { mutate: updateRobotUsingSetting } = useUpdateRobotUsingSetting()
  const queryClient = useQueryClient()

  const { color, hover, border } = getThemeConfig(marketCode.ROBOT)
  const themConfig = {
    colorPrimary: color,
    defaultBorderColor: color,
    defaultColor: color,
    colorPrimaryHover: hover,
    colorPrimaryActive: hover
  }
  const theme = {
    components: {
      Switch: themConfig,
      Button: themConfig,
      Radio: themConfig
    }
  }

  const refreshAPPRobotList = () => {
    postMessageForLX({
      type: MessageType.REFRESH_APP_ROBOT_LIST
    })
  }

  const handleSwitchChange = (checked, robot, e) => {
    e.stopPropagation()
    const newStatus = checked ? "1" : "0"
    const confirmContent =
      newStatus === "1"
        ? "启用后，将在应用端展示该应用，是否确认？"
        : "停用后，应用端将不再展示该应用，是否确认？"

    Modal.confirm({
      title: "提示",
      content: confirmContent,
      onOk: () => {
        updateRobotStatus(
          {
            botNo,
            robotNo: robot.robotNo,
            status: newStatus
          },
          {
            onSuccess: (e) => {
              if (e.success) {
                message.success(e.message)
                setSwitchChecked(checked)
                refreshAPPRobotList()
                queryClient.invalidateQueries([QUERY_KEYS.ROBOT_LIST])
              } else {
                message.error(e.message)
              }
            }
          }
        )
      },
      onCancel: () => {
        // 如果用户点击取消，不做任何操作
      }
    })
  }

  const handleUsingSetting = (e, robot) => {
    cancelBubble(e)
    setCurrentRobot(robot)
    setUsingSettingVisible(true)
  }

  const handleSetCurrentRobot = (e, robot) => {
    cancelBubble(e)
    setCurrentRobot(robot)
    setCardModalVisible(true)
  }

  const onUsingSettingFinish = ({ showInChatWnd }) => {
    updateRobotUsingSetting(
      {
        botNo,
        robotNo,
        status: showInChatWnd
      },
      {
        onSuccess: (e) => {
          if (e.success) {
            message.success("更新成功")
            setUsingSettingVisible(false)
            querySubscribedRobots()
          } else {
            message.error(e.message)
          }
        }
      }
    )
  }

  const querySubscribedRobots = () => {
    queryClient.invalidateQueries([QUERY_KEYS.ROBOT_LIST])
    queryClient.invalidateQueries([QUERY_KEYS.SUBSCRIBE_SKILL_LIST_BY_PAGE])
    refreshAPPRobotList()
  }

  const { mutate: mutateCancelSubscribe } = useCancelSubscribeApi(querySubscribedRobots)
  const onCancelSubscribe = async (e) => {
    cancelBubble(e)
    return mutateCancelSubscribe({
      botNo,
      bizType: "ROBOT",
      bizNo: robotNo
    })
  }

  const handleCardClick = () => {
    postMessageForLX({
      type: MessageType.TO_APP_ROBOT_DETAIL_PAGE,
      payload: {
        jumpUrl,
        workbenchNo: resourceCode
      }
    })
  }
  const linkButtonStyle = { color }
  const deactivate = (status === 1 || status === 0) && !switchChecked
  const using = status === true || status === "1"
  return (
    <Col md={12} lg={8} xl={6} key={name}>
      <ConfigProvider theme={theme}>
        <Card
          onClick={handleCardClick}
          className={`card-item ${deactivate && "deactivate"} cursor-pointer`}
          style={{ marginBottom: "20px", borderColor: border }}
        >
          <div className="flex justify-between items-start mb-2" style={{ height: 33 }}>
            <div style={{ maxWidth: "84%" }}>
              <div className="title mr-2 skill-item-text">
                <Tooltip title={name}>
                  <Text className="text-base">{name}</Text>
                </Tooltip>
              </div>
              <Tooltip title={desc}>
                <Text className="desc">{desc}</Text>
              </Tooltip>
              {belongToBotName && (
                <div className="skill-item-text belongTo-botName">
                  <Tooltip title={belongToBotName}>来源：{belongToBotName}</Tooltip>
                </div>
              )}
            </div>
            <div>
              <img src={iconUrl} className=" rounded-full" style={{ width: 48, height: 48 }} />
            </div>
          </div>
          <div className="mb-4 flex justify-between items-center absolute bottom-0 w-full">
            {type === ROBOT_TYPE_SUBSCRIBE ? (
              // 兼容订阅技能的显示
              <span
                className={`subscribe-status ${using && "using"}`}
                style={{ backgroundColor: using ? color : "#bfbfbf" }}
              >
                {using ? "启用中" : "已停用"}
              </span>
            ) : type === ROBOT_TYPE_MINE ? (
              <Switch
                checked={switchChecked}
                onChange={(checked, e) => handleSwitchChange(checked, robot, e)}
                defaultChecked={false}
              />
            ) : (
              <span
                className={`status ${status === "2" ? "debug" : status === "0" ? "not" : undefined}`}
              >
                {statusDisplayName}
              </span>
            )}

            <div className="remove-btn">
              {subscribeCount > 0 && (
                <span className="subscribe-count" style={{ color }}>
                  <StarOutlined />
                  <span className="count-text">{subscribeCount}</span>
                </span>
              )}
              {type === ROBOT_TYPE_SUBSCRIBE && isSubscribed ? (
                // 订阅技能
                <>
                  <Button
                    type="link"
                    className="p-0"
                    // style={linkButtonStyle}
                    onClick={(e) => handleUsingSetting(e, robot)}
                  >
                    使用设置
                  </Button>
                  <Popconfirm
                    title=""
                    description="【取消订阅】后，将无法调用对应应用"
                    onConfirm={(e) => onCancelSubscribe(e)}
                    onPopupClick={(e) => cancelBubble(e)}
                  >
                    <Button
                      type="link"
                      // style={linkButtonStyle}
                      onClick={(e) => cancelBubble(e)}
                    >
                      取消订阅
                    </Button>
                  </Popconfirm>
                </>
              ) : (
                <>
                  <Button
                    type="link"
                    className="p-0 mr-4"
                    style={linkButtonStyle}
                    onClick={(e) => handleSetCurrentRobot(e, robot)}
                  >
                    设置
                  </Button>
                </>
              )}
            </div>
          </div>
        </Card>

        <CardSettingModal
          onSuccess={() => {}}
          visible={cardModalVisible}
          key={currentRobot?.robotNo}
          robotNo={currentRobot?.robotNo}
          botNo={currentRobot?.botNo}
          initialValues={{
            ...currentRobot
          }}
          onClose={() => {
            setCardModalVisible(false)
          }}
        />
        <UsingSetting
          visible={usingSettingVisible}
          onClose={() => setUsingSettingVisible(false)}
          initialValues={{ showInChatWnd }}
          onFinish={onUsingSettingFinish}
        />
      </ConfigProvider>
    </Col>
  )
}

export default Robots
