.skill-list-wrapper-old {
  .skill-list-wrapper.iframeStyle {
    max-height: 100%;
    .header,
    .content {
      background-color: #f7f8fa;
      padding: 20px;
      border-radius: 14px;
    }

    .header {
      margin-bottom: 10px;
      margin-top: 0;
    }
  }

  .skill-list-wrapper {
    padding-right: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    overflow-x: hidden;
    .form-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 14px;
    }
    .divider {
      font-size: 18px;
      font-weight: 900;
      margin: 40px 0 0;
      letter-spacing: 10px;
    }

    .order-btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        margin-right: 10px;
      }
    }
    .tooltip-bar {
      margin-bottom: 10px;
      color: var(--text-5, #1d2129);
      font-size: 14px;
      font-weight: 400;
      font-weight: 500;
    }

    .text-base {
      font-size: 18px;
    }
    .skill-type-badge {
      position: absolute;
      top: 0px;
      right: 0px;
      background-color: #f0f0f0;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      color: #5d5fef;
    }
    .skill-group-badge {
      background-color: var(--main-2, #f2f2fe);
      padding: 2px 20px;
      border-radius: 4px;
      font-size: 12px;
      color: #5d5fef !important;
      border-radius: 15px;
      font-weight: bold;
    }

    .desc {
      min-height: 40px;
      margin-bottom: 2px;
      font-size: 12px;
      color: #777;
      // 俩行溢出
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }

    .belongTo-botName {
      font-size: 12px;
      color: #b1b1b1;
      white-space: nowrap;
    }

    .remove-btn {
      position: absolute;
      right: 25px;
      .subscribe-count {
        font-size: 14px;
        margin-right: 12px;
        color: #5d5fef;
        .count-text {
          margin-left: 4px;
        }
      }
    }

    .subscribe-status {
      line-height: 24px;
      padding: 0 12px;
      border-radius: 16px;
      font-size: 14px;
      background-color: #bfbfbf;
      color: #fff;
      &.using {
        background-color: #5d5fef;
      }
    }

    .card-item.deactivate {
      border-color: #f3f3f3;
    }

    .card-item {
      min-width: 190px;
      height: 170px;
      position: relative;
      border: 2px solid #d7d5fc;

      .title {
        color: var(--text-5, #1d2129);
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 400;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .skill-large-icon {
        font-size: 46px;
        color: #dad9d9;
      }

      .skill-item-text {
        max-width: 100%;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .status {
        color: var(--6, #00b42a);
        font-size: 12px;
        font-weight: 500;
        border-radius: 2px;
        background: var(--1, #e8ffea);
        padding: 2px 6px;
        min-width: 48px;
      }

      .status.debug {
        color: var(--6, #ff7d00);
        background: var(--1, #fff7e8);
      }

      // 未启用
      .status.not {
        color: #6f7271;
        background: #f2f3f5;
      }
    }

    .swich {
      position: absolute;
      bottom: 20px;
      right: 12px;
    }
  }
}
