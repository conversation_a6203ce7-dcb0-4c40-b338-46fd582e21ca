import React, { useState, useEffect } from "react"
import { useLocation } from "react-router-dom"
import VoiceFlow from "./components/VoiceFlow"
import { useGetFlowConfigDetails } from "@/api/voiceAgent"
import { Spin } from "antd"
import queryString from "query-string"

const Canvas = () => {
  // 获取URL参数
  const location = useLocation()
  const { id: taskId, botNo, taskName, status, agentNo } = queryString.parse(location.search)
  const [flowConfig, setFlowConfig] = useState(null)

  // 获取画布历史节点
  const {
    refetch: refreshFlowConfig,
    data: flowConfigData,
    isLoading
  } = useGetFlowConfigDetails({
    taskId,
    botNo,
    agentNo
  })

  const refreshFlowConfigHandle = () => {
    if (taskId && botNo) {
      refreshFlowConfig()
    }
  }

  // 处理获取的数据
  useEffect(() => {
    if (flowConfigData?.data) {
      // 测试，处理nodeCodes下面数据 position 全是 null
      flowConfigData?.data?.nodeCodes?.forEach((node) => {
        if (node.position && Object.keys(node.position).length === 0) {
          node.position = { x: null, y: null }
        }
      })

      setFlowConfig(flowConfigData.data)
    }
  }, [flowConfigData])

  return (
    <Spin spinning={isLoading} tip="加载中..." size="large">
      <div style={{ height: "100vh", width: "100%" }}>
        <VoiceFlow
          flowConfig={flowConfig}
          taskId={taskId}
          botNo={botNo}
          agentNo={agentNo}
          taskName={taskName}
          status={status}
          refreshFlowConfig={refreshFlowConfigHandle}
        />
      </div>
    </Spin>
  )
}

export default Canvas
