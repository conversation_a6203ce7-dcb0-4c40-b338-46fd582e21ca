import React, { memo } from "react"
import { Edge<PERSON>abel<PERSON><PERSON><PERSON>, getSmoothStepPath } from "reactflow"

// 高亮样式常量
const highlightStyle = {
  opacity: 1,
  stroke: "rgba(127, 86, 217, 1)",
  strokeWidth: 1.5,
  strokeDasharray: "15, 5",
  animation: "flowAnimation 1s linear infinite",
  boxShadow: "0 0 10px 0 rgba(127, 86, 217, 0.3)"
}

// 自定义边组件
const CustomEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  selected,
  markerEnd,
  style,
  data
}) => {
  // 计算边的路径和中点坐标 - 使用平滑步进路径
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
    borderRadius: 10 // 添加圆角使连接线更平滑
  })

  // 删除边的处理函数
  const onEdgeDelete = (evt) => {
    evt.stopPropagation()
    // 使用从 data 中获取的回调函数
    if (data?.onDeleteEdge) {
      data.onDeleteEdge(id)
      console.log(`尝试删除连接线: ${id}`) // 添加日志便于调试
    }
  }

  // 合并样式，选中时应用高亮样式
  const edgeStyle = {
    ...style,
    ...(selected ? highlightStyle : {})
  }

  // 检查是否为状态机模式，如果是则不允许删除连接线
  const isStateMachine = data?.flowType === 2

  return (
    <>
      {/* 添加一个不可见的更宽的路径，用于增加点击区域 */}
      <path
        className="react-flow__edge-interaction-path"
        d={edgePath}
        strokeWidth={20}
        stroke="transparent"
        fill="none"
      />
      {/* 显示的实际边 */}
      <path
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
        style={edgeStyle}
      />
      {selected && !isStateMachine && (
        <EdgeLabelRenderer>
          <div
            className="absolute z-10 nodrag nopan"
            style={{
              transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
              pointerEvents: "all",
              display: "flex",
              flexDirection: "column",
              alignItems: "center"
            }}
          >
            <div style={{ fontSize: "10px", color: "#666", marginBottom: "4px" }}>
              按Delete键或双击可删除连接线
            </div>
            <button className="edge-delete-button" onClick={onEdgeDelete}>
              删除
            </button>
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  )
}

export default memo(CustomEdge)
