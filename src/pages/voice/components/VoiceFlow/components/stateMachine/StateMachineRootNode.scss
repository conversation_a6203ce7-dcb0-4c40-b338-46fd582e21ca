.state-machine-root-node {
  position: relative;

  .add-node-btn {
    position: absolute;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #7f56d9;
    border: 2px solid #7f56d9;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    z-index: 10;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.2s ease;

    &.left-btn {
      left: -26px; //-36px
    }

    &.right-btn {
      right: -26px; // -36px
    }

    &:hover {
      transform: translateY(-50%) scale(1.1);
      background: #6941c6;
      border-color: #6941c6;
      box-shadow: 0 2px 8px rgba(127, 86, 217, 0.3);
    }

    .anticon {
      font-size: 12px;
      z-index: 2;
      position: relative;
    }

    // Handle 样式调整
    .react-flow__handle {
      opacity: 0; // 隐藏默认的连接点样式，但保持功能
      cursor: pointer;
      // 确保Handle完全覆盖按钮并居中
      border-radius: 50%;
      transform: none !important; // 覆盖默认的transform
      top: 0 !important;
      left: 0 !important;
      right: auto !important;
      bottom: auto !important;
    }
  }
}
