.voice-flow-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: rgb(241, 242, 247);

  .voice-flow-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    // .react-flow__background {
    //   display: none;
    // }
  }

  .react-flow__handle-top {
    left: 50%;
    top: 13px;
    transform: translate(-50%, 0);
    width: 20px;
    height: 10px;
    border-radius: 4px !important;
    background: #7f56d9;
    z-index: 10;
    // border: 2px solid #949494;
    // top: 0px;
    // transform: translate(-50%, 0);
    // width: 100%;
    // height: calc(100% - 60px);
    // background: transparent;
    // // border: 1px solid #949494;
    // border-radius: 0;
  }
}

.react-flow__node {
  overflow: visible !important;
  padding: 5px;
}

.react-flow__node-content {
  overflow: visible !important;
}
