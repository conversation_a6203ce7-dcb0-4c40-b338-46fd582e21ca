.react-flow__edge {
  &.selected {
    .react-flow__edge-path {
      stroke: #7f56d9 !important;
      stroke-width: 2px !important;
      filter: drop-shadow(0 0 3px rgba(127, 86, 217, 0.5));
    }
  }

  /* 默认连接线样式增强 */
  .react-flow__edge-path {
    stroke-width: 1.5px;
    transition: all 0.2s ease;
  }

  /* 连接线交互区域样式 */
  .react-flow__edge-interaction-path {
    cursor: pointer;
  }

  /* 鼠标悬停时高亮显示连接线 */
  &:hover {
    .react-flow__edge-path {
      stroke: #7f56d9;
      stroke-width: 2px;
      filter: drop-shadow(0 0 2px rgba(127, 86, 217, 0.3));
    }
  }
}

/* 添加一个动画效果，提示用户连接线是可以交互的 */
@keyframes pulseEdge {
  0% {
    filter: drop-shadow(0 0 2px rgba(127, 86, 217, 0));
  }
  50% {
    filter: drop-shadow(0 0 4px rgba(127, 86, 217, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(127, 86, 217, 0));
  }
}

.react-flow__edge.selected .react-flow__edge-path {
  animation: pulseEdge 2s infinite;
}

.edge-delete-button {
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 2px 4px; /* 增大按钮尺寸 */
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 添加阴影增强可见性 */
  transition: all 0.2s ease;

  &:hover {
    background-color: #ff7875;
    transform: scale(1.05);
  }
}

/* 优化React Flow性能 */
.react-flow {
  /* 启用硬件加速 */
  .react-flow__renderer,
  .react-flow__pane,
  .react-flow__viewport {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* 确保拖动节点时边能实时更新 */
  .react-flow__node {
    &.dragging {
      z-index: 10;
    }
  }

  /* 平滑边的动画效果 */
  .react-flow__edge {
    transition: none; /* 移除边的过渡效果，使其实时更新 */

    .react-flow__edge-path {
      transition: none; /* 移除边路径的过渡效果 */
    }
  }
}

/* 节点拖动时的样式优化 */
body.node-dragging {
  /* 拖动时优化性能 */
  cursor: grabbing !important;

  .react-flow__edge {
    pointer-events: none !important; /* 拖动时禁用边的交互，提高性能 */

    .react-flow__edge-path {
      transition: none !important;
      animation: none !important;
    }
  }

  /* 拖动时确保高性能渲染 */
  .react-flow__renderer {
    will-change: transform;
  }
}

/* 确保通用边缘实时更新 */
.react-flow__edges {
  pointer-events: none; /* 默认边容器不接收鼠标事件，提高性能 */
}

.react-flow__edge {
  pointer-events: visibleStroke; /* 只在可见的线条上接收鼠标事件 */
  cursor: pointer;

  &.selected {
    z-index: 1000 !important; /* 选中的边显示在最上层 */
  }
}

/* 连接规则样式 */
.connection-rule-tip {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  font-size: 12px;
  color: #333;
  width: 200px;
}

/* 无效连接提示样式 */
.invalid-connection-tooltip {
  position: absolute;
  background: rgba(255, 77, 79, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}

/* 处理标签指针样式 */
.react-flow__handle.connecting {
  background: #7f56d9 !important;
}

.react-flow__handle.valid {
  background: #52c41a !important;
}

.react-flow__handle.invalid {
  background: #ff4d4f !important;
}

/* 为标签Handle添加悬停提示 */
.tag .react-flow__handle {
  &:hover::after {
    content: "拖动连接到其他节点";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    white-space: nowrap;
  }
}
