.voice-flow-sidebar {
  // position: absolute;
  // top: 20px;
  // left: 20px;
  // width: 200px;
  // background: white;
  // padding: 20px;
  // border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  // z-index: 10;
  // border-right: 1px solid #eee;

  .voice-flow-sidebar-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .voice-flow-node {
    // padding: 10px;
    // border: 1px solid #ddd;
    // border-radius: 4px;
    // margin-bottom: 8px;
    cursor: move;
    background: transparent;
    text-align: center;

    // &:hover {
    //   background: #f0f0f0;
    //   border-color: #999;
    // }
  }
}
