// 话术管理
import { useState, useEffect } from "react"
import {
  Table,
  Button,
  Space,
  message,
  Input,
  Tooltip,
  Modal,
  Divider,
  Tag,
  Form,
  Select,
  Radio,
  Drawer,
  Popconfirm
} from "antd"
import {
  SearchOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EditOutlined
} from "@ant-design/icons"
import styles from "./script.module.scss"
import { useGetScriptListByPage } from "@/api/voiceAgent/index"
import { createOrUpdateScript, deleteScript } from "@/api/voiceAgent/api"
import { useLocation } from "react-router-dom"

export default function Script() {
  // 获取URL查询参数
  const location = useLocation()
  const queryString = new URLSearchParams(location.search)
  const botNoFromUrl = queryString.get("botNo")

  // 状态定义

  // 新增/编辑话术状态
  const [scriptDrawerVisible, setScriptDrawerVisible] = useState(false)
  const [scriptModalType, setScriptModalType] = useState("add") // 'add' 或 'edit'
  const [scriptForm] = Form.useForm()
  const [submitLoading, setSubmitLoading] = useState(false)
  const [currentScript, setCurrentScript] = useState(null)

  // 分页和搜索参数
  const [queryParams, setQueryParams] = useState({
    botNo: botNoFromUrl || "", // 从URL获取botNo
    pageSize: 10,
    pageNum: 1,
    orderColumn: "gmt_created",
    orderType: "desc"
  })

  // 当URL中的botNo变化时更新查询参数
  useEffect(() => {
    if (botNoFromUrl) {
      setQueryParams((prev) => ({
        ...prev,
        botNo: botNoFromUrl
      }))
    } else {
      message.error("缺少必要参数botNo，请检查URL")
    }
  }, [botNoFromUrl])

  // 使用自定义钩子获取话术列表数据
  const { data, isLoading, refetch } = useGetScriptListByPage(queryParams, {
    enabled: !!botNoFromUrl // 只有当有botNo时才发起请求
  })

  const dataSource = data?.data?.list || []
  const total = data?.data?.total || 0

  // 表格列定义
  const columns = [
    {
      title: "话术ID",
      dataIndex: "id",
      key: "id",
      width: 100,
      ...getColumnSearchProps("id", "话术ID")
    },
    {
      title: "话术名称",
      dataIndex: "name",
      key: "name",
      width: 180,
      render: (text) => text || "--",
      ...getColumnSearchProps("name", "话术名称")
    },
    {
      title: "是否有变量",
      dataIndex: "variable",
      key: "variable",
      width: 120,
      render: (variable) => (
        <Tag color={variable === 1 ? "green" : "red"}>{variable === 1 ? "有" : "无"}</Tag>
      ),
      filters: [
        { text: "有", value: 1 },
        { text: "无", value: 0 }
      ],
      onFilter: (value, record) => record.variable === value
    },
    {
      title: "话术文案",
      dataIndex: "content",
      key: "content",
      width: 300,
      render: (text) => (
        <Tooltip title={text}>
          <div className={styles.ellipsisText}>{text}</div>
        </Tooltip>
      ),
      ...getColumnSearchProps("content", "话术文案")
    },
    {
      title: "创建信息",
      dataIndex: "gmtCreated",
      key: "gmtCreated",
      width: 200,
      sorter: true, // 添加排序功能
      defaultSortOrder: "descend", // 默认降序排列（最新的在前）
      render: (_, record) => (
        <div>
          {record.creator || "--"}
          <br />
          {record.gmtCreated || "--"}
        </div>
      )
    },
    {
      title: "更新信息",
      dataIndex: "gmtModified",
      key: "gmtModified",
      width: 200,
      sorter: true, // 添加排序功能
      render: (_, record) => (
        <div>
          {record.modifier || "--"}
          <br />
          {record.gmtModified || "--"}
        </div>
      )
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="是否删除该条数据？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </div>
      )
    }
  ]

  // 搜索字段处理函数
  function getColumnSearchProps(dataIndex, placeholder) {
    return {
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={`搜索${placeholder}`}
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
            style={{ marginBottom: 8, display: "block" }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => handleReset(clearFilters, dataIndex)}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered) => (
        <SearchOutlined style={{ color: filtered ? "#1677ff" : undefined }} />
      ),
      onFilterDropdownOpenChange: (visible) => {
        if (visible) {
          setTimeout(() => {
            document.querySelector("input")?.focus()
          }, 100)
        }
      }
    }
  }

  // 搜索处理函数
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm()

    // 更新查询参数
    const newParams = {
      ...queryParams,
      pageNum: 1, // 重置到第一页
      [dataIndex]: selectedKeys[0]
    }

    setQueryParams(newParams)
  }

  // 重置搜索
  const handleReset = (clearFilters, dataIndex) => {
    clearFilters()

    // 重置特定字段的查询参数
    const newParams = { ...queryParams }
    delete newParams[dataIndex]

    setQueryParams(newParams)
  }

  // 表格分页变化处理
  const handleTableChange = (pagination, filters, sorter) => {
    const newParams = {
      ...queryParams,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }

    // 处理排序
    if (sorter.field && sorter.order) {
      // 根据排序字段设置对应的接口参数
      if (sorter.field === "gmtCreated") {
        newParams.orderColumn = "gmt_created" // 创建时间
      } else if (sorter.field === "gmtModified") {
        newParams.orderColumn = "gmt_modified" // 修改时间
      } else {
        newParams.orderColumn = "gmt_created" // 默认按创建时间
      }

      // 设置排序方向
      newParams.orderType = sorter.order === "ascend" ? "asc" : "desc"
    }

    // 处理筛选
    if (filters.variable && filters.variable.length > 0) {
      newParams.variable = filters.variable[0]
    } else {
      delete newParams.variable
    }

    setQueryParams(newParams)
  }

  // 处理编辑话术
  const handleEdit = (record) => {
    setScriptModalType("edit")
    setCurrentScript(record)

    // 设置表单初始值
    scriptForm.setFieldsValue({
      scriptId: record.id,
      code: record.code,
      name: record.name,
      variable: Number(record.variable), // 确保转换为数字类型
      content: record.content
    })

    setScriptDrawerVisible(true)
  }

  // 删除话术
  const handleDelete = async (record) => {
    // 检查是否有botNo
    if (!botNoFromUrl) {
      message.error("缺少必要参数botNo，无法删除")
      return
    }

    try {
      const params = {
        botNo: botNoFromUrl,
        scriptId: record.id
      }

      // 调用删除接口
      const res = await deleteScript(params)

      if (res && res.status === 200) {
        message.success("删除成功")
        // 刷新列表
        refetch()
      } else {
        message.error(res?.message || "删除失败")
      }
    } catch (error) {
      console.error("删除失败:", error)
      message.error("删除失败，请稍后重试")
    }
  }

  // 创建话术
  const handleCreate = () => {
    if (!botNoFromUrl) {
      message.error("缺少必要参数botNo，无法创建话术")
      return
    }

    setScriptModalType("add")
    setCurrentScript(null)

    // 重置表单
    scriptForm.resetFields()
    // 设置默认值
    scriptForm.setFieldsValue({
      variable: 0 // 默认无变量
    })

    setScriptDrawerVisible(true)
  }

  // 处理新增/编辑话术提交
  const handleScriptSubmit = async () => {
    // 检查是否有botNo
    if (!botNoFromUrl) {
      message.error("缺少必要参数botNo，无法提交")
      return
    }

    try {
      // 表单验证
      const values = await scriptForm.validateFields()

      setSubmitLoading(true)

      // 构建请求参数
      const params = {
        botNo: botNoFromUrl || queryParams.botNo, // 优先使用URL中的botNo
        name: values.name,
        variable: values.variable,
        content: values.content
      }

      // 编辑模式需要添加scriptId
      if (scriptModalType === "edit" && values.scriptId) {
        params.scriptId = values.scriptId
      }

      // 调用API
      const res = await createOrUpdateScript(params)

      if (res && res.status === 200) {
        message.success(scriptModalType === "add" ? "创建成功" : "更新成功")
        setScriptDrawerVisible(false)
        refetch() // 刷新列表
      } else {
        message.error(res?.message || (scriptModalType === "add" ? "创建失败" : "更新失败"))
      }
    } catch (error) {
      console.error("表单验证失败:", error)
    } finally {
      setSubmitLoading(false)
    }
  }

  // 导入话术
  const handleImport = () => {
    message.info("导入话术功能待实现")
  }

  // 导出话术
  const handleExport = () => {
    message.info("导出话术功能待实现")
  }

  return (
    <div className={styles.scriptContainer}>
      <div className={styles.header}>
        <div className={styles.leftButtons}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            创建话术
          </Button>
          {/* <div className={styles.importExportGroup}>
            <Button icon={<UploadOutlined />} onClick={handleImport}>
              导入
            </Button>
            <Divider type="vertical" />
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button>
          </div> */}
        </div>
        <div className={styles.rightButtons}>
          <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
            刷新
          </Button>
        </div>
      </div>

      <Table
        className="table-style-v2"
        rowClassName={(record, index) => {
          if (index % 2 === 0) {
            return "table-style-v2-even-row"
          } else {
            return "table-style-v2-odd-row"
          }
        }}
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        scroll={{ y: "calc(100vh - 220px)", x: 900 }}
        loading={isLoading}
        onChange={handleTableChange}
        pagination={{
          current: queryParams.pageNum,
          pageSize: queryParams.pageSize,
          total: total,
          showTotal: (total) => `共${total}条`,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50"],
          onChange: (page, pageSize) => {
            setQueryParams({
              ...queryParams,
              pageNum: page,
              pageSize
            })
          }
        }}
      />

      {/* 新增/编辑话术抽屉 */}
      <Drawer
        title={scriptModalType === "add" ? "创建话术" : "编辑话术"}
        open={scriptDrawerVisible}
        onClose={() => setScriptDrawerVisible(false)}
        width={500}
        footer={
          <div style={{ textAlign: "right" }}>
            <Space>
              <Button onClick={() => setScriptDrawerVisible(false)}>取消</Button>
              <Button type="primary" onClick={handleScriptSubmit} loading={submitLoading}>
                保存
              </Button>
            </Space>
          </div>
        }
      >
        <Form form={scriptForm} layout="vertical" initialValues={{ variable: 0 }}>
          {scriptModalType === "edit" && (
            <Form.Item name="scriptId" label="话术ID" hidden>
              <Input />
            </Form.Item>
          )}

          <Form.Item
            name="name"
            label="话术名称"
            rules={[{ required: true, message: "请输入话术名称" }]}
          >
            <Input placeholder="请输入话术名称" maxLength={50} />
          </Form.Item>

          <Form.Item
            name="variable"
            label="是否有变量"
            rules={[{ required: true, message: "请选择是否有变量" }]}
            className={styles.customRadioGroup}
          >
            <Radio.Group>
              <Radio value={1}>有</Radio>
              <Radio value={0}>无</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="content"
            label="话术内容"
            rules={[{ required: true, message: "请输入话术内容" }]}
          >
            <Input.TextArea
              placeholder="请输入话术内容"
              autoSize={{ minRows: 9, maxRows: 15 }}
              showCount
            />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  )
}
