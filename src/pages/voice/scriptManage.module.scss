.scriptManageContainer {
  padding: 24px;
  background-color: #fff;
  max-height: calc(100vh - 0px);
  overflow: hidden;

  .stepsContainer {
    width: 800px;
    margin: 20px auto;
  }

  .addButtonContainer {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .footerActions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    position: fixed;
    right: 0;
    bottom: 0;
    background-color: #fff;
    padding: 16px 20px;
    width: 100%;
    background: #fff;
    box-shadow: 0px 0px 4px 0px rgba(14, 18, 27, 0.12);

    button {
      min-width: 80px;
    }
  }
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.tableContainer {
  margin-top: 16px;
}

.footerActions {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
}

.stepsContainer {
  margin-bottom: 24px;
}
