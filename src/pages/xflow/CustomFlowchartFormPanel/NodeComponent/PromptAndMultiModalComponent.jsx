import { useMemo, useState, useEffect } from "react"
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Divider,
  Slider,
  Tabs,
  Switch,
  Tag,
  Radio,
  Tooltip,
  InputNumber
} from "antd"
import { DndProvider } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import { OutputParameter } from "@/pages/xflow/CustomFlowchartFormPanel/NodeComponent/components/OutputParameter"
import { parseTypes, parseModalTypeTagColor, marks } from "@/constants"
import { useNode } from "@/pages/xflow/CustomFlowchartFormPanel/NodeComponent/hooks/useNodeOutput"
import DynamicFormComponent from "@/pages/xflow/CustomFlowchartFormPanel/NodeComponent/components/DynamicFormComponent"
import "@/pages/xflow/CustomFlowchartFormPanel/NodeComponent/index.scss"
import {
  useFetchLlmFilterModelType,
  useFetchLlmModelType,
  useFetchOutputType,
  useFetchCodeModePermission,
  useFetchLlmReasoningEffort
} from "@/api/common"
import { useFetchGlobalVariable } from "@/api/skill"
import { useFormData } from "../../hooks/useInputFormData"
import React from "react"
import { useNodeUpdate } from "../../hooks/useNodeUpdate"
import useSaveShortcut from "../../hooks/useSaveShortcut"
import { PromptTips } from "@/constants/tips"
import VariableTextArea from "./components/VariableTextArea"
import { isFunction, uniq } from "lodash"
import { CommonContent } from "../CommonContent"
import { useComponentAndDebugPanel, useCurrentSkillLockInfo } from "@/store/index"
import useFormDisabled from "@/pages/xflow/hooks/useFormDisabled"
import { moveItem } from "@/api/tools"
import PreJudgment from "./components/PreJudgment"
import { formatSessionParams } from "./utils"
import DynamicFormList from "./DynamicFormList"
import { useCustomVariableType } from "../../hooks"
import GlobalVariableSelect from "@/components/GlobalVariableSelect"
// import SecurityPolicySwitch from "@/components/SecurityPolicySwitch"
import { MinusCircleOutlined } from "@ant-design/icons"
import FallbackHandler from "./FallbackHandler"
import Iconfont from "@/components/Icon"
import CustomDivider from "@/components/CustomDivider"
import { AddIcon, DeleteIcon, InfoIcon } from "@/components/FormIcon"
import { PlusOutlined } from "@ant-design/icons"
import AIOptimize from "@/components/AIOptimize"
import AdvanceSetting from "@/pages/xflow/CustomFlowchartFormPanel/NodeComponent/AdvanceSetting"
import QuickMode from "./components/QuickMode"
import ProfessionalMode from "./components/ProfessionalMode"
import CodeMode from "./components/CodeMode"
const { TabPane } = Tabs

// Memo
const NodeComponent = React.memo(
  // @ts-ignore
  ({ targetData, appData, commandService }) => {
    const { form } = useFormData()
    const [_, forceUpdate] = useState({})
    const [debugData, setDebugData] = useState([])
    const [forceShowDebugButton, setForceShowDebugButton] = useState(false)
    const [isOnlyMultiModal, setIsOnlyMultiModal] = useState(true)
    const [multiModalTypeList, setMultiModalTypeList] = useState([])
    const [supportReasoning, setSupportReasoning] = useState(false)
    const [getAdvanceModel, setAdvanceModel] = useState(false)
    const [promptMode, setPromptMode] = useState("BASIC")
    const [multiModalWarning, setMultiModalWarning] = useState("")

    // Use Form.useWatch to monitor content field
    const contentValue = Form.useWatch("content", form)
    const scriptValue = Form.useWatch("script", form)

    const { parseMethod, outputs, handleParseMethodChange, addOutput, deleteOutput } = useNode(
      form,
      targetData
    )

    const { data: varOptions = [] } = useCustomVariableType()

    const { updateNodeComp, skillFlowData, isLoading } = useNodeUpdate(commandService, appData)

    const { data: globalData = [] } = useFetchGlobalVariable(skillFlowData?.versionNo)

    const { data: reasoningEffortOptions = [] } = useFetchLlmReasoningEffort()

    const moveOutput = (index, direction) => {
      form.setFieldsValue({
        outputParams: moveItem(form.getFieldValue("outputParams"), index, direction)
      })
    }

    const onFinish = (callback = () => {}, noMessage = false) => {
      const callbackFunc = isFunction(callback) ? callback : () => {}
      return form.validateFields().then((values) => {
        const {
          outputParams,
          advancedSettingEnable,
          topKEnable,
          topPEnable,
          maxTokenEnable,
          seedEnable,
          topK,
          topP,
          maxToken,
          seed,
          promptMode,
          quickModeInputs,
          professionalContents,
          systemMessage,
          script,
          codeInputParams,
          reasoningEffort,
          ...rest
        } = values

        // 处理 inputParams - 只有快速模式并且有输入项时才处理
        let inputParams = []
        if (promptMode === "BASIC" && quickModeInputs) {
          inputParams = quickModeInputs
            ?.map(({ variable, modalType }) => {
              if (variable && modalType) {
                return {
                  variableName: variable,
                  variableValueType: "string",
                  variableRequire: false,
                  tag: {
                    modalType: modalType.toUpperCase() // 确保大写
                  }
                }
              }
            })
            .filter((item) => item)
        } else if (promptMode === "CODE" && codeInputParams) {
          // 处理代码模式的输入参数
          inputParams = codeInputParams
            ?.map((param, index) => {
              if (param.variableName && param.variableValueType) {
                const result = {
                  variableName: param.variableName,
                  variableValueType: param.variableValueType,
                  variableRequire: param.variableRequire || false,
                  description: param.description || "",
                  inputValue: param.inputValue, // 保留输入值
                  valueExpression: param.valueExpression // 保留取值表达式
                }
                return result
              } else {
                return null
              }
            })
            .filter((item) => item)
        } else if (values.inputItems) {
          // 兼容旧的 inputItems 结构
          inputParams = values.inputItems
            ?.map(({ variableName, modalType }) => {
              if (variableName && modalType) {
                return {
                  variableName,
                  variableValueType: "string",
                  variableRequire: false,
                  tag: {
                    modalType
                  }
                }
              }
            })
            .filter((item) => item)
        }

        if (inputParams?.length < 1 && rest.inputItems) {
          rest.inputItems = []
        }

        // 处理专业模式的消息结构
        let professionalModeData = null
        if (promptMode === "PROFESSIONAL") {
          const messages = []

          // 添加系统消息
          if (systemMessage) {
            messages.push({
              role: "system",
              enableMultiModal: false,
              content: systemMessage
            })
          }

          // 处理动态消息
          if (professionalContents && Array.isArray(professionalContents)) {
            professionalContents.forEach((msg) => {
              // 检查是否为禁用的多模态配置
              const isMultiModalDisabled = !!multiModalWarning && msg.enableImage

              if (
                msg.enableImage &&
                msg.imageInputs &&
                Array.isArray(msg.imageInputs) &&
                !isMultiModalDisabled
              ) {
                // 启用了图片且当前模型支持多模态的消息
                messages.push({
                  role: msg.type?.toLowerCase() || "user",
                  enableMultiModal: true,
                  modals: msg.imageInputs.map((input) => ({
                    type: input.modalType?.toUpperCase() || "IMAGE",
                    url: input.variable
                  }))
                })
              } else if (msg.content) {
                // 普通文本消息（包括原本的文本消息和被禁用的多模态消息转换为文本）
                messages.push({
                  role: msg.type?.toLowerCase() || "user",
                  enableMultiModal: false,
                  content: msg.content
                })
              } else if (isMultiModalDisabled && !msg.content) {
                // 如果是禁用的多模态消息且没有文本内容，跳过此消息
                // 这种情况下不添加任何消息到数组中
                console.log("跳过禁用的多模态消息:", msg)
              }
            })
          }

          professionalModeData = messages
        }

        // 处理代码模式的数据结构 - script与professionalContents平级
        if (promptMode === "CODE") {
          professionalModeData = [] // 代码模式时传空数组
        }

        const session = formatSessionParams(globalData, values.sessions)

        // 构建高级设置参数
        const advancedSettings = {
          advancedSettingEnable: advancedSettingEnable || false,
          topKEnable: topKEnable || false,
          topPEnable: topPEnable || false,
          maxTokenEnable: maxTokenEnable || false,
          seedEnable: seedEnable || false
        }

        // 如果所有子开关都关闭，则自动关闭高级设置主开关
        const hasAnySubToggleEnabled =
          advancedSettings.topKEnable ||
          advancedSettings.topPEnable ||
          advancedSettings.maxTokenEnable ||
          advancedSettings.seedEnable

        if (!hasAnySubToggleEnabled) {
          advancedSettings.advancedSettingEnable = false
        }

        // 传递具体值（只有在对应开关启用时才传递）
        if (topKEnable && topK !== undefined) {
          advancedSettings.topK = Number(topK)
        }
        if (topPEnable && topP !== undefined) {
          advancedSettings.topP = Number(topP)
          // 兼容原来的字段名
          advancedSettings.top_p = Number(topP)
        }
        if (maxTokenEnable && maxToken !== undefined) {
          advancedSettings.maxToken = Number(maxToken)
        }
        if (seedEnable && seed !== undefined) {
          // seed 可能是流程变量名（字符串）或数字，保持原始类型
          advancedSettings.seed = seed
        }

        const formattedValues = {
          ...rest,
          ...advancedSettings,
          outputParams,
          session, // 提交参数使用
          promptMode,
          reasoningEffort // 添加推理程度字段
        }

        // 根据模式添加对应的数据
        if (promptMode === "PROFESSIONAL" && professionalModeData) {
          formattedValues.professionalContents = professionalModeData
        } else if (promptMode === "CODE") {
          // 代码模式：script与professionalContents平级
          formattedValues.professionalContents = [] // 空数组
          formattedValues.script = script || "" // 确保script字段总是存在
        }

        updateNodeComp(
          {
            ...targetData,
            ...formattedValues,
            inputParams,
            globalDataOptions: globalData
          },
          callbackFunc,
          noMessage
        )
        forceUpdate({})
      })
    }

    const { isLocked } = useCurrentSkillLockInfo((state) => state.currentSkillLockInfo)
    const [isDisabled] = useFormDisabled()

    // const { data: modelOptions = [] } = useFetchLlmModelType()
    const { data: modelOptions = [] } = useFetchLlmFilterModelType(skillFlowData?.botNo)

    const { data: outputType = [] } = useFetchOutputType("PROMPT_TEMPLATE")
    const { data: codeModePermission = false } = useFetchCodeModePermission()
    useSaveShortcut(onFinish, isLoading)

    useEffect(() => {
      // 初始化处理
      if (modelOptions.length > 0 && targetData.modelType) {
        const item = modelOptions.find((item) => item.code === targetData.modelType)
        if (item) {
          const isOnlyMultiModal =
            item.modalType?.filter((m) => {
              return m.code !== "TEXT"
            }).length === item.modalType?.length
          setIsOnlyMultiModal(isOnlyMultiModal)

          const multiModalTypeList = item?.modalType?.filter((m) => {
            return m.code !== "TEXT"
          })

          // 如果有多模态能力
          if (multiModalTypeList?.length) {
            setMultiModalTypeList(item.modalType)
          }

          // 设置是否支持推理过程
          setSupportReasoning(item.supportReasoning ?? false)
        }
      }
    }, [modelOptions, targetData.modelType])

    const onLlmModelTypeChange = (value) => {
      const item = modelOptions.find((item) => item.code === value)
      if (item) {
        // 检查当前是否有多模态能力
        const wasMultiModal = multiModalTypeList && multiModalTypeList.length > 0

        // 如果非文本类型的长度和实际的长度一致，那么就是仅仅是多模态
        const isOnlyMultiModal =
          item.modalType?.filter((m) => {
            return m.code !== "TEXT"
          }).length === item.modalType?.length
        setIsOnlyMultiModal(isOnlyMultiModal)

        const newMultiModalTypeList = item.modalType?.filter((m) => {
          return m.code !== "TEXT"
        })
        const newHasMultiModal = newMultiModalTypeList && newMultiModalTypeList.length > 0

        setMultiModalTypeList(newMultiModalTypeList ?? [])
        setSupportReasoning(item.supportReasoning ?? false)

        // 检测从多模态切换到非多模态
        if (wasMultiModal && !newHasMultiModal && promptMode === "PROFESSIONAL") {
          // 获取当前专业模式的数据
          const currentProfessionalContents = form.getFieldValue("professionalContents") || []

          // 检查是否有启用的图片开关，但不清除配置
          const hasEnabledImages = currentProfessionalContents.some(
            (content) => content.enableImage
          )

          if (hasEnabledImages) {
            // 设置警告信息，但保持配置不变
            setMultiModalWarning("模型已切换成纯文本模式，多模态不可以使用")
          } else {
            setMultiModalWarning("")
          }
        } else if (!wasMultiModal && newHasMultiModal && promptMode === "PROFESSIONAL") {
          // 从非多模态切换回多模态，清除警告
          setMultiModalWarning("")
        } else if (newHasMultiModal) {
          // 清除警告信息
          setMultiModalWarning("")
        }

        if (newHasMultiModal) {
          const inputItems = form.getFieldValue("inputItems")
          if (!inputItems?.length) {
            form.setFieldsValue({
              inputItems: [
                {
                  modalType: null,
                  variableName: null
                }
              ]
            })
          }
        }
      }
    }

    /**
     * 析出prompt里边的字段
     */
    function matchElements(content, globalObject) {
      // console.log("content =>", content, globalObject)
      let fieldNames = Object.keys(globalObject)
      const sortedArray = fieldNames.slice().sort((a, b) => b.length - a.length)
      const pattern = sortedArray
        .map((str) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")) // 转义需要转义的字符
        .join("|")
      const regex = new RegExp(pattern, "g")
      let match = content?.match(regex)

      const matchedFields = []
      if (match && Array.isArray(match)) {
        uniq(match).map((item) => {
          let fieldObject = globalObject[item]
          // console.log("content1111 =>", fieldObject)
          if (
            fieldObject &&
            fieldObject.hasOwnProperty("formControlFacade") &&
            fieldObject.formControlFacade &&
            fieldObject.formControlFacade.controlType
          ) {
            matchedFields.push({
              ...fieldObject.formControlFacade,
              title: fieldObject.formControlFacade.title || fieldObject.displayName,
              valueExpression: fieldObject.valueExpression,
              attributeName: fieldObject.valueExpression
            })
          } else {
            matchedFields.push({
              ...fieldObject,
              controlType: "textarea",
              attributeName: fieldObject?.valueExpression,
              title: fieldObject?.description || fieldObject?.displayName,
              placeholder: "请输入……"
            })
          }
        })
      }

      // console.log("content222 =>", matchedFields)
      return matchedFields
    }

    const globalDataMemo = useMemo(() => {
      const data = {}
      if (Array.isArray(globalData)) {
        globalData.forEach((item) => {
          data[item.displayName] = item
        })
      }
      return data
    }, [globalData])

    const onMouseBlur = () => {
      const content =
        promptMode === "PROFESSIONAL"
          ? form.getFieldValue("systemMessage") || ""
          : promptMode === "CODE"
            ? ""
            : form.getFieldValue("content") || ""
      if (content?.trim()) {
        setForceShowDebugButton(true)
        // TODO: 是否需要改变状态
      } else {
        setForceShowDebugButton(false)
      }
      const inputItems =
        promptMode === "PROFESSIONAL"
          ? form.getFieldValue("professionalContents") || []
          : promptMode === "CODE"
            ? form.getFieldValue("codeInputParams") || []
            : form.getFieldValue("quickModeInputs")?.length
              ? form.getFieldValue("quickModeInputs") || []
              : form.getFieldValue("inputParams") || [] //form.getFieldValue("inputParams") || []

      console.log("inputItems =>", promptMode, form.getFieldValue(), inputItems)

      const inputItemsStr = inputItems
        .map(
          (item) =>
            (promptMode === "PROFESSIONAL" && !item?.imageInputs) || !item?.imageInputs?.length
              ? item.content
              : promptMode === "PROFESSIONAL" && item?.imageInputs && item?.imageInputs?.length
                ? item?.imageInputs?.map((image) => image.variable)
                : promptMode === "CODE"
                  ? item.valueExpression
                  : item.variable || item.variableName //item.variableName
        )
        .join(",")

      const str = `${content}${inputItemsStr}`

      console.log("inputItems32222 =>", str, inputItemsStr)

      const fieldList = matchElements(str, globalDataMemo)
      setDebugData(fieldList)
    }

    useEffect(() => {
      onMouseBlur()
    }, [targetData.content, globalData, promptMode])

    // 初始化高级设置表单数据
    useEffect(() => {
      if (targetData) {
        // 基础数据初始化
        const baseFields = {
          promptMode: targetData.promptMode || "BASIC"
        }

        // 只有当后端明确返回 advancedSettingEnable 为 true 时，才打开高级设置
        const shouldEnableAdvanced = targetData.advancedSettingEnable === true

        const advancedSettingsFields = {
          advancedSettingEnable: shouldEnableAdvanced,
          topKEnable: targetData.topKEnable || false,
          topPEnable: targetData.topPEnable || false,
          maxTokenEnable: targetData.maxTokenEnable || false,
          seedEnable: targetData.seedEnable || false
        }

        // 设置具体的数值（只有在对应开关启用时才恢复具体数值）
        if (targetData.topKEnable && targetData.topK !== undefined) {
          advancedSettingsFields.topK = targetData.topK
        }
        // 优先使用新字段名，兼容旧字段名
        if (targetData.topPEnable) {
          if (targetData.topP !== undefined) {
            advancedSettingsFields.topP = targetData.topP
          } else if (targetData.top_p !== undefined) {
            advancedSettingsFields.topP = targetData.top_p
          }
        }
        if (targetData.maxTokenEnable && targetData.maxToken !== undefined) {
          advancedSettingsFields.maxToken = targetData.maxToken
        }
        if (targetData.seedEnable && targetData.seed !== undefined) {
          advancedSettingsFields.seed = targetData.seed
        }

        // 处理推理程度字段的兼容性转换
        let reasoningEffortFields = {}
        if (targetData.reasoningEffort) {
          // 如果有新字段，直接使用
          reasoningEffortFields.reasoningEffort = targetData.reasoningEffort
        } else if (targetData.appendReasoning === true) {
          // 如果原来的开关是打开状态，默认选择高
          reasoningEffortFields.reasoningEffort = "high"
        } else {
          // 否则默认选择低
          reasoningEffortFields.reasoningEffort = "low"
        }

        // 初始化快速模式和专业模式的数据
        let quickModeFields = {}
        let professionalModeFields = {}
        let codeModeFields = {}

        if (promptMode === "BASIC") {
          //  targetData.promptMode === "BASIC"
          // 快速模式：从 inputParams 恢复 quickModeInputs
          if (targetData.inputParams && Array.isArray(targetData.inputParams)) {
            quickModeFields.quickModeInputs = targetData.inputParams.map((param) => ({
              modalType: param.tag?.modalType || param.modalType,
              variable: param.variableName
            }))
          }
        } else if (promptMode === "PROFESSIONAL") {
          //targetData.promptMode === "PROFESSIONAL"
          // 专业模式：从 professionalContents 恢复数据
          if (targetData.professionalContents && Array.isArray(targetData.professionalContents)) {
            // 分离系统消息和其他消息
            const systemMessage = targetData.professionalContents.find(
              (msg) => msg.role === "system"
            )
            const otherMessages = targetData.professionalContents.filter(
              (msg) => msg.role !== "system"
            )

            if (systemMessage) {
              professionalModeFields.systemMessage = systemMessage.content
            }

            // 处理其他消息
            const transformedMessages = otherMessages.map((msg) => {
              if (msg.enableMultiModal && msg.modals) {
                return {
                  type: msg.role?.toUpperCase() || "USER",
                  enableImage: true,
                  imageInputs: msg.modals.map((modal) => ({
                    modalType: modal.type,
                    variable: modal.url
                  }))
                }
              } else {
                return {
                  type: msg.role?.toUpperCase() || "USER",
                  enableImage: false,
                  content: msg.content
                }
              }
            })

            if (transformedMessages.length > 0) {
              professionalModeFields.professionalContents = transformedMessages
            }
          }
        } else if (promptMode === "CODE") {
          // targetData.promptMode === "CODE"
          // 代码模式：从 script 字段恢复脚本数据（新的数据结构）
          if (targetData.script) {
            codeModeFields.script = targetData.script
          } else if (
            targetData.professionalContents &&
            Array.isArray(targetData.professionalContents)
          ) {
            // 兼容旧的数据结构：从 professionalContents 恢复脚本数据
            const scriptMessage = targetData.professionalContents.find(
              (msg) => msg.script !== undefined
            )
            if (scriptMessage) {
              codeModeFields.script = scriptMessage.script
            }
          }

          // 从 inputParams 恢复代码模式的输入参数
          if (targetData.inputParams && Array.isArray(targetData.inputParams)) {
            codeModeFields.codeInputParams = targetData.inputParams.map((param) => ({
              variableName: param.variableName,
              variableValueType: param.variableValueType,
              variableRequire: param.variableRequire || false,
              description: param.description || "",
              inputValue: param.inputValue
            }))
          }
        }

        // 合并所有字段
        const allFields = {
          ...baseFields,
          ...advancedSettingsFields,
          ...reasoningEffortFields,
          ...quickModeFields,
          ...professionalModeFields,
          ...codeModeFields
        }

        form.setFieldsValue(allFields)

        // 同步 promptMode 状态
        setPromptMode(targetData.promptMode || "BASIC")
      }
    }, [targetData, form])

    // 清除多模态警告当切换到快速模式或代码模式时
    useEffect(() => {
      if (promptMode === "BASIC" || promptMode === "CODE") {
        setMultiModalWarning("")
      }
    }, [promptMode])

    // 检查代码模式权限，如果当前是代码模式但没有权限，则切换到快速模式
    useEffect(() => {
      if (promptMode === "CODE" && !codeModePermission) {
        setPromptMode("BASIC")
        form.setFieldsValue({ promptMode: "BASIC" })
      }
    }, [codeModePermission, promptMode, form])

    return (
      <div className={`prompt-node-wrapper`}>
        <div className="base-node-comp">
          <Form form={form} onFinish={onFinish} disabled={isDisabled} layout="vertical">
            <CommonContent
              title={"Prompt组件"}
              containerClass="noPadding"
              isLoading={isLoading}
              disabled={isLocked}
              onFinish={onFinish}
            >
              <Tabs defaultActiveKey="1" type="line">
                <TabPane tab="组件设置" key="1" forceRender>
                  <PreJudgment form={form} />
                  <CustomDivider showTopLine={true}>基础设置</CustomDivider>
                  <Row>
                    <Col span={24}>
                      <Form.Item name="label" label="组件名" rules={[{ required: true }]}>
                        <Input placeholder="请输入组件名" />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="modelType"
                        label="模型类型"
                        rules={[{ required: true }]}
                        // initialValue={"GPT_3.5"}
                      >
                        <Select
                          placeholder="请选择模型类型"
                          onChange={onLlmModelTypeChange}
                          showSearch={true}
                        >
                          {modelOptions?.map((opt) => (
                            <Select.Option
                              key={opt.code}
                              value={opt.code}
                              disabled={opt.status === 0}
                            >
                              {opt.name}{" "}
                              {opt.modalType?.map((m) => {
                                return (
                                  <Tag bordered={false} color={parseModalTypeTagColor(m.code)}>
                                    {m.name}
                                  </Tag>
                                )
                              })}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    {supportReasoning && (
                      <Col span={24}>
                        <Form.Item
                          layout="horizontal"
                          label="推理程度"
                          labelCol={{
                            span: 4
                          }}
                          style={{ marginLeft: -2 }}
                          tooltip={{
                            icon: <InfoIcon />,
                            title:
                              "推理程度仅对推理模型生效。对部分国产模型此选项仅作为开关，【低】表示关闭推理，【中】和【高】均为开启推理（效果无差别）"
                          }}
                        >
                          <Form.Item noStyle name="reasoningEffort" initialValue="low">
                            <Radio.Group size="small">
                              {reasoningEffortOptions.map((option) => (
                                <Radio key={option.code} value={option.code}>
                                  {option.name}
                                </Radio>
                              ))}
                            </Radio.Group>
                          </Form.Item>
                        </Form.Item>
                      </Col>
                    )}
                    <Col span={24}>
                      <Form.Item
                        layout="horizontal"
                        labelCol={{
                          span: 2
                        }}
                        name="temperature"
                        label="温度"
                      >
                        <Row gutter={[0, 0]}>
                          <Col span={18}>
                            <Form.Item noStyle name="temperature" initialValue={0.7}>
                              <Slider max={2} step={0.01} marks={marks} defaultValue={0.7} />
                            </Form.Item>
                          </Col>

                          <Col span={5}>
                            <Form.Item className="mr-0 pr-0" name="temperature" initialValue={0.7}>
                              <InputNumber
                                style={{ marginLeft: "20px", width: "100%" }}
                                min={0}
                                max={2}
                                step={0.01}
                                precision={2}
                                defaultValue={0.7}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Form.Item>
                    </Col>

                    {/* <Col span={24} style={{ marginBottom: "16px" }}>
                      <span style={{ textIndent: "2px" }}>高级设置</span>
                      <span
                        onClick={() => {
                          setAdvanceModel(!getAdvanceModel)
                        }}
                        style={{ cursor: "pointer", color: "#7f56d9", marginLeft: "20px" }}
                      >
                        {getAdvanceModel ? "收起" : "展开"}
                      </span>
                    </Col>
                    {getAdvanceModel && (
                      <div className="advance_setting">
                        <Col span={24}>
                          <Row gutter={[0, 0]}>
                            <Col span={3}>
                              <Switch
                                size="small"
                                onChange={() => {
                                  forceUpdate({})
                                }}
                              />
                            </Col>
                            <Col span={5}>
                              <Form.Item
                                className="global-tips"
                                name="content"
                                label={"Top K"}
                                labelAlign={"right"}
                                labelCol={"3"}
                                tooltip={{
                                  icon: <InfoIcon />,
                                  title:
                                    "生成时，采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高"
                                }}
                              ></Form.Item>
                            </Col>
                            <Col span={11}>
                              <Slider max={99} min={0} step={1} defaultValue={0} />
                            </Col>
                            <Col span={5}>
                              <Form.Item className="mr-0 pr-0" name="temperature" initialValue={0}>
                                <InputNumber
                                  min={0}
                                  max={2}
                                  step={0.01}
                                  precision={2}
                                  defaultValue={0.7}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Col>
                        <Col span={24}>
                          <Row gutter={[0, 0]}>
                            <Col span={3}>
                              <Switch
                                size="small"
                                onChange={() => {
                                  forceUpdate({})
                                }}
                              />
                            </Col>
                            <Col span={5}>
                              <Form.Item
                                className="global-tips"
                                name="content"
                                label={"Top P"}
                                tooltip={{
                                  icon: <InfoIcon />,
                                  title:
                                    "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值越大，生成的随机性越高；取值越低，生成的确定性越高"
                                  // overlayStyle: { maxWidth: 400 }
                                }}
                              ></Form.Item>
                            </Col>
                            <Col span={11}>
                              <Slider max={0.9} min={0.01} step={0.01} defaultValue={0.7} />
                            </Col>
                            <Col span={5}>
                              <Form.Item
                                className="mr-0 pr-0"
                                name="temperature"
                                initialValue={0.7}
                              >
                                <InputNumber
                                  min={0}
                                  max={2}
                                  step={0.01}
                                  precision={2}
                                  defaultValue={0.7}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Col>
                        <Col span={24}>
                          <Row gutter={[0, 0]}>
                            <Col span={3}>
                              <Switch
                                size="small"
                                onChange={() => {
                                  forceUpdate({})
                                }}
                              />
                            </Col>
                            <Col span={5} style={{ textAlign: "left" }}>
                              <Form.Item
                                className="global-tips"
                                name="content"
                                label={"Max Token"}
                                tooltip={{
                                  icon: <InfoIcon />,
                                  title:
                                    "生成时使用的随机数种子，用户|控制模型生成内容的随机性。在使用seed时，模型将尽可能生成相同或相似的结果，但目前不保证每次生成的结果完全相同"
                                  // overlayStyle: { maxWidth: 400 }
                                }}
                              ></Form.Item>
                            </Col>
                            <Col span={16}>
                              <InputNumber
                                min={0}
                                max={2}
                                step={0.01}
                                style={{ width: "100%" }}
                                size="smill"
                                defaultValue={0.7}
                              />
                            </Col>
                          </Row>
                        </Col>
                        <Col span={24}>
                          <Row gutter={[0, 0]}>
                            <Col span={3}>
                              <Switch
                                size="small"
                                onChange={() => {
                                  forceUpdate({})
                                }}
                              />
                            </Col>
                            <Col span={5}>
                              <Form.Item
                                className="global-tips"
                                name="content"
                                label={"Seed"}
                                tooltip={{
                                  icon: <InfoIcon />,
                                  title:
                                    "用于指定模型在生成内容时token的最大效量，它定义了生成的上限，但不保证每次都会生成到这个数量"
                                  // overlayStyle: { maxWidth: 400 }
                                }}
                              ></Form.Item>
                            </Col>
                            <Col span={16}>
                              <Select
                                size={"smill"}
                                style={{ width: "100%" }}
                                defaultValue="a1"
                                // onChange={handleChange}
                                // options={options}
                              />
                            </Col>
                          </Row>
                        </Col>
                      </div>
                    )} */}
                    <AdvanceSetting />
                    <Divider className="!my-2 !mb-4" />
                    {/* 快速和专业模式切换 */}
                    <div>
                      <Form.Item name="promptMode" label="" initialValue="BASIC">
                        <Radio.Group
                          value={promptMode}
                          onChange={(e) => {
                            const newMode = e.target.value
                            setPromptMode(newMode)
                            form.setFieldsValue({ promptMode: newMode })
                          }}
                        >
                          <Radio value="BASIC">快速模式</Radio>
                          <Radio value="PROFESSIONAL">专业模式</Radio>
                          {codeModePermission && <Radio value="CODE">代码模式</Radio>}
                        </Radio.Group>
                      </Form.Item>
                    </div>

                    {/* 是否启用top_p参数 */}
                    {/* {getAdvanceModel && (
                      <Col span={24}>
                        <Form.Item
                          layout="horizontal"
                          label="是否启用top_p参数"
                          labelCol={{
                            span: 7
                          }}
                          style={{ marginLeft: -6 }}
                          tooltip={{
                            icon: <InfoIcon />,
                            title:
                              "生成过程中核采样方法概率阈值，例如，取值为0.8时，仅保留概率加起来大于等于0.8的最可能token的最小集合作为候选集。取值范围为 (0-1)，取值越大，生成的随机性越高；取值越低，生成的确定性越高"
                          }}
                        >
                          <Form.Item
                            noStyle
                            name="topPEnable"
                            valuePropName="checked"
                            initialValue={false}
                          >
                            <Switch
                              size="small"
                              onChange={() => {
                                forceUpdate({})
                              }}
                            />
                          </Form.Item>
                          <span className="judge-tip">取值越低，生成的确定性越高</span>
                        </Form.Item>
                      </Col>
                    )} */}
                    {/* {form.getFieldValue("topPEnable") && getAdvanceModel && ( */}
                    {/* {getAdvanceModel && (
                      <Col span={24} style={{ paddingLeft: "5px" }}>
                        <Form.Item
                          layout="horizontal"
                          labelCol={{ span: 2 }}
                          name="top_p"
                          label="top-p"
                        >
                          <Row gutter={[16, 0]}>
                            <Col span={3}>
                              <Switch size="small" onChange={() => {}} />
                            </Col>
                            <Col span={16}>
                              <Form.Item noStyle>
                                <Slider
                                  max={0.9}
                                  min={0.01}
                                  step={0.01}
                                  defaultValue={0.8}
                                  marks={{
                                    0.01: "0.01",
                                    0.8: "0.8",
                                    0.9: "0.9"
                                  }}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={3}>
                              <Form.Item noStyle name="top_p">
                                <InputNumber
                                  min={0.1}
                                  max={0.9}
                                  step={0.01}
                                  precision={2}
                                  defaultValue={0.8}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </Col>
                    )} */}
                  </Row>

                  <div className="mb-6">
                    {/* 根据模式显示不同的组件 */}
                    {promptMode === "BASIC" ? (
                      <QuickMode
                        form={form}
                        globalData={globalData}
                        skillFlowData={skillFlowData}
                        contentValue={contentValue}
                        onMouseBlur={onMouseBlur}
                        isDisabled={isDisabled}
                        multiModalTypeList={multiModalTypeList}
                        isOnlyMultiModal={isOnlyMultiModal}
                      />
                    ) : promptMode === "PROFESSIONAL" ? (
                      <ProfessionalMode
                        form={form}
                        globalData={globalData}
                        skillFlowData={skillFlowData}
                        onMouseBlur={onMouseBlur}
                        isDisabled={isDisabled}
                        multiModalTypeList={multiModalTypeList}
                        isOnlyMultiModal={isOnlyMultiModal}
                        multiModalWarning={multiModalWarning}
                      />
                    ) : (
                      <CodeMode
                        form={form}
                        globalData={globalData}
                        skillFlowData={skillFlowData}
                        contentValue={scriptValue}
                        onMouseBlur={onMouseBlur}
                        isDisabled={isDisabled}
                        targetData={targetData}
                      />
                    )}
                  </div>

                  <Divider />

                  {/* 输出参数部分 */}
                  <CustomDivider showTopLine={false}>输出参数</CustomDivider>
                  <Row gutter={[24, 8]}>
                    <Col span={12}>
                      <Form.Item
                        layout="vertical"
                        name="parseMethod"
                        label="解析方式"
                        className="mb-2"
                        rules={[
                          {
                            required: true,
                            message: "请选择解析方式"
                          }
                        ]}
                      >
                        <Select placeholder="请选择解析方式" onChange={handleParseMethodChange}>
                          {outputType.map((method, i) => (
                            <Select.Option key={i} value={method.code}>
                              {method.name}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        layout="vertical"
                        name="outputName"
                        label="输出变量名"
                        className="mb-2"
                        rules={[
                          {
                            required: true,
                            message: "请输入"
                          }
                        ]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <DndProvider backend={HTML5Backend}>
                    {(parseMethod === "JSON" || parseMethod === "JSON_ARRAY") && (
                      <>
                        {outputs.map((output, index) => (
                          <OutputParameter
                            allowDelete={outputs.length > 1}
                            key={index}
                            index={index}
                            id={output.id}
                            parseMethod={parseMethod}
                            deleteOutput={deleteOutput}
                            addOutput={addOutput}
                            parseTypes={parseTypes}
                            moveOutput={moveOutput}
                            isFirst={index === 0}
                            isLast={index === outputs.length - 1}
                            form={form}
                          />
                        ))}
                        <Row>
                          <Col span={24}>
                            {!isDisabled && (
                              <Button
                                type="link"
                                onClick={addOutput}
                                style={{
                                  paddingLeft: 24,
                                  marginTop: 8
                                }}
                                icon={<PlusOutlined />}
                              >
                                添加
                              </Button>
                            )}
                          </Col>
                        </Row>
                      </>
                    )}
                  </DndProvider>
                </TabPane>
                <TabPane tab="后置处理" key="2" forceRender>
                  <DynamicFormList form={form} varOptions={varOptions} />
                </TabPane>
                <TabPane tab="兜底处理" key="3" forceRender>
                  <FallbackHandler
                    form={form}
                    varOptions={varOptions}
                    botNo={skillFlowData?.botNo}
                    skillNo={skillFlowData?.skillNo}
                    nodeType="prompt-node"
                  />
                </TabPane>
              </Tabs>
            </CommonContent>
          </Form>
        </div>
        <div className={`debug-panel`}>
          <DynamicFormComponent
            onFinish={onFinish}
            nodeId={targetData.id}
            preview={false}
            formData={debugData}
            isProcess={false}
            forceShowDebugButton={forceShowDebugButton}
          />
        </div>
      </div>
    )
  }
)

export default NodeComponent
