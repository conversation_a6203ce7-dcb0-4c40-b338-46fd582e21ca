import { useEffect, useState, useRef } from "react"
import {
  Form,
  Input,
  Row,
  Col,
  Button,
  Divider,
  Tabs,
  Select,
  Radio,
  Slider,
  InputNumber,
  message
} from "antd"
import { PlayCircleOutlined, PauseCircleOutlined } from "@ant-design/icons"
import GlobalVariableSelect from "@/components/GlobalVariableSelect"
import DynamicFormComponent from "./components/DynamicFormComponent"
import { useFormData } from "../../hooks/useInputFormData"
import { useFetchGlobalVariable } from "@/api/skill"
import { useNodeUpdate } from "../../hooks/useNodeUpdate"
import useSaveShortcut from "../../hooks/useSaveShortcut"
import { isFunction } from "lodash"
import { CommonContent } from "../CommonContent"
import { useCurrentSkillLockInfo } from "@/store/index"
import useFormDisabled from "@/pages/xflow/hooks/useFormDisabled"
import { useCustomVariableType } from "../../hooks"
import { formatSessionParams } from "./utils"
import PreJudgment from "./components/PreJudgment"
import DynamicFormList from "./DynamicFormList"
import FallbackHandler from "./FallbackHandler"
import { useFetchTTSAudioFormat } from "@/api/workBench"
import CustomDivider from "@/components/CustomDivider"
import { fetchPersonalTimbreListV2 } from "@/api/timbre/api"
import { synthesisVoice } from "@/api/voiceAgent/api"
import { useNavigate } from "react-router-dom"

const TabPane = Tabs.TabPane

const TTSComponent = ({ targetData, appData, commandService }) => {
  const navigate = useNavigate()
  const { form, formData } = useFormData()
  const [_, forceUpdate] = useState({})
  const [loading, setLoading] = useState(false)

  const { updateNodeComp, skillFlowData, isLoading } = useNodeUpdate(commandService, appData)
  const { data: globalData = [] } = useFetchGlobalVariable(skillFlowData?.versionNo)
  const { isLocked } = useCurrentSkillLockInfo((state) => state.currentSkillLockInfo)
  const [isDisabled] = useFormDisabled()

  const { data: audioFormatOptions, isLoading: isLoadingFormat } = useFetchTTSAudioFormat()

  // 移除mode, timbreCode, timbreId, audioFormat, sampleRate, speed, volume, synthesisText的useState
  // 只保留试听相关的useState
  const [audioUrl, setAudioUrl] = useState("")
  const [synthesizing, setSynthesizing] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioDuration, setAudioDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [progress, setProgress] = useState(0)
  const audioRef = useRef(null)

  const [timbreLoading, setTimbreLoading] = useState(false)
  const [timbreList, setTimbreList] = useState([])

  useEffect(() => {
    // 简化：只判断timbreId
    const timbreId = targetData?.timbreId
    const selectSound =
      timbreId && !targetData?.selectSound
        ? false
        : timbreId && targetData?.selectSound
          ? true
          : false
    form.setFieldsValue({
      ...targetData,
      selectSound,
      inputParams: targetData?.inputParams?.[0],
      audioFormat: targetData?.audioFormat || "wav"
    })
    forceUpdate({})
  }, [targetData, form])

  // 替换 fetchTimbreList
  const fetchTimbreList = async (search = "") => {
    setTimbreLoading(true)
    try {
      const list = await fetchPersonalTimbreListV2({
        botNo: skillFlowData?.botNo,
        timbreName: search
      })
      setTimbreList(list)
    } finally {
      setTimbreLoading(false)
    }
  }
  useEffect(() => {
    fetchTimbreList()
  }, [form, skillFlowData?.botNo])

  // 替换试听相关逻辑
  const handleSynthesis = async () => {
    const selectSound = form.getFieldValue("selectSound")
    const timbreId = form.getFieldValue("timbreId")
    const audioFormat = form.getFieldValue("audioFormat")
    const sampleRate = form.getFieldValue("sampleRate")
    const speed = form.getFieldValue("speed")
    const volume = form.getFieldValue("volume")
    const synthesisText = form.getFieldValue("synthesisText")
    const timbreCode = timbreId // 不管哪种模式都用timbreId
    if (!timbreCode) return message.warning("请选择或输入音色")
    if (!synthesisText) return message.warning("请输入要合成的文本")
    setSynthesizing(true)
    setAudioUrl("")
    try {
      const res = await synthesisVoice({
        botNo: skillFlowData?.botNo,
        content: synthesisText,
        timbreCode,
        audioFormat,
        sampleRate,
        speed,
        volume
      })
      if (res && res.status === 200 && res.data) {
        message.success("语音合成成功")
        setAudioUrl(res.data)
        setProgress(0)
        setCurrentTime(0)
        setIsPlaying(false)
      } else {
        message.error(res?.message || "语音合成失败")
      }
    } catch {
      message.error("语音合成失败")
    } finally {
      setSynthesizing(false)
    }
  }
  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying((v) => !v)
    }
  }
  const handleAudioEnded = () => {
    setIsPlaying(false)
    setProgress(100)
  }
  const formatTime = (time) => {
    if (isNaN(time) || time === 0) return "00:00"
    const m = Math.floor(time / 60)
    const s = Math.floor(time % 60)
    return `${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`
  }
  const handleProgressClick = (e) => {
    if (audioRef.current && audioDuration) {
      const bar = e.currentTarget
      const rect = bar.getBoundingClientRect()
      const offsetX = e.clientX - rect.left
      const newProgress = (offsetX / bar.offsetWidth) * 100
      const newTime = (newProgress / 100) * audioDuration
      audioRef.current.currentTime = newTime
      setProgress(newProgress)
      setCurrentTime(newTime)
    }
  }
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return
    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime)
      setProgress((audio.currentTime / (audioDuration || 1)) * 100)
    }
    const handleLoadedMetadata = () => {
      setAudioDuration(audio.duration)
    }
    audio.addEventListener("timeupdate", handleTimeUpdate)
    audio.addEventListener("loadedmetadata", handleLoadedMetadata)
    return () => {
      audio.removeEventListener("timeupdate", handleTimeUpdate)
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata)
    }
  }, [audioRef.current, audioDuration])

  const onFinish = (callback = () => {}, noMessage = false) => {
    return form.validateFields().then((values) => {
      const callbackFunc = isFunction(callback) ? callback : () => {}
      console.log("Received values of form: ", values)
      const session = formatSessionParams(globalData, values.sessions)
      const params = {
        ...values,
        inputParams: [values.inputParams],
        session
      }
      updateNodeComp(
        {
          ...targetData,
          ...params,
          globalDataOptions: globalData
        },
        callbackFunc,
        noMessage
      )
      forceUpdate({})
    })
  }

  useSaveShortcut(onFinish, isLoading)
  const { data: varOptions = [] } = useCustomVariableType()

  return (
    <div className="common-node-wrapper">
      <div className="base-node-comp ">
        <Form
          form={form}
          onFinish={onFinish}
          labelCol={{ span: 24 }}
          disabled={isDisabled}
          layout="vertical"
        >
          <CommonContent
            title={"TTS文转音组件"}
            containerClass="noPadding"
            onFinish={onFinish}
            isLoading={isLoading}
            disabled={isLocked}
          >
            <Tabs defaultActiveKey="1" type="line">
              <TabPane tab="组件设置" key="1" forceRender>
                <PreJudgment form={form} />
                <CustomDivider showTopLine={true}>基础设置</CustomDivider>
                <Row className="mt-3">
                  <Col span={24}>
                    <Form.Item
                      name="label"
                      label="组件名"
                      rules={[{ required: true, message: "请输入组件名" }]}
                    >
                      <Input placeholder="请输入组件名" />
                    </Form.Item>

                    <Form.Item
                      name="sceneId"
                      label="场景ID"
                      // rules={[{ required: false, message: "请输入场景ID" }]}
                    >
                      <Input placeholder="请输入场景ID" />
                    </Form.Item>
                  </Col>
                  {/* <Col span={24}>
                    <Form.Item
                      name="voiceId"
                      label="音色ID"
                      rules={[{ required: true, message: "请输入音色ID" }]}
                      help={
                        <div className="text-[12px]  bg-gray-100 rounded-md p-2 mt-1">
                          <p className="text-red-500 mb-[5px]">说明：</p>

                          <div>
                            请直接在输入框中填写或前往 maas 平台查看
                            <p>
                              <a
                                href="https://maas-test.zhonganonline.com"
                                target="_blank"
                                rel="noreferrer"
                              >
                                测试环境： https://maas-test.zhonganonline.com
                              </a>
                              <br />
                              <a
                                href="https://maas-pre.zhonganonline.com"
                                target="_blank"
                                rel="noreferrer"
                              >
                                预发环境： https://maas-pre.zhonganonline.com
                              </a>
                              <br />
                              <a
                                href="https://maas.zhonganonline.com"
                                target="_blank"
                                rel="noreferrer"
                              >
                                生产环境： https://maas.zhonganonline.com
                              </a>
                            </p>
                          </div>
                        </div>
                      }
                    >
                      <Input placeholder="请输入音色ID" />
                    </Form.Item>
                  </Col> */}

                  {/* 声音设置 */}
                  <Col span={24}>
                    <CustomDivider showTopLine={true}>声音设置</CustomDivider>
                    <Form.Item
                      label="音色创建方式"
                      name="selectSound"
                      required
                      initialValue={false}
                    >
                      <Radio.Group
                        onChange={(e) => {
                          form.setFieldsValue({
                            timbreId: undefined
                          })
                        }}
                      >
                        <Radio value={true}>选择音色创建</Radio>
                        <Radio value={false}>音色ID创建</Radio>
                      </Radio.Group>
                    </Form.Item>
                    <Form.Item shouldUpdate>
                      {() =>
                        form.getFieldValue("selectSound") ? (
                          // 选择音色创建
                          <>
                            <Form.Item label="音色" name="timbreId" required>
                              <Select
                                showSearch
                                placeholder="请选择音色"
                                loading={timbreLoading}
                                // onSearch 移除，改为前端过滤
                                filterOption={(input, option) =>
                                  (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
                                }
                                options={timbreList
                                  ?.filter((item) => item.enabled === "Y")
                                  .map((item) => ({
                                    label: item.timbreName,
                                    value: item.timbreCode
                                  }))}
                                allowClear
                              />
                            </Form.Item>
                            <Row gutter={16}>
                              <Col span={12}>
                                {/* <Form.Item
                                  label="录音格式"
                                  name="audioFormat"
                                  required
                                  initialValue="wav"
                                >
                                  <Select
                                    options={[
                                      { label: "mp3", value: "mp3" },
                                      { label: "wav", value: "wav" }
                                    ]}
                                  />
                                </Form.Item> */}
                                <Form.Item
                                  name="audioFormat"
                                  label="音频格式"
                                  rules={[{ required: true, message: "请选择音频格式" }]}
                                >
                                  <Select
                                    placeholder="请选择音频格式"
                                    loading={isLoadingFormat}
                                    options={audioFormatOptions?.map((item) => ({
                                      label: item.name,
                                      value: item.code
                                    }))}
                                    showSearch
                                    defaultValue="wav"
                                    filterOption={(input, option) =>
                                      // @ts-ignore
                                      (option?.label ?? "")
                                        ?.toLowerCase()
                                        .includes(input.toLowerCase())
                                    }
                                  />
                                </Form.Item>
                              </Col>
                              <Col span={12}>
                                <Form.Item
                                  label="采样频率"
                                  name="sampleRate"
                                  required
                                  initialValue={16000}
                                >
                                  <Select
                                    options={[
                                      { label: "8k", value: 8000 },
                                      { label: "16k", value: 16000 },
                                      { label: "24k", value: 24000 }
                                    ]}
                                  />
                                </Form.Item>
                              </Col>
                            </Row>
                            <Row gutter={16}>
                              <Col span={12}>
                                <Form.Item
                                  label="音频速率"
                                  name="speed"
                                  required
                                  initialValue={1.05}
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="flex-1">
                                      <Slider
                                        min={0.6}
                                        max={2.5}
                                        step={0.01}
                                        value={form.getFieldValue("speed")}
                                        onChange={(val) => form.setFieldsValue({ speed: val })}
                                        tooltip={{ formatter: (value) => `${value}` }}
                                      />
                                    </div>
                                    <InputNumber
                                      min={0.6}
                                      max={2.5}
                                      step={0.01}
                                      precision={2}
                                      className="w-20"
                                      value={form.getFieldValue("speed")}
                                      onChange={(val) => form.setFieldsValue({ speed: val })}
                                    />
                                  </div>
                                </Form.Item>
                              </Col>
                              <Col span={12}>
                                <Form.Item label="音量" name="volume" required initialValue={50}>
                                  <div className="flex items-center gap-3">
                                    <div className="flex-1">
                                      <Slider
                                        min={0}
                                        max={100}
                                        value={form.getFieldValue("volume")}
                                        onChange={(val) => form.setFieldsValue({ volume: val })}
                                        tooltip={{ formatter: (value) => `${value}%` }}
                                      />
                                    </div>
                                    <InputNumber
                                      min={0}
                                      max={100}
                                      precision={0}
                                      className="w-20"
                                      value={form.getFieldValue("volume")}
                                      onChange={(val) => form.setFieldsValue({ volume: val })}
                                      formatter={(value) => `${value}%`}
                                      parser={(value) => value?.replace("%", "")}
                                    />
                                  </div>
                                </Form.Item>
                              </Col>
                            </Row>
                            <Row gutter={16} className="mt-2">
                              <Col span={24}>
                                <Form.Item label="试听文本" name="synthesisText">
                                  <Input.TextArea placeholder="请输入要合成试听的文本" rows={3} />
                                </Form.Item>
                                <div className="text-left">
                                  <Button
                                    type="primary"
                                    icon={<i className="iconfont icon-zhinengyouhua"></i>}
                                    onClick={handleSynthesis}
                                    loading={synthesizing}
                                    style={{
                                      background:
                                        "linear-gradient(83.59deg, #E9E8FF 6.73%, #EEC7FF 131.73%)",
                                      color: "#7F56D9"
                                    }}
                                  >
                                    合成试听
                                  </Button>
                                </div>
                              </Col>
                            </Row>
                            {audioUrl && (
                              <div className="mt-3 p-3 border rounded-md bg-gray-100">
                                <div className="flex items-center mb-2">
                                  <div className="mr-2 flex items-center justify-center w-9 h-9 bg-orange-500 p-1 text-white rounded-md">
                                    <span className="text-xs font-bold">MP3</span>
                                  </div>
                                  <div className="flex-1 text-sm font-medium text-gray-700 truncate">
                                    已生成语音文件
                                  </div>
                                  {isPlaying ? (
                                    <PauseCircleOutlined
                                      className="text-2xl text-[#7F56D9] cursor-pointer"
                                      onClick={togglePlay}
                                    />
                                  ) : (
                                    <PlayCircleOutlined
                                      className="text-2xl text-[#7F56D9] cursor-pointer"
                                      onClick={togglePlay}
                                    />
                                  )}
                                </div>
                                <div className="flex items-center">
                                  <div className="text-xs text-gray-500 mr-2">
                                    {formatTime(currentTime)}
                                  </div>
                                  <div
                                    className="flex-1 bg-gray-200 h-1 rounded cursor-pointer relative overflow-hidden"
                                    onClick={handleProgressClick}
                                  >
                                    <div
                                      className="absolute h-full bg-[#7F56D9] rounded-lg"
                                      style={{ width: `${progress}%` }}
                                    ></div>
                                  </div>
                                  <div className="text-xs text-gray-500 ml-2">
                                    {formatTime(audioDuration)}
                                  </div>
                                </div>
                                <audio
                                  ref={audioRef}
                                  src={audioUrl}
                                  onEnded={handleAudioEnded}
                                  preload="metadata"
                                  className="hidden"
                                />
                              </div>
                            )}
                          </>
                        ) : (
                          // 音色ID创建
                          <>
                            <Form.Item label="音色ID" name="timbreId" required>
                              <Input placeholder="请输入音色ID" />
                            </Form.Item>
                            <Form.Item
                              name="audioFormat"
                              label="音频格式"
                              // rules={[{ required: true, message: "请选择音频格式" }]}
                            >
                              <Select
                                placeholder="请选择音频格式"
                                loading={isLoadingFormat}
                                options={audioFormatOptions?.map((item) => ({
                                  label: item.name,
                                  value: item.code
                                }))}
                                showSearch
                                defaultValue="wav"
                                filterOption={(input, option) =>
                                  // @ts-ignore
                                  (option?.label ?? "")?.toLowerCase().includes(input.toLowerCase())
                                }
                              />
                            </Form.Item>
                          </>
                        )
                      }
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <CustomDivider showTopLine={true}>输入参数</CustomDivider>
                  </Col>
                  <Col span={24}>
                    <GlobalVariableSelect
                      label={"输入"}
                      span={3}
                      formName="inputParams"
                      multiple={false}
                      className={undefined}
                      onChange={undefined}
                      style={undefined}
                      initialValue={undefined}
                      layout="vertical"
                    />
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={24}>
                    <CustomDivider showTopLine={true}>输出参数</CustomDivider>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="输出结果类型">
                      <Input value="字符串" disabled={true} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="outputName"
                      label="输出变量名"
                      rules={[{ required: true, message: "请输入" }]}
                    >
                      <Input placeholder="输出变量名" />
                    </Form.Item>
                  </Col>
                </Row>
              </TabPane>
              <TabPane tab="后置处理" key="2" forceRender>
                <DynamicFormList form={form} varOptions={varOptions} />
              </TabPane>
              <TabPane tab="兜底处理" key="3" forceRender>
                <FallbackHandler
                  form={form}
                  varOptions={varOptions}
                  botNo={skillFlowData?.botNo}
                  skillNo={skillFlowData?.skillNo}
                />
              </TabPane>
            </Tabs>
          </CommonContent>
        </Form>
        <div className="debug-panel">
          <DynamicFormComponent
            nodeId={targetData.id}
            preview={false}
            isProcess={false}
            formData={formData}
            onFinish={onFinish}
          />
        </div>
      </div>
    </div>
  )
}

export default TTSComponent
