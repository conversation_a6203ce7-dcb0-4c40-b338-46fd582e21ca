import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Pop<PERSON>, Table } from "antd"
import { useRef, useEffect, forwardRef, useImperativeHandle, useMemo, useState } from "react"
import DynamicFormItem from "@/components/DynamicFormItem"
import MarkdownRenderer from "@/components/MarkdownRenderer"
import "./index.scss"
import { useDebugHandler } from "../hooks/useDebugHandler"
import { useCell } from "@/pages/xflow/hooks/useCell"
import useFormDisabled from "@/pages/xflow/hooks/useFormDisabled"
import React from "react"
import JSONValidator from "@/components/JSONValidator"
import {
  useComponentAndDebugPanel,
  useCurrentSkillLockInfo,
  useHistoryDebugData
} from "@/store/index"
import UseDebugHistory from "@/pages/xflow/hooks/useDebugHistory"
import { useDebugAIGeneratingSkill } from "@/api/AICreate"
import { useQueryClient } from "@tanstack/react-query"
import { QUERY_KEYS } from "@/constants/queryKeys"
import "./../../CommonContent.scss"
import Iconfont from "@/components/Icon"
import TopDownButton from "@/components/TopDownButton"
import { InfoIcon } from "@/components/FormIcon"

const DynamicFormComponent = (
  {
    formData = [],
    preview,
    nodeId,
    isProcess,
    isJSONDebug = false,
    onFinish: save,
    skillFlowData = {},
    isAICreate = false,
    forceShowDebugButton = false,
    forceShowPanel = false,
    onClose = null,
    className = ""
  },
  ref
) => {
  const [form] = Form.useForm()
  const loadingRef = useRef(null)
  const { skillNo } = skillFlowData
  const [isDisabled] = useFormDisabled()
  const [isUploading, setIsUploading] = useState(false)

  const { debugContent, handleDebug, setDebugContent, loading } = useDebugHandler(nodeId)
  const { mutate: debugAIGeneratingSkill } = useDebugAIGeneratingSkill()
  const queryClient = useQueryClient()

  const { componentNo } = useCell(nodeId)
  const { isLocked } = useCurrentSkillLockInfo((state) => state.currentSkillLockInfo)

  const { componentAndDebugPanel, changeComponentAndDebugPanel } = useComponentAndDebugPanel(
    (state) => state
  )
  const showDebuggerPanel = useMemo(() => {
    return componentAndDebugPanel.showDebugPanel || forceShowPanel
  }, [componentAndDebugPanel, forceShowPanel])

  const setFormData = (formData) => {
    form.setFieldsValue(formData)
  }

  const skillNoOrComponentNo = skillNo || nodeId
  const { setDebugData, debugData } = useHistoryDebugData((state) => state)
  const { DebugPopoverButton } = UseDebugHistory({
    skillNo: skillNoOrComponentNo,
    setFormData
  })

  useImperativeHandle(ref, () => ({
    setFormData
  }))

  const onFinish = async () => {
    setDebugContent("")

    form.validateFields().then((values) => {
      try {
        if (save) {
          save((AICreateParams = {}) => {
            if (isAICreate) {
              let params = {}
              try {
                params = JSON.parse(values["$apiInputParam"])
              } catch (error) {
                console.log("error:", error)
              }

              debugAIGeneratingSkill(
                { ...params, ...AICreateParams },
                {
                  onSuccess: (e) => {
                    if (e.success === true) {
                      setDebugContent(`${e.data?.log}`)
                    } else {
                      setDebugContent(e?.message)
                    }
                    queryClient.invalidateQueries([QUERY_KEYS.LATEST_DEFINITION])
                  }
                }
              )
            } else {
              //attributeName
              const hasValidValues =
                values && Object.keys(values).some((key) => key && values[key] !== undefined)

              console.log("debugContent=>>>>>>", save, isAICreate)
              handleDebug(hasValidValues ? values : {})
            }
          }, true)
        } else {
          //attributeName
          const hasValidValues =
            values && Object.keys(values).some((key) => key && values[key] !== undefined)
          handleDebug(hasValidValues ? values : {})
        }
      } catch (error) {
        console.log("Validate failed:", error)
      } finally {
        const theSaveData = {
          ...values,
          __time: new Date().getTime()
        }
        const historyData = debugData
        // 如果还没有任何数据，那么直接写进去
        if (!historyData[skillNoOrComponentNo]) {
          setDebugData({ [skillNoOrComponentNo]: [theSaveData] })
        } else {
          if (historyData[skillNoOrComponentNo].length > 4) {
            historyData[skillNoOrComponentNo].pop()
          }
          historyData[skillNoOrComponentNo].unshift(theSaveData)
          setDebugData({
            [skillNoOrComponentNo]: historyData[skillNoOrComponentNo]
          })
        }
      }
    })
  }

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [debugContent])

  const show =
    (formData && formData.filter((item) => item.controlType)?.length !== 0) || forceShowDebugButton

  if (!showDebuggerPanel) {
    return null
  }

  const closeDebugPanel = () => {
    if (onClose) {
      onClose()
    } else {
      changeComponentAndDebugPanel({
        ...componentAndDebugPanel,
        showDebugPanel: false,
        width: 600
      })
    }
  }

  const formContainerStyle = {}
  const commonContentStyle = {}
  if (preview) {
    formContainerStyle.height = "auto"
    formContainerStyle.padding = 0
    commonContentStyle.width = "100%"
    commonContentStyle.paddingTop = 0
  }

  return (
    <div className={`common-content ${className}`} style={commonContentStyle}>
      {!skillNo && componentNo ? (
        <div className={`common-content-header`} style={{ borderBottom: "1px solid #E5E7EB" }}>
          <span>组件调试</span>
          <div className="common-content-extra color-[#475467]">
            <DebugPopoverButton />
            <Iconfont type="icon-tuodong-shanchu" className="ml-4" onClick={closeDebugPanel} />
          </div>
        </div>
      ) : (
        isProcess && (
          <div className={`common-content-header`} style={{ borderBottom: "1px solid #E5E7EB" }}>
            <span>流程调试</span>
            <div className="common-content-extra">
              <DebugPopoverButton />
              <Iconfont type="icon-tuodong-shanchu" className="ml-4" onClick={closeDebugPanel} />
            </div>
          </div>
        )
      )}

      <div
        className="dynamicFormComponent-wrapper common-content-container"
        style={formContainerStyle}
      >
        <Form
          onFinish={onFinish}
          form={form}
          disabled={isDisabled}
          layout="vertical"
          labelCol={{ span: 24 }}
        >
          {show &&
            !isJSONDebug &&
            formData.map((item) => {
              const {
                controlType,
                attributeName,
                title,
                tip,
                placeholder,
                options,
                required = false
              } = item

              return (
                <Form.Item
                  rules={[
                    {
                      required: required,
                      message: "请完成必填项"
                    }
                  ]}
                  className="dynamicFormComponent-item"
                  key={attributeName}
                  name={attributeName}
                  tooltip={
                    tip
                      ? {
                          icon: <InfoIcon />,
                          title: tip
                        }
                      : ""
                  }
                  label={
                    <Tooltip placement="left" title={title}>
                      {title}
                    </Tooltip>
                  }
                  initialValue={controlType === "switch" ? false : undefined}
                >
                  <DynamicFormItem
                    name={attributeName}
                    controlType={controlType}
                    placeholder={placeholder}
                    options={options}
                    onUploadStatusChange={setIsUploading}
                  />
                </Form.Item>
              )
            })}

          {isJSONDebug && (
            <Form.Item
              name={isProcess ? "$apiInputParam" : "$webHookParams"}
              rules={[
                {
                  required: true,
                  message: "请完成必填项"
                }
              ]}
              initialValue="{}"
            >
              <JSONValidator />
            </Form.Item>
          )}

          <div
            className="debug-content-wrapper"
            style={
              {
                // maxHeight: !isProcess && 600
              }
            }
          >
            <MarkdownRenderer content={debugContent} />
            {loading && (
              <div
                style={{
                  margin: "20px auto",
                  display: "flex",
                  justifyContent: "center"
                }}
              >
                <Spin tip="正在玩命加载中……" spinning={loading} />
              </div>
            )}
            <div ref={loadingRef} />
          </div>
        </Form>

        {!preview && (
          <div className="flex justify-end absolute common-content-footer">
            <div className="mr-4">
              <Button type="default" className="btn-cancel" onClick={closeDebugPanel}>
                取消
              </Button>
            </div>
            <div>
              {isJSONDebug && (
                <Button
                  type="primary"
                  onClick={onFinish}
                  loading={loading}
                  disabled={(!componentNo && !isProcess) || isLocked || isUploading}
                >
                  调试
                </Button>
              )}
              {/* show && !preview && !isJSONDebug */}
              {!preview && !isJSONDebug && (
                <Button
                  type="primary"
                  onClick={onFinish}
                  loading={loading}
                  disabled={(!componentNo && !isProcess) || isLocked || isUploading}
                >
                  调试
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
      <TopDownButton
        target={document.querySelector(".dynamicFormComponent-wrapper.common-content-container")}
      />
    </div>
  )
}
const DynamicForm = forwardRef(DynamicFormComponent)

export default React.memo(DynamicForm)
