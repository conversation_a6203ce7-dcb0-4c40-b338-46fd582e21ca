import React, { useState } from "react"
import { Form, Select, Row, Col, Button } from "antd"
import { AddIcon, DeleteIcon, InfoIcon } from "@/components/FormIcon"
import { PlusOutlined } from "@ant-design/icons"
import VariableTextArea from "./VariableTextArea"
import { PromptTips } from "@/constants/tips"
import AIOptimize from "@/components/AIOptimize"
import CustomDivider from "@/components/CustomDivider"
import GlobalVariableSelect from "@/components/GlobalVariableSelect"

const QuickMode = ({
  form,
  globalData,
  skillFlowData,
  contentValue,
  onMouseBlur,
  isDisabled,
  multiModalTypeList = [],
  isOnlyMultiModal = false
}) => {
  const hasMultiModal = multiModalTypeList && multiModalTypeList.length > 0

  return (
    <div>
      {/* 只有多模态模型才显示输入配置项 */}
      {hasMultiModal && (
        <>
          <CustomDivider showTopLine={false}>输入项配置</CustomDivider>
          <Form.List name="quickModeInputs" initialValue={[{}]} className="!-mt-2">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} className="flex mb-2">
                    <Col span={5}>
                      <Form.Item {...restField} name={[name, "modalType"]}>
                        <Select placeholder="模态类型" onChange={onMouseBlur}>
                          {multiModalTypeList
                            ?.filter((m) => m.code !== "TEXT")
                            .map((modalTypeItem) => (
                              <Select.Option key={modalTypeItem.code} value={modalTypeItem.code}>
                                {modalTypeItem.name}
                              </Select.Option>
                            ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col className="flex-1">
                      <GlobalVariableSelect
                        formName={[name, "variable"]}
                        multiple={false}
                        required={false}
                        onChange={onMouseBlur}
                        placeholder="选择变量"
                      />
                    </Col>
                    <Col className="mt-[5px]">
                      <DeleteIcon
                        onClick={() => {
                          remove(name)
                          onMouseBlur()
                        }}
                      />
                    </Col>
                  </Row>
                ))}
                <Form.Item className="">
                  <AddIcon text="添加新模态" onClick={() => add()} />
                </Form.Item>
              </>
            )}
          </Form.List>
        </>
      )}

      {/* 提示词部分 */}
      <div className="p-2 pb-1 rounded-md bg-gray-100 mt-0">
        <Form.Item
          className="global-tips"
          name="content"
          label={
            <div className="flex items-center">
              <span className="mr-2">提示词</span>
              {skillFlowData?.skillNo && (
                <AIOptimize
                  originalPrompt={contentValue}
                  intelligentAgentType="SKILL"
                  intelligentAgentNo={skillFlowData.skillNo}
                  onSubmit={({ type, content }) => {
                    if (type === "agent") {
                      form.setFieldsValue({ content })
                    }
                  }}
                />
              )}
            </div>
          }
          rules={[
            {
              required: true,
              message: "请输入提示词"
            }
          ]}
          help={"输入$调用过程变量"}
          labelCol={{
            span: 24,
            offset: 0.3
          }}
          tooltip={{
            icon: <InfoIcon />,
            title: PromptTips,
            overlayStyle: { maxWidth: 400 }
          }}
        >
          <VariableTextArea
            variables={globalData}
            disabled={isDisabled}
            onMouseBlur={onMouseBlur}
            onChange={onMouseBlur}
            placeholder="请输入提示词内容..."
            miniInputStyle={{
              border: "none",
              boxShadow: "none",
              backgroundColor: "#f4f4f4"
            }}
          />
        </Form.Item>
      </div>
    </div>
  )
}

export default QuickMode
