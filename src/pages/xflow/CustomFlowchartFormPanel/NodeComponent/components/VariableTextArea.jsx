import React, { useState } from "react"
import { List, Popover, Input, Tooltip } from "antd"
import { ZoomInOutlined } from "@ant-design/icons"
import LargeEditorModal from "./LargeEditorModal"
import "./variableTextArea.scss"
import useVariableSuggestions from "@/pages/xflow/hooks/useVariableSuggestions"
import Iconfont from "@/components/Icon"

const VariableTextArea = ({
  variables,
  value: propValue,
  onChange,
  onMouseBlur,
  disabled = false,
  disableSuggestion = false,
  style = {},
  miniInputStyle = {},
  miniInputProps = {},
  largeInputProps = {},
  placeholder,
  isModal = false
}) => {
  const {
    textAreaRef,
    showSuggestion,
    position,
    filteredVariables,
    highlightedIndex,
    handleInputChange,
    handleCompositionStart,
    handleCompositionEnd,
    handleKeyDown,
    handleSelect
  } = useVariableSuggestions(variables, onChange) // 使用自定义Hook
  const [isLargeEditorOpen, setLargeEditorOpen] = useState(false)
  const listItemRefs = React.useRef([])

  const scrollToHighlightedItem = () => {
    const ref = listItemRefs.current[highlightedIndex]
    if (ref) {
      ref.scrollIntoView({
        block: "nearest"
      })
    }
  }

  React.useEffect(() => {
    scrollToHighlightedItem()
  }, [highlightedIndex])

  return (
    <div style={{ position: "relative", ...style }} className="variable-text-area">
      <Input.TextArea
        onKeyDown={handleKeyDown}
        autoSize={miniInputStyle.height ? false : { minRows: 9, maxRows: 100 }}
        value={propValue}
        onChange={handleInputChange}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        onBlur={onMouseBlur}
        placeholder={placeholder}
        ref={textAreaRef}
        disabled={disabled}
        style={{
          width: "100%",
          height: "100px",
          ...style,
          ...miniInputStyle
        }}
        {...miniInputProps}
      />
      {!disableSuggestion && showSuggestion && (
        <Popover
          content={
            <List
              style={{
                height: 300,
                overflowY: "scroll"
              }}
              dataSource={filteredVariables}
              renderItem={(variable, index) => (
                <List.Item
                  ref={(el) => (listItemRefs.current[index] = el)}
                  className="cursor-pointer"
                  onClick={() => handleSelect(variable)}
                  style={index === highlightedIndex ? { backgroundColor: "#f5f5f5" } : {}}
                >
                  {`${variable.valueExpression} ${
                    variable.description ? `(${variable.description})` : ""
                  }`}
                </List.Item>
              )}
            />
          }
          visible={showSuggestion}
          placement="bottomLeft"
          getPopupContainer={() => document.body}
          overlayStyle={{
            position: "absolute",
            top: isModal ? position.top + 350 : position.top,
            left: position.left
          }}
        >
          <div />
        </Popover>
      )}
      <Tooltip title="放大编辑框">
        <Iconfont
          type={"icon-a-expand"}
          onClick={() => setLargeEditorOpen(true)}
          className="large-icon"
        />
      </Tooltip>

      <LargeEditorModal
        isOpen={isLargeEditorOpen}
        onClose={() => setLargeEditorOpen(false)}
        initialValue={propValue}
        placeholder={placeholder}
        onSubmit={(newValue) => {
          onChange({ target: { value: newValue } })
        }}
        variables={variables}
        disableSuggestion={disableSuggestion}
        largeInputProps={largeInputProps}
      />
    </div>
  )
}

export default VariableTextArea
