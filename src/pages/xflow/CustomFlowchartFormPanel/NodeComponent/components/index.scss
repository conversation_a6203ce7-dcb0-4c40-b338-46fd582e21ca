.debug-content-wrapper {
  // height: 700px;
  // overflow: auto;

  p {
    margin-bottom: 6px;
    margin-top: 6px;
    font-size: 14px;
  }

  pre {
    margin-top: 6px;
    margin-bottom: 6px;
    font-size: 14px;

  }
}

.dynamicFormComponent-wrapper {
  .dynamicFormComponent-item {
    label {
      display: inline-block;
      // max-width: 180px;
      // white-space: normal;
      text-overflow: ellipsis;
      overflow: hidden;
      transform: translateY(6px);
    }
  }
  .debug-output-separate {
    display: flex; 
    align-items: center; 
    justify-content: center;
    margin-top: 65px;
    margin-bottom: 42px;
    .text {
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      background-color: white; 
      padding: 0 10px;
    }
    .line {
      flex: 1; 
      height: 1px;
      background-color: #F0F0F0;
    }
  }
}

.overflow-y-auto-80vh {
  overflow-y: auto !important;
  height: 80vh !important;
}