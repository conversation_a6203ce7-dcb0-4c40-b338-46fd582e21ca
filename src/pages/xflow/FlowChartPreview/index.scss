.flow-chat-preview-wrapper {
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(100%);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .flow-item {
    animation-name: slideUp;
    animation-duration: 500ms;
    animation-fill-mode: both;

    // 移动到这里
    .chart-img {
      transition: transform 300ms;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      border: 2px solid rgba(255, 255, 255, 0.3);

      &:hover {
        transform: translateY(-5px) scale(1.03);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        // border-color: rgba(255, 255, 255, 0.7);
      }
    }

  }

  .chatName {
    text-align: center;
    color: #333333;
    font-size: 13px;
    line-height: 18px;
    // padding-top: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}