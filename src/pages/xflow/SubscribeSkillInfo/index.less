.subscribe-skill-info {
  // position: absolute;
  // top: 0;
  // left: 0;
  display: flex;
  align-items: flex-start;
  // padding: 26px 22px;
  padding: 10px 16px;
  padding-bottom: 0px;
  background-color: #fff;
  // box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  border-radius: 18px;
  .back-btn {
    height: 32px;
    margin-right: 18px;
  }
  .pt-32 {
    padding-top: 32px;
  }
  .pt-54 {
    padding-top: 54px;
  }
  .base-info {
    flex: 1;
    .title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 32px;
      font-size: 20px;
      font-weight: 700;
    }
    .field-item {
      line-height: 22px;
      font-size: 12px;
      color: #555;
      margin-top: -5px;
      padding-bottom: 5px;
    }
    .status {
      display: inline-block;
      line-height: 22px;
      padding: 0 14px;
      border: 1px solid #7f56d9;
      border-radius: 12px;
      color: #7f56d9;
    }
  }
}
