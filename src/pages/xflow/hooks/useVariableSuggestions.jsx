import { useState, useRef, useEffect } from "react"
import getCaretCoordinates from "textarea-caret"

const useVariableSuggestions = (variables, onChange) => {
  const [showSuggestion, setShowSuggestion] = useState(false)
  const [position, setPosition] = useState({ top: 0, left: 0 })
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const [filteredVariables, setFilteredVariables] = useState(variables)
  const textAreaRef = useRef(null)
  const [isComposing, setIsComposing] = useState(false)
  const [caretPosition, setCaretPosition] = useState(0)

  const handleVariableSuggestion = (inputValue, currentCaretPosition) => {
    if (inputValue[currentCaretPosition - 1] === "$") {
      const rect = textAreaRef.current.resizableTextArea.textArea.getBoundingClientRect()
      const coordinates = getCaretCoordinates(
        textAreaRef.current.resizableTextArea.textArea,
        currentCaretPosition
      )

      console.log(
        rect.top,
        coordinates.top,
        window.scrollY,
        rect.top + coordinates.top + window.scrollY - 330
      )

      setPosition({
        top: rect.top + coordinates.top + window.scrollY - 330,
        left: rect.left + coordinates.left + window.scrollX - 20
      })
      setShowSuggestion(true)
      setFilteredVariables(variables)
      setHighlightedIndex(0)
    } else if (showSuggestion) {
      const prefix = inputValue.slice(
        inputValue.lastIndexOf("$", currentCaretPosition - 1),
        currentCaretPosition
      )

      if (!prefix.includes("$")) {
        setShowSuggestion(false)
      } else {
        const regex = new RegExp(`^${prefix.slice(1)}`, "i")
        const newFilteredVariables = variables.filter((variable) =>
          regex.test(variable.valueExpression)
        )

        setFilteredVariables(newFilteredVariables)
        setHighlightedIndex(0)

        if (newFilteredVariables.length === 0) {
          setShowSuggestion(false)
        }
      }
    } else {
      setHighlightedIndex(-1)
      setShowSuggestion(false)
    }
  }

  const handleInputChange = (event) => {
    const inputValue = event.target.value

    if (!isComposing) {
      const currentCaretPosition = textAreaRef.current.resizableTextArea.textArea.selectionEnd //当前光标位置
      handleVariableSuggestion(inputValue, currentCaretPosition)
    }

    if (onChange) {
      onChange(event)
    }
  }

  const handleCompositionStart = () => {
    setIsComposing(true)
  }

  const handleCompositionEnd = (event) => {
    setIsComposing(false)
    const inputValue = event.target.value
    const currentCaretPosition = textAreaRef.current.resizableTextArea.textArea.selectionEnd
    handleVariableSuggestion(inputValue, currentCaretPosition)
    if (onChange) {
      onChange(event)
    }
  }

  const handleSelect = (variable) => {
    const inputValue = textAreaRef.current.resizableTextArea.textArea.value
    const currentCaretPosition = textAreaRef.current.resizableTextArea.textArea.selectionEnd
    const lastDollarIndex = inputValue.lastIndexOf("$", currentCaretPosition - 1)

    if (lastDollarIndex === -1 || lastDollarIndex >= currentCaretPosition) {
      return
    }

    const replacement = "${" + variable.valueExpression + "}"

    const newValue =
      inputValue.substring(0, lastDollarIndex) +
      replacement +
      inputValue.substring(currentCaretPosition)

    const newCaretPosition = lastDollarIndex + replacement.length
    setCaretPosition(newCaretPosition)
    setTimeout(() => {
      if (
        textAreaRef.current &&
        textAreaRef.current.resizableTextArea &&
        textAreaRef.current.resizableTextArea.textArea
      ) {
        textAreaRef.current.resizableTextArea.textArea.focus()
        textAreaRef.current.resizableTextArea.textArea.selectionStart = newCaretPosition
        textAreaRef.current.resizableTextArea.textArea.selectionEnd = newCaretPosition
      }
    }, 0)

    setShowSuggestion(false)
    setHighlightedIndex(-1)

    if (onChange) {
      onChange({ target: { value: newValue } }) // 这里模拟了一个event
    }
  }

  const handleKeyDown = (event) => {
    if (!showSuggestion) return

    switch (event.keyCode) {
      case 27: // Esc key
        setShowSuggestion(false)
        event.preventDefault()
        event.stopPropagation()
        break
      case 38: // Up arrow key
        event.preventDefault()
        if (highlightedIndex <= 0) {
          setHighlightedIndex(filteredVariables.length - 1)
        } else {
          setHighlightedIndex(highlightedIndex - 1)
        }
        break
      case 40: // Down arrow key
        event.preventDefault()
        if (highlightedIndex >= filteredVariables.length - 1) {
          setHighlightedIndex(0)
        } else {
          setHighlightedIndex(highlightedIndex + 1)
        }
        break
      case 13: // Enter key
        event.preventDefault()
        if (
          showSuggestion &&
          highlightedIndex >= 0 &&
          highlightedIndex < filteredVariables.length
        ) {
          handleSelect(filteredVariables[highlightedIndex])
        }
        break
      default:
        break
    }
  }

  return {
    textAreaRef,
    showSuggestion,
    setShowSuggestion,
    position,
    filteredVariables,
    highlightedIndex,
    handleInputChange,
    handleCompositionStart,
    handleCompositionEnd,
    handleKeyDown,
    handleSelect
  }
}

export default useVariableSuggestions
