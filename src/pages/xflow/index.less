.xflow-canvas-root {
  // padding-left: 140px;
  font-family:
    Monaco,
    Courier New,
    monospace !important;
}

.xflow-json-schema-form {
  // padding: 20px;
  border: none;
  background: none;
}

.horizontal.xflow-toolbar-root {
  border-bottom: 1px solid #ccc;
}

.flowchart-extension-container .xflow-node-panel-custom {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-gap: 16px;
}

// .tool-bar-wrapper {
//   position: absolute;
//   top: -42px;
// }

.flowchart-extension-container.xflow-app-workspace {
  overflow: visible;
}

.flow-user-custom-clz {
  height: calc(100vh - 50px);
}

.global-drawer {
  .ant-drawer-content-wrapper {
    box-shadow: none !important;
    border-right: 1px solid #d9d9d9;
    border-left: 1px solid #d9d9d9;
    top: 0px;
  }
  .const-test-drawer {
    top: 16px;
  }
}

.xflow-x6-canvas {
  overflow: hidden;
  max-width: 100%;
}

.x6-widget-minimap-viewport {
  width: 200px !important;
  height: 180px !important;
}

.x6-graph-grid {
  background-image: none !important;
}
.node-class-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  .node-class-icons {
    font-size: 18px;
    margin-right: 7px;
    font-weight: 500;
  }
}
.flow-class-panel {
  overflow-y: auto;
  .ant-collapse-header {
    background: #f4f6f9;
    border: 0;
    padding-right: 30px !important;
  }
  .ant-collapse-content {
    border: 0;
  }
  .ant-collapse-expand-icon {
    color: #333;
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.skill-detail-unopened {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 40px;
  font-size: 18px;
  color: #777;
}
