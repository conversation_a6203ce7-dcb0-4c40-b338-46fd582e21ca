.ant-collapse-header {
  align-items: center !important;
  padding: 5px 15px !important;
}
.custom-item {
  box-sizing: border-box;
  border: 1px solid #999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  border-left: 4px solid #999;
  box-sizing: border-box;
  position: relative;
  padding-top: 25px;
  padding-bottom: 10px;

}
.custom-node-content {
}
.custom-node-label {
  font-size: 12px;
}
.custom-node-circle-content {
  border-radius: 20px;
  padding-bottom: 0;
  border-width: 1px;
}
.custom-node-condition-content {
  background: url("../../../assets/img/rectangle.png") 0 0 no-repeat;
  background-size: 100% 100%;
  border: 0;
  padding-bottom: 0;
  // height: 35px !important;
}
.custom-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 10px;
  line-height: 25px;
  padding: 0 5px;
  box-sizing: border-box;
  span[role="img"] {
    font-size: 16px;
    font-weight: bold;
    margin-right: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.xflow-node-panel-custom {
  .custom-icon {
    display: none;
  }
  .custom-item {
    padding-top: 0;
    padding-bottom: 0;
  }
}
body > svg {
  path {
    fill: unset;
  }
}
