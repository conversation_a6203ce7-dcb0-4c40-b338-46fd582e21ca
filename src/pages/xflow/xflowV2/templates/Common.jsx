// 输入类型
import { Tooltip } from "antd"

export const CommonNode = (props) => {
  const { topDescription, subDescription, maxTopLines = 1, maxSubLines = 1 } = props

  // 处理 title，支持函数式和字符串
  const renderTitle = (titleValue) => {
    if (typeof titleValue === "function") {
      return titleValue()
    }
    return titleValue
  }

  // console.log("topDescription", topDescription?.description())
  return (
    <>
      <div className="h-[44px] w-[100%] bg-[#F5F7FA] rounded-[4px] p-[4px]">
        <div className="text-[#525866] text-[12px] font-[400]">
          {renderTitle(topDescription?.title) || "未知组件"}
        </div>
        {topDescription?.description && (
          <Tooltip title={topDescription?.description()} placement="right">
            <div
              className={`text-[#0E121B] font-[400] w-[100%] text-[12px] overflow-hidden mt-[2px] ${
                maxTopLines === 1
                  ? "text-ellipsis whitespace-nowrap"
                  : `line-clamp-${maxTopLines} display-webkit-box webkit-line-clamp-${maxTopLines} webkit-box-orient-vertical`
              }`}
            >
              {topDescription?.description()}
            </div>
          </Tooltip>
        )}
      </div>

      {subDescription && (
        <div
          className={`${maxSubLines >= 10 ? "h-[206px]  overflow-auto" : "h-[44px]"} w-[100%] bg-[#F5F7FA] rounded-[4px] p-[4px] -mt-[10px]`}
        >
          <div className="text-[#525866] text-[12px] font-[400]">
            {renderTitle(subDescription?.title) || "未知组件"}
          </div>
          {topDescription?.description && (
            <Tooltip title={subDescription?.description()} placement="right">
              <div
                className={`text-[#0E121B] font-[400] w-[100%] text-[12px] overflow-hidden mt-[2px] ${
                  maxSubLines === 1
                    ? "text-ellipsis whitespace-nowrap"
                    : `line-clamp-${maxSubLines} display-webkit-box webkit-line-clamp-${maxSubLines} webkit-box-orient-vertical`
                }`}
              >
                {subDescription?.description()}
              </div>
            </Tooltip>
          )}
        </div>
      )}
    </>
  )
}
