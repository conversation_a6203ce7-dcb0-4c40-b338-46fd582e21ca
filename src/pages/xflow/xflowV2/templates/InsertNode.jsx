import { CommonNode } from "./Common"
import { Tag } from "antd"

// @ts-ignore
import icon1 from "../images/icon-1.png"
// @ts-ignore
import icon2 from "../images/icon-2.png"
// @ts-ignore
import icon3 from "../images/icon-3.png"
// @ts-ignore
import icon4 from "../images/icon-4.png"
// @ts-ignore
import icon5 from "../images/icon-5.png"
import { pluginTools } from "../untils"

const matchQueryRange = (queryRange = [], selectedCatalogs = []) => {
  if (!queryRange?.length || !selectedCatalogs?.length) return "--"

  const matchedNames = []

  // 遍历已选择的目录编号
  selectedCatalogs.forEach((catalogNo) => {
    // 在queryRange中查找匹配的目录
    for (const category of queryRange) {
      // 在 category.children 中查找匹配项
      const found = category.children?.find((item) => item.catalogNo === catalogNo)
      if (found) {
        matchedNames.push(`${category.catalogName}-${found.catalogName}`)
        break
      }
    }
  })

  return matchedNames.join("；") || "--"
}

const findToolPath = (tools, toolCode) => {
  // 遍历查找匹配的工具
  const findTool = (tools, parentNames = []) => {
    for (const item of tools) {
      // 如果是插件层级
      if (item.pluginCode) {
        const foundInChildren = item.children?.find((child) => child.toolCode === toolCode)
        if (foundInChildren) {
          return [...parentNames, item.title, foundInChildren.toolName].join(" / ")
        }
        // 如果有 tools 数组
        const foundInTools = item.tools?.find((tool) => tool.toolCode === toolCode)
        if (foundInTools) {
          return [...parentNames, item.title, foundInTools.toolName].join(" / ")
        }
      }
      // 如果是分类层级且有子项
      if (item.children?.length) {
        const found = findTool(item.children, [...parentNames, item.title])
        if (found) return found
      }
    }
    return null
  }

  return findTool(tools) || "--"
}

export const InsertNode = (
  name,
  data,
  queryRange,
  availableSkills,
  availablePluginTools,
  toolOptions,
  dataSetList,
  currentInfos
) => {
  // queryRange FAQ 查询范围 知识库目录
  const currentNode = currentInfos?.skillComponentDefinitions?.find(
    (item) => item.nodeId === data?.id
  )
  // console.log(
  // "currentInfos888888",
  // currentInfos,
  // data,
  // currentNode,
  // queryRange,
  // availableSkills
  // pluginTools(availablePluginTools),
  // toolOptions,
  // dataSetList,
  // availablePluginTools
  // )
  switch (name) {
    case "begin-node": // 输入
      return (
        <CommonNode
          topDescription={{
            title: "输入描述",
            description: () => {
              if (
                currentInfos?.releaseStatus === "released" &&
                !currentInfos?.formControlDefinitions?.length
              ) {
                return "msg"
              }
              return `包含字段：${
                currentInfos?.formControlDefinitions
                  ?.map((item) => item.attributeName)
                  .join("；") || "--"
              }`
            }
          }}
          subDescription={null}
        />
      )
    case "end-node": //输出
      return (
        <CommonNode
          topDescription={{
            title: "输出方式",
            description: () => {
              return `${currentNode?.extraInfo?.stream ? "流式输出" : "普通输出" || "--"}`
            }
          }}
          subDescription={{
            title: "解析方式",
            description: () => {
              return `${currentNode?.outputType === "JSON" ? "JSON" : currentNode?.outputType === "MARKDOWN" ? "模板输出" : "文本" || "--"}`
            }
          }}
        />
      )
    case "condition-node": //条件
      return (
        <CommonNode
          topDescription={{
            title: "条件表达式",
            description: () => {
              return `${data?.expressions?.map((item) => item.expression).join("；") || "--"}`
            }
          }}
          subDescription={null}
        />
      )
    case "prompt-node": //大模型-prompt
      return (
        <CommonNode
          topDescription={{
            title: "模型名称",
            description: () => {
              return `${currentNode?.extraInfo?.modelType || "--"}`
            }
          }}
          maxSubLines={10}
          subDescription={{
            title: () => {
              const promptMode = currentNode?.extraInfo?.promptMode
              let modeText = "快速模式"
              let tagColor = "blue"

              if (promptMode === "PROFESSIONAL") {
                modeText = "专业模式"
                tagColor = "green"
              } else if (promptMode === "CODE") {
                modeText = "代码模式"
                tagColor = "purple"
              }

              return (
                <span>
                  提示词 <Tag color={tagColor}>{modeText}</Tag>
                </span>
              )
            },
            description: () => {
              const promptMode = currentNode?.extraInfo?.promptMode

              if (promptMode === "PROFESSIONAL") {
                // 专业模式：查找 role 为 "system" 的消息内容
                const professionalContents = currentNode?.extraInfo?.professionalContents
                if (Array.isArray(professionalContents)) {
                  const systemMessage = professionalContents.find((msg) => msg.role === "system")
                  return systemMessage?.content || "--"
                }
                return "--"
              } else if (promptMode === "CODE") {
                // 代码模式：显示 script 内容
                const script = currentNode?.extraInfo?.script
                if (!script) return "--"

                return script
              } else {
                // 快速模式：显示原来的 content
                return `${currentNode?.extraInfo?.content || "--"}`
              }
            }
          }}
        />
      )
    case "search-node": //查询 FQA
      return (
        <CommonNode
          topDescription={{
            title: "查询范围",
            description: () => {
              return matchQueryRange(queryRange, currentNode?.extraInfo?.catalogNos)
            }
          }}
          subDescription={{
            title: "查询数量",
            description: () => {
              return `${currentNode?.extraInfo?.size || "--"}`
            }
          }}
        />
      )
    case "document-search-node": //查询文档
      return (
        <CommonNode
          topDescription={{
            title: "查询范围",
            description: () => {
              return matchQueryRange(queryRange, currentNode?.extraInfo?.catalogNos)
            }
          }}
          subDescription={{
            title: "查询数量",
            description: () => {
              return `${currentNode?.extraInfo?.size || "--"}`
            }
          }}
        />
      )
    case "query-dataset-node": //查询数据集
      return (
        <CommonNode
          topDescription={{
            title: "查询范围",
            description: () => {
              // 获取结构编号
              const structureNo = currentNode?.extraInfo?.structureNo
              // 从 dataSetList 中查找匹配的数据集
              const matchedDataSet = dataSetList?.find((item) => item.value === structureNo)
              return matchedDataSet?.label || "--"
            }
          }}
          subDescription={{
            title: "查询数量",
            description: () => {
              return `${currentNode?.extraInfo?.size || "--"}`
            }
          }}
        />
      )

    case "skill-node": // 调用技能
      return (
        <CommonNode
          topDescription={{
            title: "调用技能",
            description: () => {
              // 从 availableSkills 中查找匹配的技能
              const skillNo = currentNode?.extraInfo?.skillNo
              const allSkills = [
                ...(availableSkills?.subscribedSkills || []),
                ...(availableSkills?.selfSkills || [])
              ]
              const matchedSkill = allSkills.find((skill) => skill.skillNo === skillNo)
              return matchedSkill?.skillName || "--"
            }
          }}
          subDescription={null}
        />
      )
    case "plugin-node": // 调用工具
      return (
        <CommonNode
          topDescription={{
            title: "调用工具",
            description: () => {
              const toolCode = currentNode?.extraInfo?.toolCode
              return findToolPath(pluginTools(availablePluginTools), toolCode)
            }
          }}
          subDescription={null}
        />
      )
    case "tool-node": // 工具-Toolkit
      return (
        <CommonNode
          topDescription={{
            title: "工具类型",
            description: () => {
              // 根据 toolCode 匹配工具名称
              const toolCode = currentNode?.extraInfo?.toolCode
              const matchedTool = toolOptions?.find((tool) => tool.code === toolCode)
              return matchedTool?.name || "--"
            }
          }}
          subDescription={null}
        />
      )

    case "webhook-node": //工具-Webhook
      return (
        <CommonNode
          topDescription={{
            title: "调用地址",
            description: () => {
              return `${currentNode?.extraInfo?.callUrl || "--"}`
            }
          }}
          subDescription={{
            title: "调用方式",
            description: () => {
              return `${currentNode?.extraInfo?.httpMethodName || "--"}`
            }
          }}
        />
      )
    case "script-node": //工具-Script
      return (
        <CommonNode
          topDescription={{
            title: "脚本类型",
            description: () => {
              return `${currentNode?.extraInfo?.scriptType || "--"}`
            }
          }}
          maxSubLines={10}
          subDescription={{
            title: "脚本内容",
            description: () => {
              return `${currentNode?.extraInfo?.script || "--"}`
            }
          }}
        />
      )

    case "identify-language-node": //工具-识别语种
      return (
        <CommonNode
          topDescription={{
            title: "输入参数",
            description: () => {
              // 获取变量定义列表
              const variables = currentNode?.variableDefinitions || []
              return (
                variables
                  .map((variable) => {
                    // 如果有描述，则添加括号说明
                    const description = variable.description ? ` (${variable.description})` : ""
                    return `${variable.valueExpression}${description}`
                  })
                  .join("；") || "--"
              )
            }
          }}
          subDescription={{
            title: "输出变量名",
            description: () => {
              return `${currentNode?.outputName || "--"}`
            }
          }}
        />
      )
    case "img2-text-node": //工具-OCR
      return (
        <CommonNode
          topDescription={{
            title: "输入参数",
            description: () => {
              // 获取变量定义列表
              const variables = currentNode?.variableDefinitions || []
              return (
                variables
                  .map((variable) => {
                    // 如果有描述，则添加括号说明
                    const description = variable.description ? ` (${variable.description})` : ""
                    return `${variable.valueExpression}${description}`
                  })
                  .join("；") || "--"
              )
            }
          }}
          subDescription={{
            title: "输出变量名",
            description: () => {
              return `${currentNode?.outputName || "--"}`
            }
          }}
        />
      )
    case "search-tool-node": //工具-搜索
      return (
        <CommonNode
          topDescription={{
            title: "输入参数",
            description: () => {
              // 获取变量定义列表
              const variables = currentNode?.variableDefinitions || []
              return (
                variables
                  .map((variable) => {
                    // 如果有描述，则添加括号说明
                    const description = variable.description ? ` (${variable.description})` : ""
                    return `${variable.valueExpression}${description}`
                  })
                  .join("；") || "--"
              )
            }
          }}
          subDescription={{
            title: "输出变量名",
            description: () => {
              return `${currentNode?.outputName || "--"}`
            }
          }}
        />
      )
    case "asr-node": //工具-ASR
      return (
        <CommonNode
          topDescription={{
            title: "输入参数",
            description: () => {
              // 获取变量定义列表
              const variables = currentNode?.variableDefinitions || []
              return (
                variables
                  .map((variable) => {
                    // 如果有描述，则添加括号说明
                    const description = variable.description ? ` (${variable.description})` : ""
                    return `${variable.valueExpression}${description}`
                  })
                  .join("；") || "--"
              )
            }
          }}
          subDescription={{
            title: "输出变量名",
            description: () => {
              return `${currentNode?.outputName || "--"}`
            }
          }}
        />
      )
    case "tts-node": //工具-TTS
      return (
        <CommonNode
          topDescription={{
            title: "输入参数",
            description: () => {
              // 获取变量定义列表
              const variables = currentNode?.variableDefinitions || []
              return (
                variables
                  .map((variable) => {
                    // 如果有描述，则添加括号说明
                    const description = variable.description ? ` (${variable.description})` : ""
                    return `${variable.valueExpression}${description}`
                  })
                  .join("；") || "--"
              )
            }
          }}
          subDescription={{
            title: "输出变量名",
            description: () => {
              return `${currentNode?.outputName || "--"}`
            }
          }}
        />
      )
    default:
      return null
  }
}

export const InsertIcon = (data) => {
  switch (data?.name) {
    case "begin-node":
    case "end-node": // 开始-输入-条件
    case "condition-node":
      return icon1

    case "prompt-node":
      return icon2
    case "search-node":
    case "document-search-node":
    case "query-dataset-node":
      return icon3
    case "skill-node":
    case "plugin-node":
      return icon4
    default:
      return icon5
  }
}
