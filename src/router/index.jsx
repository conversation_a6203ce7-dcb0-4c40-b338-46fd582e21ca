import { Suspense, lazy, Component } from "react"
import { createHashRouter, Navigate } from "react-router-dom"
import loadable from "@loadable/component"
import withLoadable from "@/utils/withLoadable"
// 引入Entry框架页面
import { globalConfig } from "@/globalConfig"
import { BotRoutes } from "./robotRoutes"
import MainLayout from "@/components/MainLayout"
import Entry from "@/pages/entry"

import FallBack from "@/components/FallBack"
import UserManageMent from "@/pages/permission/UserManageMent"
import MenuAuthManagement from "@/pages/permission/MenuAuthManagement"
import GroupManageMent from "@/pages/permission/GroupManageMent"
import RoleManageMent from "@/pages/permission/RoleManageMent"
import SkillTemplateManageMent from "@/pages/permission/SkillTemplateManageMent"
import UserFeedback from "@/pages/dataStatistic/userFeedback"
import SecurityAlarm from "@/pages/dataStatistic/securityAlarm"
import DailyReport from "@/pages/dataStatistic/dailyReport"
import CallLogs from "@/pages/dataStatistic/callLogs"
import VoiceRecord from "@/pages/dataStatistic/voiceRecord"
import OptimizeOrder from "@/pages/dataStatistic/optimizeOrder"
import AvailableLimit from "@/pages/availableLimit"
import Workbench from "@/pages/workbench"
import BotConstants from "@/pages/botConstants"
import PluginsManagement from "@/pages/promptEngineering/pluginsManagement"
import PluginsDetail from "@/pages/promptEngineering/pluginsDetail"
import PluginToolList from "@/pages/pluginToolList"
import ModelManage from "@/pages/modelManage"
import { Market } from "@/pages/market"
import { MarketSub } from "@/pages/market-sub"
import CreatePluginTools from "@/pages/createPluginTools"
import ToolsList from "@/pages/tools"
import FileUpload from "@/pages/tools/file/upload"
import Page404 from "@/pages/404"

// const Login = lazy(() => import("@/pages/login"))
import {
  knowledgeExtractionRouters,
  dataManagementRouters,
  knowledgeManagementRouters
} from "./routers/knowledgeProject"
import OnlineStudy from "@/pages/study/online"

// lazy
// import MarkdownPreview from "@/pages/tools/markdownPreview"
const MarkdownPreview = withLoadable(() => import("@/pages/tools/markdownPreview"))
// import KnowledgeSourceTag from "@/pages/knowledgeSourceTag"
const KnowledgeSourceTag = withLoadable(() => import("@/pages/knowledgeSourceTag"))
// import TestSetManagement from "@/pages/testSetManagement"
const TestSetManagement = withLoadable(() => import("@/pages/testSetManagement"))
// import TestSetVariable from "@/pages/testSetManagement/variable"
const TestSetVariable = withLoadable(() => import("@/pages/testSetManagement/variable"))
// import TestSetDataList from "@/pages/testSetManagement/dataList"
const TestSetDataList = withLoadable(() => import("@/pages/testSetManagement/dataList"))
// import BasicSettings from "@/pages/basicSettings"
const BasicSettings = withLoadable(() => import("@/pages/basicSettings"))
// import Robots from "@/pages/robots"
const Robots = withLoadable(() => import("@/pages/robots"))
// import DiagramPreview from "@/pages/tools/diagramPreview"
const DiagramPreview = withLoadable(() => import("@/pages/tools/diagramPreview"))
const SkillEdit = withLoadable(() => import("@/pages/skillEdit"))

const SkillList = withLoadable(() => import("@/pages/skillList"))
const AgentView = withLoadable(() => import("@/pages/agent"))
const AgentDetail = withLoadable(() => import("@/pages/agent/detail"))

const KnowledgeManage = withLoadable(() => import("@/pages/knowledgeManage"))
const ViewStructureKnowledgeDetail = withLoadable(
  () => import("@/pages/addBot/components/ViewStructureKnowledgeDetail")
)
const AgentWorkbench = withLoadable(() => import("@/pages/agentWorkbench"))
const Debug = withLoadable(() => import("@/pages/debug"))
const Voice = withLoadable(() => import("@/pages/voice"))
const Canvas = withLoadable(() => import("@/pages/voice/canvas"))
const Script = withLoadable(() => import("@/pages/voice/script"))
const Timbre = withLoadable(() => import("@/pages/voice/timbre"))
const Event = withLoadable(() => import("@/pages/voice/event"))
const ChatProvider = withLoadable(() => import("../../packages/components/Chat/Provider"))
const ScriptManage = withLoadable(() => import("@/pages/voice/scriptManage"))
export const allRoutes = [
  {
    path: "/",
    element: <Entry />,
    children: [
      ...BotRoutes,
      {
        path: "/debug",
        title: "调试",
        element: (
          <Suspense fallback={<FallBack />}>
            <Debug />
          </Suspense>
        )
      },
      {
        path: "/permission-management",
        title: "权限管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <div />
          </Suspense>
        )
      },
      {
        path: "/menu-auth-management",
        title: "菜单管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <MenuAuthManagement />
          </Suspense>
        )
      },
      {
        path: "/user-management",
        title: "用户管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <UserManageMent />
          </Suspense>
        )
      },
      {
        path: "/role-management",
        title: "角色管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <RoleManageMent />
          </Suspense>
        )
      },
      {
        path: "/group-management",
        title: "标签管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <GroupManageMent />
          </Suspense>
        )
      },
      {
        path: "/template-management",
        title: "模板管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <div />
          </Suspense>
        )
      },
      {
        path: "/model-management",
        title: "模型管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <ModelManage />
          </Suspense>
        )
      },
      {
        path: "/skill-template-management",
        title: "技能模板",
        element: (
          <Suspense fallback={<FallBack />}>
            <SkillTemplateManageMent />
          </Suspense>
        )
      },
      {
        path: "/data-statistics",
        title: "数据统计",
        element: (
          <Suspense fallback={<FallBack />}>
            <div />
          </Suspense>
        )
      },
      {
        path: "/workbenchManageMemu",
        title: "工作台管理",
        element: (
          <Suspense fallback={<FallBack />}>
            <Workbench />
          </Suspense>
        )
      },
      {
        path: "/user-feedback",
        title: "用户反馈",
        element: (
          <Suspense fallback={<FallBack />}>
            <UserFeedback />
          </Suspense>
        )
      },
      {
        path: "/security-alarm",
        title: "安全告警",
        element: (
          <Suspense fallback={<FallBack />}>
            <SecurityAlarm />
          </Suspense>
        )
      },
      {
        path: "/daily-report",
        title: "日常报表",
        element: (
          <Suspense fallback={<FallBack />}>
            <DailyReport />
          </Suspense>
        )
      },
      {
        path: "/call-logs",
        title: "调用日志",
        element: (
          <Suspense fallback={<FallBack />}>
            <CallLogs />
          </Suspense>
        )
      },
      {
        path: "/voice-record",
        title: "语音记录",
        element: (
          <Suspense fallback={<FallBack />}>
            <VoiceRecord />
          </Suspense>
        )
      },
      {
        path: "/optimize-order",
        title: "优化单",
        element: (
          <Suspense fallback={<FallBack />}>
            <OptimizeOrder />
          </Suspense>
        )
      },
      {
        path: "/botConstants",
        title: "编辑机器人",
        element: (
          <Suspense fallback={<FallBack />}>
            <BotConstants />
          </Suspense>
        )
      },
      {
        path: "/available-limit",
        title: "可用性治理",
        element: (
          <Suspense fallback={<FallBack />}>
            <AvailableLimit />
          </Suspense>
        )
      },
      {
        path: "/knowledgeManage",
        element: (
          <Suspense fallback={<FallBack />}>
            <MainLayout>
              <KnowledgeManage />
            </MainLayout>
          </Suspense>
        )
      },

      {
        path: "/",
        element: <Navigate to="/addBotList" />
      },
      {
        path: "*",
        element: <Navigate to="/addBotList" />
      }
    ]
  },
  {
    path: "/editSkill",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <SkillEdit />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/skillList",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <SkillList />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/agent",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout closeGlobalLoadingIndicator={true}>
          <AgentView />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/agent/detail",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout closeGlobalLoadingIndicator={true}>
          <AgentDetail />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/app/bot/constants",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <BotConstants />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/viewStructureKnowledgeDetail",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <ViewStructureKnowledgeDetail />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/agent/workbench",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <AgentWorkbench />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/market",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout closeGlobalLoadingIndicator={true}>
          <Market />
        </MainLayout>
      </Suspense>
    )
  },

  {
    path: "/market-sub",
    element: (
      <Suspense fallback={<FallBack />}>
        <MarketSub />
      </Suspense>
    )
  },
  {
    path: "/plugins-management",
    title: "工具管理",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout closeGlobalLoadingIndicator={true}>
          <PluginsManagement />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/plugins-detail",
    title: "插件详情",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout closeGlobalLoadingIndicator={true}>
          <PluginsDetail />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/plugin/:pluginNo",
    title: "插件工具列表",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <PluginToolList />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/plugin/tools",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <CreatePluginTools />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/knowledge/source/tag",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <KnowledgeSourceTag />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/test/set/",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <TestSetManagement />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/test/variable/",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <TestSetVariable />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/basic/settings",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <BasicSettings />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/robots",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Robots />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/test/set/data/list",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <TestSetDataList />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/study/online",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <OnlineStudy />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Voice />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice/script",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Script />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice/timbre",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Timbre />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice/event",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Event />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice/canvas",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <Canvas />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/voice/scriptManage",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <ScriptManage />
        </MainLayout>
      </Suspense>
    )
  },
  {
    path: "/markdown-preview",
    element: <MarkdownPreview />
  },
  {
    path: "/diagram-preview",
    element: <DiagramPreview />
  },
  {
    path: "/tools",
    element: <ToolsList />
  },
  {
    path: "/tools/upload",
    element: <FileUpload />
  },
  {
    path: "/404",
    element: <Page404 />
  },

  // 对外暴露 iframe 部分
  {
    path: "/chat",
    element: (
      <Suspense fallback={<FallBack />}>
        <MainLayout>
          <ChatProvider />
        </MainLayout>
      </Suspense>
    )
  },
  // 知识萃取
  ...knowledgeExtractionRouters,
  // 数据管理
  ...dataManagementRouters,
  // 知识管理
  ...knowledgeManagementRouters
]

// 全局路由
export const globalRouters = createHashRouter(allRoutes)

// 路由守卫
export function PrivateRoute(props) {
  // 判断localStorage是否有登录用户信息，如果没有则跳转登录页
  return window.localStorage.getItem(globalConfig.SESSION_LOGIN_INFO) ? (
    props.children
  ) : (
    <Navigate to="/addBotList" />
  )
}
